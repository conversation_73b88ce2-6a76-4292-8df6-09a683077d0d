<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitSharePostdivideMapper">

    <sql id="Base_Column_List">
        id,preId,orderNo,projectName,profitTotalPoints,operatorNo,
        investorNo,businessNo,operatorPoints,investorPoints,platformPoints,
        businessPoints,shareStauts,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitSharePostdivide">
        select
        <include refid="Base_Column_List" />
        from profit_share_postdivide
        where  id = #{id}
    </select>

    <select id="selectByShareStatus" resultType="com.jmt.model.profit.ProfitSharePostdivide">
        SELECT
        id,
        preId,
        orderNo,
        projectName,
        profitTotalPoints,
        operatorNo,
        investorNo,
        businessNo,
        operatorPoints,
        investorPoints,
        platformPoints,
        businessPoints,
        shareStauts,
        createTime,
        updateTime,
        isDelete
        FROM profit_share_postdivide
        WHERE isDelete = 0
        AND shareStauts = #{shareStauts}  <!-- 参数名与字段名保持一致 -->
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_share_postdivide
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitSharePostdivide" useGeneratedKeys="true">
        insert into profit_share_postdivide
        ( id,preId,orderNo,projectName,profitTotalPoints,operatorNo,
        investorNo,businessNo,operatorPoints,investorPoints,platformPoints,
        businessPoints,shareStauts,createTime,updateTime,isDelete)
        values (#{id},#{preId},#{orderNo},#{projectName},#{profitTotalPoints},#{operatorNo},
        #{investorNo},#{businessNo},#{operatorPoints},#{investorPoints},#{platformPoints},
        #{businessPoints},#{shareStatus},#{createTime},#{updateTime},#{isDelete})  <!-- 参数名修正 -->
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitSharePostdivide" useGeneratedKeys="true">
        insert into profit_share_postdivide
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="preId != null">preId,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="projectName != null">projectName,</if>
            <if test="profitTotalPoints != null">profitTotalPoints,</if>
            <if test="operatorNo != null">operatorNo,</if>
            <if test="investorNo != null">investorNo,</if>
            <if test="businessNo != null">businessNo,</if>
            <if test="operatorPoints != null">operatorPoints,</if>
            <if test="investorPoints != null">investorPoints,</if>
            <if test="platformPoints != null">platformPoints,</if>
            <if test="businessPoints != null">businessPoints,</if>
            <if test="shareStauts != null">shareStauts,</if>  <!-- 字段名修正 -->
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="preId != null">#{preId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="profitTotalPoints != null">#{profitTotalPoints},</if>
            <if test="operatorNo != null">#{operatorNo},</if>
            <if test="investorNo != null">#{investorNo},</if>
            <if test="businessNo != null">#{businessNo},</if>
            <if test="operatorPoints != null">#{operatorPoints},</if>
            <if test="investorPoints != null">#{investorPoints},</if>
            <if test="platformPoints != null">#{platformPoints},</if>
            <if test="businessPoints != null">#{businessPoints},</if>
            <if test="shareStauts != null">#{shareStauts},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitSharePostdivide">
        update profit_share_postdivide
        <set>
            <if test="preId != null">
                preId = #{preId},
            </if>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="projectName != null">
                projectName = #{projectName},
            </if>
            <if test="profitTotalPoints != null">
                profitTotalPoints = #{profitTotalPoints},
            </if>
            <if test="operatorNo != null">
                operatorNo = #{operatorNo},
            </if>
            <if test="investorNo != null">
                investorNo = #{investorNo},
            </if>
            <if test="businessNo != null">
                businessNo = #{businessNo},
            </if>
            <if test="operatorPoints != null">
                operatorPoints = #{operatorPoints},
            </if>
            <if test="investorPoints != null">
                investorPoints = #{investorPoints},
            </if>
            <if test="platformPoints != null">
                platformPoints = #{platformPoints},
            </if>
            <if test="businessPoints != null">
                businessPoints = #{businessPoints},
            </if>
            <if test="shareStauts != null">
                shareStauts = #{shareStauts},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitSharePostdivide">
        update profit_share_postdivide
        set
            preId =  #{preId},
            orderNo =  #{orderNo},
            projectName =  #{projectName},
            profitTotalPoints =  #{profitTotalPoints},
            operatorNo =  #{operatorNo},
            investorNo =  #{investorNo},
            businessNo =  #{businessNo},
            operatorPoints =  #{operatorPoints},
            investorPoints =  #{investorPoints},
            platformPoints =  #{platformPoints},
            businessPoints =  #{businessPoints},
            shareStauts =  #{shareStauts},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>
