<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.EqInfoDao">
	<insert id="batchInsert">
		INSERT INTO eq_info(eqNo,eqState,createTime,isDelete)
		VALUES
		<foreach collection="newEqList" item="item" separator=",">
			(#{item.eqNo},#{item.eqState},#{item.createTime},#{item.isDelete})
		</foreach>
	</insert>

	<update id="transfer">
		UPDATE eq_info
		SET operatorNo = #{targetOperatorNo}
		WHERE operatorNo = #{sourceOperatorNo}
		AND isDelete = 0
	</update>
	<update id="assign">
		UPDATE eq_info
		SET investorNo = #{investorNo}
		WHERE eqNo in
		<foreach collection="existsEqNos" item="eqNo" open="(" separator="," close=")">
			#{eqNo}
		</foreach>
		AND isDelete = 0
	</update>
	<update id="transferSalesman">
		UPDATE eq_info
		SET salesmanNo = #{targetSalesmanNo}
		WHERE salesmanNo = #{sourceSalesmanNo}
		AND isDelete = 0
	</update>
	<update id="clearOperatorEq">
		UPDATE eq_info
		SET operatorNo = NULL
		WHERE operatorNo = #{operatorNo}
		AND isDelete = 0
	</update>
	<update id="clearInvestorEq">
		UPDATE eq_info
		SET investorNo = NULL
		WHERE investorNo = #{investorNo}
		AND isDelete = 0
	</update>


	<select id="getPage" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT id,eqNo,eqType,eqState,busNo,operatorNo,investorNo,deployerNo,createTime,updateTime,isDelete
		FROM eq_info
		<where>
			<if test="queryData != null and queryData.eqNo != null">
				and eqNo = #{queryData.eqNo}
			</if>
			<if test="queryData != null and queryData.eqType != null">
				and eqType = #{queryData.eqType}
			</if>
			<if test="queryData != null and queryData.eqState != null">
				and eqState = #{queryData.eqState}
			</if>
			<if test="queryData != null and queryData.busNo != null">
				and busNo = #{queryData.busNo}
			</if>
			<if test="queryData != null and queryData.operatorNo != null">
				and operatorNo = #{queryData.operatorNo}
			</if>
			<if test="queryData != null and queryData.investorNo != null">
				and investorNo = #{queryData.investorNo}
			</if>
			<if test="queryData != null and queryData.deployerNo != null">
				and deployerNo = #{queryData.deployerNo}
			</if>
			<if test="true">
				AND isDelete = 0
			</if>
		</where>
	</select>
	<select id="getInfo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
		id,
		eqNo,
		eqType,
		eqState,
		busNo,
		operatorNo,
		investorNo,
		deployerNo,
		createTime,
		updateTime,
		isDelete
		FROM eq_info
		WHERE
		id = #{id}
	</select>
	<select id="selectExistingEqNos" resultType="java.lang.String">
		SELECT eqNo FROM eq_info
		WHERE eqNo in
		<foreach collection="eqNoList" item="eqNo" open="(" separator="," close=")">
			#{eqNo}
		</foreach>
		AND isDelete = 0
	</select>
	<select id="checkExistsDevices" resultType="java.lang.String">
		SELECT eqNo
		FROM eq_info
		WHERE eqNo in
		<foreach collection="eqNos" item="eqNo" open="(" separator="," close=")">
			#{eqNo}
		</foreach>
		AND isDelete = 0
		AND investorNo IS NULL
		AND eqState = 1
	</select>
	<select id="getInfoByBusNo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
		id,
		eqNo,
		eqType,
		eqState,
		busNo,
		operatorNo,
		investorNo,
		deployerNo,
		createTime,
		updateTime,
		isDelete
		FROM eq_info
		WHERE
		busNo = #{busNo} and eqState = 1
		AND (isDelete IS NULL OR isDelete = 0)
	</select>
	<select id="getInfoByOperatorNo" resultType="com.jmt.model.eq.dto.EqInfo">
		SELECT
		id,
		eqNo,
		eqType,
		eqState,
		busNo,
		operatorNo,
		investorNo,
		deployerNo,
		createTime,
		updateTime,
		isDelete
		FROM eq_info
		WHERE
		operatorNo = #{operatorNo}
		AND isDelete = 0
	</select>
	<select id="getInvestorEqInfo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
		id,
		eqNo,
		eqType,
		eqState,
		busNo,
		operatorNo,
		investorNo,
		deployerNo,
		createTime,
		updateTime,
		isDelete
		FROM eq_info
		WHERE
		investorNo = #{investorNo}
		AND isDelete = 0
	</select>
	<select id="getOperatorEqInfo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
		id,
		eqNo,
		eqType,
		eqState,
		busNo,
		operatorNo,
		investorNo,
		deployerNo,
		createTime,
		updateTime,
		isDelete
		FROM eq_info
		WHERE
		operatorNo = #{operatorNo}
		AND isDelete = 0
	</select>
	<select id="getSalesmanEqInfo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
		id,
		eqNo,
		eqType,
		eqState,
		busNo,
		operatorNo,
		investorNo,
		deployerNo,
		createTime,
		updateTime,
		isDelete
		FROM eq_info
		WHERE
		deployerNo = #{deployerNo}
		AND isDelete = 0
	</select>
</mapper>