package com.jmt.dao;

import com.jmt.model.cm.dto.CmWorkEqDeployDTO;
import com.jmt.model.cm.entity.CmWorkEqDeploy;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmWorkEqDeployDao {
    Integer insert(CmWorkEqDeploy record);
    CmWorkEqDeploy selectByPrimaryKey(Long id);
    Integer update(CmWorkEqDeploy record);
    Integer updateByPrimaryKey(CmWorkEqDeploy record);
    Integer delete(Long id);
    List<CmWorkEqDeploy> selectAll();
    List<CmWorkEqDeploy> selectByWorkStatus(Integer workStatus);
    CmWorkEqDeploy selectByWorkNo(String workNo);

    CmWorkEqDeploy selectById(@Param("id") Long id);

    List<CmWorkEqDeployDTO> selectByCondition(CmWorkEqDeployDTO cmWorkEqDeploy);
}
