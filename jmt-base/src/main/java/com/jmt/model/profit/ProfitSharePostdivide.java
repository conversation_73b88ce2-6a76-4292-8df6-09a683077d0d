package com.jmt.model.profit;

import lombok.Data;

import java.util.Date;

/**
 * 收益分润表
 * @TableName profit_share_postdivide
 */
@Data
public class ProfitSharePostdivide {
    /**
     * 主键
     */
    private Long id;

    /**
     * 预分润的主键 share_profit_predivide的主键ID
     */
    private Long preId;

    /**
     * 来自所有收益订单的orderNo
     */
    private String orderNo;

    /**
     * 收益分润项目名称
     */
    private String projectName;

    /**
     * 收益总积分
     */
    private Integer profitTotalPoints;

    /**
     * 运营商编号 jhh_operator_info的operatorNo
     */
    private String operatorNo;

    /**
     * 设备收益人编号 jhh_investor_info的investorNo
     */
    private String investorNo;

    /**
     * 商家编码 cm_business_info的businessNo
     */
    private String businessNo;

    /**
     * 运营商补贴积分，当shareType=1时填写
     */
    private Integer operatorPoints;

    /**
     * 设备收益人补贴积分，当shareType=1时填写
     */
    private Integer investorPoints;

    /**
     * 平台补贴积分，当shareType=1时填写
     */
    private Integer platformPoints;

    /**
     * 商家补贴积分，当shareType=1时填写
     */
    private Integer businessPoints;

    /**
     * 分润状态
     0-未入账
     1-已入账
     */
    private Integer shareStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProfitSharePostdivide other = (ProfitSharePostdivide) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getPreId() == null ? other.getPreId() == null : this.getPreId().equals(other.getPreId()))
                && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
                && (this.getProjectName() == null ? other.getProjectName() == null : this.getProjectName().equals(other.getProjectName()))
                && (this.getProfitTotalPoints() == null ? other.getProfitTotalPoints() == null : this.getProfitTotalPoints().equals(other.getProfitTotalPoints()))
                && (this.getOperatorNo() == null ? other.getOperatorNo() == null : this.getOperatorNo().equals(other.getOperatorNo()))
                && (this.getInvestorNo() == null ? other.getInvestorNo() == null : this.getInvestorNo().equals(other.getInvestorNo()))
                && (this.getBusinessNo() == null ? other.getBusinessNo() == null : this.getBusinessNo().equals(other.getBusinessNo()))
                && (this.getOperatorPoints() == null ? other.getOperatorPoints() == null : this.getOperatorPoints().equals(other.getOperatorPoints()))
                && (this.getInvestorPoints() == null ? other.getInvestorPoints() == null : this.getInvestorPoints().equals(other.getInvestorPoints()))
                && (this.getPlatformPoints() == null ? other.getPlatformPoints() == null : this.getPlatformPoints().equals(other.getPlatformPoints()))
                && (this.getBusinessPoints() == null ? other.getBusinessPoints() == null : this.getBusinessPoints().equals(other.getBusinessPoints()))
                && (this.getShareStatus() == null ? other.getShareStatus() == null : this.getShareStatus().equals(other.getShareStatus()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPreId() == null) ? 0 : getPreId().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getProjectName() == null) ? 0 : getProjectName().hashCode());
        result = prime * result + ((getProfitTotalPoints() == null) ? 0 : getProfitTotalPoints().hashCode());
        result = prime * result + ((getOperatorNo() == null) ? 0 : getOperatorNo().hashCode());
        result = prime * result + ((getInvestorNo() == null) ? 0 : getInvestorNo().hashCode());
        result = prime * result + ((getBusinessNo() == null) ? 0 : getBusinessNo().hashCode());
        result = prime * result + ((getOperatorPoints() == null) ? 0 : getOperatorPoints().hashCode());
        result = prime * result + ((getInvestorPoints() == null) ? 0 : getInvestorPoints().hashCode());
        result = prime * result + ((getPlatformPoints() == null) ? 0 : getPlatformPoints().hashCode());
        result = prime * result + ((getBusinessPoints() == null) ? 0 : getBusinessPoints().hashCode());
        result = prime * result + ((getShareStatus() == null) ? 0 : getShareStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", preId=").append(preId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", projectName=").append(projectName);
        sb.append(", profitTotalPoints=").append(profitTotalPoints);
        sb.append(", operatorNo=").append(operatorNo);
        sb.append(", investorNo=").append(investorNo);
        sb.append(", businessNo=").append(businessNo);
        sb.append(", operatorPoints=").append(operatorPoints);
        sb.append(", investorPoints=").append(investorPoints);
        sb.append(", platformPoints=").append(platformPoints);
        sb.append(", businessPoints=").append(businessPoints);
        sb.append(", shareStatus=").append(shareStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}