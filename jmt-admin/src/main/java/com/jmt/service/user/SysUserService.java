package com.jmt.service.user;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.*;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.admin.permission.SysPermissionVO;
import com.jmt.model.admin.resource.SysResource;
import com.jmt.model.admin.resource.SysResourceVO;
import com.jmt.model.admin.role.SysRole;
import com.jmt.model.admin.role.SysRoleVO;
import com.jmt.model.admin.user.*;
import com.jmt.util.EntityToVOConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SysUserService extends BaseService {

    @Resource
    private SysUserDao sysUserDao;
    @Resource
    private SysRoleDao sysRoleDao;
    @Resource
    private SysPermissionDao sysPermissionDao;
    @Resource
    private SysRolePermissionDao sysRolePermissionDao;
    @Resource
    private SysUserRoleDao sysUserRoleDao;
    @Resource
    private SysPermissionResourceDao sysPermissionResourceDao;
    @Resource
    private SysResourceDao sysResourceDao;

    /**
     * 用户启用/禁用（复用isDelete字段：0-启用，1-禁用）
     */
    @Transactional
    public void changeStatus(Long userId, Integer status) {
        // 参数校验
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new IllegalArgumentException("status必须为0（禁用）或1（启用）");
        }

        // 校验用户存在
        SysUser user = sysUserDao.getInfo(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 更新用户状态
        Date now = new Date();
        SysUser updateUser = new SysUser();
        updateUser.setId(userId);
        updateUser.setIsDelete(status == 0 ? 1 : 0); // 状态转换：启用→0→isDelete=1（禁用）；禁用→1→isDelete=0（启用）
        updateUser.setUpdateTime(now);
        sysUserDao.updateByPrimaryKeySelective(updateUser);

        // 同步更新用户角色关联状态
        syncUserRoleStatus(userId, updateUser.getIsDelete(), now);
    }

    /**
     * 批量逻辑删除用户（级联处理角色关联）
     */
    @Transactional
    public void batchDelete(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            throw new IllegalArgumentException("用户ID列表不能为空");
        }

        Date now = new Date();
        int count = 0;
        for (Long userId : userIds) {
            // 逻辑删除用户
            SysUser user = new SysUser();
            user.setId(userId);
            user.setIsDelete(1);
            user.setUpdateTime(now);
            count += sysUserDao.updateByPrimaryKeySelective(user);

            // 同步删除用户角色关联
            syncUserRoleStatus(userId, 1, now);
        }

        if (count != userIds.size()) {
            throw new RuntimeException("批量删除失败，部分用户不存在或已删除");
        }
    }

    /**
     * 批量分配角色给用户（先清除原有角色，再添加新角色）
     */
    @Transactional
    public void assignRoles(Long userId, List<Long> roleIds) {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (roleIds == null || roleIds.isEmpty()) {
            throw new IllegalArgumentException("角色ID列表不能为空");
        }

        // 清除用户原有角色关联（逻辑删除）
        Date now = new Date();
        SysUserRole deleteRole = new SysUserRole();
        deleteRole.setUserId(userId);
        deleteRole.setIsDelete(1);
        deleteRole.setUpdateTime(now);
        sysUserRoleDao.batchUpdateIsDeleteByUserId(deleteRole);

        // 批量添加新角色关联
        List<SysUserRole> userRoles = roleIds.stream().map(roleId -> {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            ur.setCreateTime(now);
            ur.setUpdateTime(now);
            ur.setIsDelete(0);
            return ur;
        }).collect(Collectors.toList());
        sysUserRoleDao.batchInsert(userRoles);
    }

    /**
     * 用户下拉框数据（支持用户名模糊搜索）
     */
    public List<SysUser> getComboList(String keyword) {
        return sysUserDao.selectComboList(keyword);
    }

    /**
     * 用户分页查询
     */
    public PageResult<SysUser> getPage(PageQuery<SysUser> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysUser sysUser = pageQuery.getQueryData();
        List<SysUser> list = sysUserDao.getPage(sysUser);
        return new PageResult<>(list);
    }

    /**
     * 获取用户详细信息（含角色、权限、资源）
     */
    public SysUserDetail getUserDetail(SysUser user) {
        SysUserDetail sysUserDetail = new SysUserDetail();
        sysUserDetail.setUser(user);

        // 获取用户角色ID列表
        List<Long> roleIds = sysUserRoleDao.findAllByIdLongs(user.getId());
        if (roleIds.isEmpty()) {
            return sysUserDetail;
        }

        // 组装角色信息
        Set<SysRole> roles = sysRoleDao.findByRoleIds(roleIds);
        Set<SysRoleVO> roleVOs = roles.stream()
                .map(EntityToVOConverter::convertToRoleVO)
                .collect(Collectors.toSet());
        sysUserDetail.setRoles(roleVOs);

        // 组装权限信息
        List<Long> permissionIds = sysRolePermissionDao.findByRoleIds(roleIds);
        if (!permissionIds.isEmpty()) {
            Set<SysPermission> permissions = sysPermissionDao.findByPermissionIds(permissionIds);
            Set<SysPermissionVO> permissionVOs = permissions.stream()
                    .map(EntityToVOConverter::convertToPermissionVO)
                    .collect(Collectors.toSet());
            sysUserDetail.setPermissions(permissionVOs);

            // 组装资源信息
            List<Long> resourceIds = sysPermissionResourceDao.findByPermissionIds(permissionIds);
            if (!resourceIds.isEmpty()) {
                Set<SysResource> resources = sysResourceDao.findByResourceIds(resourceIds);
                Set<SysResourceVO> resourceVOs = resources.stream()
                        .map(EntityToVOConverter::convertToResourceVO)
                        .collect(Collectors.toSet());
                sysUserDetail.setResources(resourceVOs);
            }
        }

        return sysUserDetail;
    }

    /**
     * 根据ID查询用户基本信息
     */
    public SysUser getInfo(Long userId) {
        return sysUserDao.getInfo(userId);
    }

    /**
     * 新增用户（默认分配角色ID=2）
     */
    @Transactional
    public Long add(SysUser sysUser) {
        // 构建用户信息
        Date now = new Date();
        SysUser newSysUser = new SysUser();
        newSysUser.setUserName(sysUser.getUserName());
        newSysUser.setCreateTime(now);
        newSysUser.setUpdateTime(now);
        newSysUser.setIsFirstLogin(1);
        newSysUser.setIsDelete(0);
        newSysUser.setUaaId(sysUser.getUaaId());
        sysUserDao.insert(newSysUser);

        // 关联默认角色
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(newSysUser.getId());
        sysUserRole.setRoleId(2L);
        sysUserRole.setCreateTime(now);
        sysUserRole.setUpdateTime(now);
        sysUserRole.setIsDelete(0);
        sysUserRoleDao.insert(sysUserRole);

        return newSysUser.getId();
    }

    /**
     * 单个用户逻辑删除（级联处理角色关联）
     */
    @Transactional
    public Integer remove(Long userId) {
        Date now = new Date();
        // 删除用户
        SysUser sysUser = new SysUser();
        sysUser.setId(userId);
        sysUser.setIsDelete(1);
        sysUser.setUpdateTime(now);
        sysUserDao.deleteByPrimaryKey(sysUser);

        // 删除用户角色关联
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(userId);
        sysUserRole.setIsDelete(1);
        sysUserRole.setUpdateTime(now);
        return sysUserRoleDao.deleteUserRoleByUserId(sysUserRole);
    }

    /**
     * 更新用户信息（用户名、首次登录状态）
     */
    @Transactional
    public Integer update(SysUser sysUser) {
        SysUser old = getInfo(sysUser.getId());
        old.setUpdateTime(new Date());
        old.setIsFirstLogin(0);
        old.setUserName(sysUser.getUserName());
        return sysUserDao.updateByPrimaryKey(old);
    }

    /**
     * 新增用户角色关联
     */
    public Integer addUserRole(SysUserRoleDto sysUserRoleDto) {
        Date now = new Date();
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUserRoleDto.getUserId());
        sysUserRole.setRoleId(sysUserRoleDto.getRoleId());
        sysUserRole.setCreateTime(now);
        sysUserRole.setUpdateTime(now);
        sysUserRole.setIsDelete(0);
        return sysUserRoleDao.insert(sysUserRole);
    }

    /**
     * 解除用户角色关联（逻辑删除）
     */
    public Integer deleteUserRole(SysUserRoleDto sysUserRoleDto) {
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUserRoleDto.getUserId());
        sysUserRole.setRoleId(sysUserRoleDto.getRoleId());
        sysUserRole.setUpdateTime(new Date());
        sysUserRole.setIsDelete(1);
        return sysUserRoleDao.deleteUserRoleByUserIdAndRoleId(sysUserRole);
    }

    /**
     * 同步更新用户角色关联状态
     */
    private void syncUserRoleStatus(Long userId, Integer isDelete, Date updateTime) {
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(userId);
        userRole.setIsDelete(isDelete);
        userRole.setUpdateTime(updateTime);
        sysUserRoleDao.batchUpdateIsDeleteByUserId(userRole);
    }
}