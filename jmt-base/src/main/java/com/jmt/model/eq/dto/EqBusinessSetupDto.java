package com.jmt.model.eq.dto;

import lombok.Data;

import java.util.Date;

/**
 * 商家设备设置表实体类
 * 对应表：eq_business_setup
 */
@Data
public class EqBusinessSetupDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 商家编号，关联 cm_business_info 的 busNo
     */
    private String busNo;
    /**
     * 音量，范围 1 - 100
     */
    private Integer volume;
    /**
     * 最小音量持续时间，单位分钟
     */
    private Integer minVoume;
    /**
     * 最小（结合注释，推测是补充字段含义，按实际业务调整，这里先保留）
     */
    private Integer minDurationTime;
    /**
     * 开机时间1
     */
    private String openTime1;
    /**
     * 关机时间1
     */
    private String closeTime1;
    /**
     * 开机时间2
     */
    private String openTime2;
    /**
     * 关机时间2
     */
    private String closeTime2;
    /**
     * 开机时间3
     */
    private String openTime3;
    /**
     * 关机时间3
     */
    private String closeTime3;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除，0-否 1-是
     */
    private Integer isDelete;
}
