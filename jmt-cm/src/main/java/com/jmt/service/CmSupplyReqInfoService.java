package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmSupplyReqInfoConvertor;
import com.jmt.dao.CmSupplyReqAuditDao;
import com.jmt.dao.CmSupplyReqInfoDao;
import com.jmt.dao.CmSupplyReqOrderDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmSupplyReqInfoDTO;
import com.jmt.model.cm.entity.CmSupplyReqAudit;
import com.jmt.model.cm.entity.CmSupplyReqInfo;
import com.jmt.model.cm.entity.CmSupplyReqOrder;
import com.jmt.model.cm.vo.CmSupplyReqInfoVO;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmSupplyReqInfoService extends BaseService {
    @Resource
    private CmSupplyReqInfoDao cmSupplyReqInfoDao;

    @Resource
    private CmSupplyReqAuditDao cmSupplyReqAuditDao;

    @Resource
    private CmSupplyReqOrderDao cmSupplyReqOrderDao;

    @Resource
    private CmSupplyReqInfoConvertor cmSupplyReqInfoConvertor;

    @Transactional
    public Long create(CmSupplyReqInfoDTO dto) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoConvertor.toEntity(dto);

        cmSupplyReqInfo.setAuditStatus(0);
        cmSupplyReqInfo.setCreateTime(new Date());
        cmSupplyReqInfo.setUpdateTime(new Date());
        cmSupplyReqInfo.setIsDelete(0);
        Long result = cmSupplyReqInfoDao.insert(cmSupplyReqInfo);
        Long generatedId = cmSupplyReqInfo.getId();

        CmSupplyReqAudit audit = new CmSupplyReqAudit();
        audit.setInfoId(generatedId);
        audit.setAuditStatus(0);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setIsDelete(0);
        cmSupplyReqAuditDao.insert(audit);
        return result;
    }

    @Transactional
    public int update(CmSupplyReqInfoDTO dto) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoDao.selectById(dto.getId());
        if (cmSupplyReqInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        if (cmSupplyReqInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmSupplyReqInfo cmWorkEq = cmSupplyReqInfoConvertor.toEntity(dto);
        cmWorkEq.setUpdateTime(new Date());
        return cmSupplyReqInfoDao.update(cmWorkEq);
    }


    @Transactional
    public int delete(Long id) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoDao.selectById(id);
        if (cmSupplyReqInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmSupplyReqInfoDao.delete(id);
    }

    @Transactional
    public CmSupplyReqInfoVO getById(Long id) {
        return cmSupplyReqInfoConvertor.toVo(cmSupplyReqInfoDao.selectById(id));
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmSupplyReqInfoDTO> getPage(PageQuery<CmSupplyReqInfoDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        CmSupplyReqInfoDTO cmSupplyReqInfoDTO = pageQuery.getQueryData();
        List<CmSupplyReqInfoDTO> list = cmSupplyReqInfoDao.selectByCondition(cmSupplyReqInfoDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason, Long auditUserId) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoDao.selectById(id);
        if (cmSupplyReqInfo == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmSupplyReqInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单审核状态
        CmSupplyReqInfo update = new CmSupplyReqInfo();
        update.setId(id);
        update.setAuditStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());
        int updateCount = cmSupplyReqInfoDao.update(update);

        // 查询审核记录
        CmSupplyReqAudit existingAudit = cmSupplyReqAuditDao.selectByInfoId(id);

        // 更新审核表
        CmSupplyReqAudit audit = new CmSupplyReqAudit();
        audit.setInfoId(id);
        audit.setAuditStatus(auditStatus);
        audit.setReason(reason);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setAuditUser(auditUserId);
        audit.setIsDelete(0);
        audit.setId(existingAudit.getId());
        cmSupplyReqAuditDao.update(audit);

        return updateCount;
    }

    /**
     * 创建订单
     * @param order
     */
    @Transactional
    public void createOrder(CmSupplyReqOrder order) {
        order.setOrderStatus(0);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = WorkNoGeneratorUtil.generate("RNO");
        order.setOrderNo(orderNo);
        cmSupplyReqOrderDao.insert(order);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional
    public void cancelOrder(Long id) {
        CmSupplyReqOrder order = cmSupplyReqOrderDao.selectById(id);
        if (order == null || order.getIsDelete() == 1) {
            throw new RuntimeException("订单不存在或已被删除");
        }
        order.setOrderStatus(3); //取消支付
        order.setUpdateTime(new Date());
        cmSupplyReqOrderDao.update(order);
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditOrder(Long id, Integer auditStatus, String reason) {
        CmSupplyReqOrder order = cmSupplyReqOrderDao.selectById(id);
        if (order == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单支付状态
        CmSupplyReqOrder update = new CmSupplyReqOrder();
        update.setId(id);
        update.setOrderStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmSupplyReqOrderDao.update(update);
    }
}
