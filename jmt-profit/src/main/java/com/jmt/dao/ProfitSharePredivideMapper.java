package com.jmt.dao;

import com.jmt.model.profit.ProfitSharePredivide;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【profit_share_predivide(收益预分润表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitSharePredivide
*/
public interface ProfitSharePredivideMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitSharePredivide record);

    int insertSelective(ProfitSharePredivide record);

    ProfitSharePredivide selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitSharePredivide record);

    int updateByPrimaryKey(ProfitSharePredivide record);
    List<ProfitSharePredivide> selectByShareStatus(@Param("shareStauts") int shareStauts);

}
