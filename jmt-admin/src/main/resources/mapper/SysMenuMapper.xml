<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysMenuDao">

    <select id="getSysMenuListByParentId" parameterType="java.lang.Long" resultType="com.jmt.model.admin.permission.SysPermission">
        SELECT
            `id`, `parentId`, `permissKey`, `permissName`, `permissType`, `router`, `listSort`, `isHide`, `createTime`, `updateTime`, `isDelete`
        FROM
            `sys_permission`
        WHERE `parentId` = #{parentId}
    </select>

</mapper>
