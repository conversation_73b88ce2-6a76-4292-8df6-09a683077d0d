package com.jmt.model.profit.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

/**
* 退款流水表
* @TableName profit_recharge_record
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfitRechargeRecord implements Serializable {

    private Long id;
    /**
    * 订单编号
    */
    private String seqNo;
    /**
    * 
    */
    private String orderNo;
    /**
    * 退款人，uaa_user的uaaId
    */
    private Long rechargeUser;
    /**
    * 退款方式
0-积分退款
1-余额退款
2-微信退款
3-支付宝退款
    */
    private Integer rechargeType;
    /**
    * 充值金额
    */
    private BigDecimal rechargeAmount;
    /**
    * 订单状态
0-未退款
1-支付退款 
2-支付退款
3-取消退款
    */
    private Integer rechargeStatus;
    /**
    * 退款失败必填
    */
    private String reason;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;



}
