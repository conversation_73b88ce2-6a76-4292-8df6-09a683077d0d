package com.jmt.dao;

import com.jmt.model.zg.ZgDriverInfo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【zg_driver_info(筷圣直供配送员信息表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgDriverInfo
*/
public interface ZgDriverInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgDriverInfo record);

    int insertSelective(ZgDriverInfo record);

    ZgDriverInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgDriverInfo record);

    int updateByPrimaryKey(ZgDriverInfo record);

    ZgDriverInfo selectByUserIdKey(Long id);

    List<ZgDriverInfo> getDriverInfoPage(ZgDriverInfo queryData);

    List<Map<String, String>> getDriverNoAndName();

    ZgDriverInfo selectByDriverNo(String driverNo);
}
