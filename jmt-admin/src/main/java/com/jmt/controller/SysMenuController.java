package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.admin.menu.AppRouteRecord;
import com.jmt.model.admin.menu.RouteMeta;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.service.menu.SysMenuService;
import com.jmt.service.user.SysUserService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/admin/menu/v1")
public class SysMenuController extends BaseController {

    @Resource
    private SysMenuService sysMenuService;

    /**
     * 获取用户菜单
     * @return
     */
    @PostMapping("userMenuList")
    @ResponseBody
    public String userMenuList() {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        List<AppRouteRecord> appRouteRecordList = sysMenuService.getUserMenuList(loginUaaUser);
        return super.responseSuccess(appRouteRecordList,"查询成功");
    }
}
