package com.jmt.controller;
import com.jmt.base.BaseController;

import com.jmt.client.CommonFeignClient;
import com.jmt.exception.BusinessException;
import com.jmt.model.eq.dto.RequestSendEq;
import com.jmt.model.jhh.dto.InvestorAuditDto;
import com.jmt.model.jhh.dto.InvestorInfo;
import com.jmt.model.jhh.dto.InvestorInfoDto;
import com.jmt.model.jhh.dto.InvestorPageQueryDto;
import com.jmt.model.jhh.vo.InvestorVo;
import com.jmt.model.jhh.vo.JhhUserInfoVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.InvestorService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


@RestController
@RequestMapping("/jhh/investor/v1")
public class InvestorController extends BaseController{

    @Resource
    private InvestorService investorService;

    @Resource
    private CommonFeignClient commonFeignClient;
    /**
     * 按id查询收益人
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        InvestorInfo investorInfo = investorService.getInfo(id);
        return super.responseSuccess(investorInfo,"查询成功");
    }
    /**
     * 分页查询
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<InvestorPageQueryDto> pageQuery) {
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<InvestorVo> page = investorService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 新增收益人
     * @param investorInfoDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody InvestorInfoDto investorInfoDto) {
        try {
            logger.info("新增收益人:{}",investorInfoDto);
            investorService.add(investorInfoDto);
            return super.responseSuccess("新增成功");
        }catch (BusinessException e){
            return super.responseFail(e.getMessage());
        }

    }
    /**
     * 上传图片
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/upload")
    public String upload(@RequestParam("file") MultipartFile multipartFile) {
        logger.info("上传图片:{}", multipartFile);
        commonFeignClient.upload(multipartFile);
        return super.responseSuccess("上传成功");
    }
    /**
     * 修改收益人信息，并修改相关审核信息
     * @param investorInfoDto 修改参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody InvestorInfoDto investorInfoDto) {
        try {
            logger.info("修改参数:{}",investorInfoDto);
            investorService.update(investorInfoDto);
            return super.responseSuccess("修改成功");
        }catch (BusinessException e){
            return super.responseFail(e.getMessage());
        }

    }
    /**
     * 删除收益人以及相关信息
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    public String delete(@RequestParam Long id) {
        try {
            logger.info("删除参数:{}",id);
            investorService.delete(id);
            return super.responseSuccess("删除成功");
        }catch (Exception e){
            logger.error("删除异常:{}",e);
            return super.responseFail(e.getMessage());
        }

    }
    /**
     * 收益人状态变更，并填写相关审核信息
     * @param investorInfoAuditDto
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/audit")
    public String audit(@RequestBody InvestorAuditDto investorInfoAuditDto, HttpServletRequest request) {
        try {
            logger.info("收益人状态变更参数:{}",investorInfoAuditDto);
            investorService.audit(investorInfoAuditDto,request);
            return super.responseSuccess("修改成功");
        }catch (Exception e){
            return super.responseFail(e.getMessage());
        }

    }
    /**
     * 设备收益人分配设备接口
     * @param requestSendEq
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/assign")
    public String assignEq(@RequestBody RequestSendEq requestSendEq) {
            logger.info("设备收益人分配设备参数:{}",requestSendEq);
            investorService.assignEq(requestSendEq);
            return super.responseSuccess("分配成功");
    }
    /**
     * 下拉数据框
     *
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInvestorSelect")
    public String getInvestorSelect() {
            List<InvestorVo> list = investorService.getOperatorSelect();
            return super.responseSuccess(list,"查询成功");

    }
    /**
     * 冻结接口
     * @param
     * @param investorInfoAuditDto
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/freeze")
    public String freeze(@RequestBody InvestorAuditDto investorInfoAuditDto, HttpServletRequest request) {
            investorService.freeze(investorInfoAuditDto,request);
            return super.responseSuccess("冻结成功");

    }
    /**
     * 用户信息查询接口
     * @param request
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getUserInfo")
    public String getUserInfo(HttpServletRequest request) {
            JhhUserInfoVo jhhUserInfoVo = investorService.getUserInfo(request);
            return super.responseSuccess(jhhUserInfoVo,"查询成功");
    }
    /**
     * 设备收益人查询接口
     * @param uaaId
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInvestorInfoByUaaId")
    public String getInvestorInfoByUaaId(@RequestParam Long uaaId) {
            InvestorInfo investorInfo = investorService.getInvestorInfoByUaaId(uaaId);
            return super.responseSuccess(investorInfo,"查询成功");
    }
    @ResponseBody
    @GetMapping(value = "/getUserInfoByInvestorNo")
    public String getUserInfo(@RequestParam String  investorNo) {
        JhhUserInfoVo jhhUserInfoVo = investorService.getUserInfoByInvestor(investorNo);
        return super.responseSuccess(jhhUserInfoVo,"查询成功");
    }
}
