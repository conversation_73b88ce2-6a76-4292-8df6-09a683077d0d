<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.InvestorEqDao">
    <update id="clearInvestorEq">
        UPDATE eq_info
        SET investorNo = NULL,
            isDelete = 1
        WHERE investorNo = #{investorNo}
    </update>

    <select id="getInfo" resultType="com.jmt.model.eq.vo.EqOperatorStockVo">
        SELECT id,operatorNo,eqStockTotal,eqAnum,eqBnum,eqCnum,onlineNum,offlineNum,createTime,updateTime,isDelete
        FROM eq_operator_stock
        WHERE id = #{id}
    </select>
</mapper>