<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitBalanceRechargeOrderMapper">

    <sql id="Base_Column_List">
        id,orderNo,orderName,rechargeUser,payType,payAmount,
        orderStatus,reason,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitBalanceRechargeOrder">
        select
        <include refid="Base_Column_List" />
        from profit_balance_recharge _order
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_balance_recharge _order
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitBalanceRechargeOrder" useGeneratedKeys="true">
        insert into profit_balance_recharge _order
        ( id,orderNo,orderName,rechargeUser,payType,payAmount,
            orderStatus,reason,createTime,updateTime,isDelete)
        values (#{id},#{orderNo},#{orderName},#{rechargeUser},#{payType},#{payAmount},
                   #{orderStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitBalanceRechargeOrder" useGeneratedKeys="true">
        insert into profit_balance_recharge _order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="orderName != null">orderName,</if>
            <if test="rechargeUser != null">rechargeUser,</if>
            <if test="payType != null">payType,</if>
            <if test="payAmount != null">payAmount,</if>
            <if test="orderStatus != null">orderStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderName != null">#{orderName},</if>
            <if test="rechargeUser != null">#{rechargeUser},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitBalanceRechargeOrder">
        update profit_balance_recharge _order
        <set>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="orderName != null">
                orderName = #{orderName},
            </if>
            <if test="rechargeUser != null">
                rechargeUser = #{rechargeUser},
            </if>
            <if test="payType != null">
                payType = #{payType},
            </if>
            <if test="payAmount != null">
                payAmount = #{payAmount},
            </if>
            <if test="orderStatus != null">
                orderStatus = #{orderStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitBalanceRechargeOrder">
        update profit_balance_recharge _order
        set
            orderNo =  #{orderNo},
            orderName =  #{orderName},
            rechargeUser =  #{rechargeUser},
            payType =  #{payType},
            payAmount =  #{payAmount},
            orderStatus =  #{orderStatus},
            reason =  #{reason},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>