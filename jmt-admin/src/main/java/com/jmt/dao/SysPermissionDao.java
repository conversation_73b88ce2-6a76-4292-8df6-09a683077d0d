package com.jmt.dao;

import com.jmt.model.admin.permission.SysPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【sys_permission(系统权限表)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.permission.SysPermission
*/
public interface SysPermissionDao {

     List<SysPermission> getPage(SysPermission sysUser);


    int deleteByPrimaryKey(Long id);

    int insert(SysPermission record);

    int insertSelective(SysPermission record);

    SysPermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysPermission record);

    int updateByPrimaryKey(SysPermission record);
    Set<SysPermission> findByPermissionId(Long Id);

    Set<SysPermission> findByPermissionIds(@Param("permissionIds") List<Long> permissionIds);

    // 查询顶级权限

    List<SysPermission> findTopLevelPermissions();

    // 根据父ID查询子权限

    List<SysPermission> findChildrenByParentId(Long parentId);
}
