package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供配送员信息表
 * @TableName zg_driver_info
 */
@Data
public class ZgDriverInfo {
    /**
     *
     */
    private Long id;

    /**
     * zg_user的id
     */
    private Long userId;

    /**
     * 配送员编号
     */
    private String driverNo;

    /**
     * 配送员姓名
     */
    private String driverName;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 手机号
     */
    private String telPhone;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 配送范围
     */
    private String areaScope;

    /**
     * 车辆照片
     */
    private String carPhoto;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆最大载重，单位KG
     */
    private Integer maxLoad;

    /**
     * 星级 1-5 标识1-5个星
     */
    private Integer driverStar;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 0-待审核
     1-同意
     2-驳回
     3-冻结
     */
    private Integer auditStatus;

    /**
     * 审核意见
     */
    private String reason;

    /**
     * 是否已实名认证 0-否 1-是
     */
    private Integer isRealAuth;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ZgDriverInfo other = (ZgDriverInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
                && (this.getDriverNo() == null ? other.getDriverNo() == null : this.getDriverNo().equals(other.getDriverNo()))
                && (this.getDriverName() == null ? other.getDriverName() == null : this.getDriverName().equals(other.getDriverName()))
                && (this.getIdNo() == null ? other.getIdNo() == null : this.getIdNo().equals(other.getIdNo()))
                && (this.getTelPhone() == null ? other.getTelPhone() == null : this.getTelPhone().equals(other.getTelPhone()))
                && (this.getCountry() == null ? other.getCountry() == null : this.getCountry().equals(other.getCountry()))
                && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
                && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
                && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
                && (this.getLongitude() == null ? other.getLongitude() == null : this.getLongitude().equals(other.getLongitude()))
                && (this.getLatitude() == null ? other.getLatitude() == null : this.getLatitude().equals(other.getLatitude()))
                && (this.getAreaScope() == null ? other.getAreaScope() == null : this.getAreaScope().equals(other.getAreaScope()))
                && (this.getCarPhoto() == null ? other.getCarPhoto() == null : this.getCarPhoto().equals(other.getCarPhoto()))
                && (this.getCarNumber() == null ? other.getCarNumber() == null : this.getCarNumber().equals(other.getCarNumber()))
                && (this.getMaxLoad() == null ? other.getMaxLoad() == null : this.getMaxLoad().equals(other.getMaxLoad()))
                && (this.getDriverStar() == null ? other.getDriverStar() == null : this.getDriverStar().equals(other.getDriverStar()))
                && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
                && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
                && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
                && (this.getIsRealAuth() == null ? other.getIsRealAuth() == null : this.getIsRealAuth().equals(other.getIsRealAuth()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getDriverNo() == null) ? 0 : getDriverNo().hashCode());
        result = prime * result + ((getDriverName() == null) ? 0 : getDriverName().hashCode());
        result = prime * result + ((getIdNo() == null) ? 0 : getIdNo().hashCode());
        result = prime * result + ((getTelPhone() == null) ? 0 : getTelPhone().hashCode());
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getAreaScope() == null) ? 0 : getAreaScope().hashCode());
        result = prime * result + ((getCarPhoto() == null) ? 0 : getCarPhoto().hashCode());
        result = prime * result + ((getCarNumber() == null) ? 0 : getCarNumber().hashCode());
        result = prime * result + ((getMaxLoad() == null) ? 0 : getMaxLoad().hashCode());
        result = prime * result + ((getDriverStar() == null) ? 0 : getDriverStar().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getIsRealAuth() == null) ? 0 : getIsRealAuth().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", driverNo=").append(driverNo);
        sb.append(", driverName=").append(driverName);
        sb.append(", idNo=").append(idNo);
        sb.append(", telphone=").append(telPhone);
        sb.append(", country=").append(country);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", address=").append(address);
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", areaScope=").append(areaScope);
        sb.append(", carPhoto=").append(carPhoto);
        sb.append(", carNumber=").append(carNumber);
        sb.append(", maxLoad=").append(maxLoad);
        sb.append(", driverStar=").append(driverStar);
        sb.append(", remark=").append(remark);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", reason=").append(reason);
        sb.append(", isRealAuth=").append(isRealAuth);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}