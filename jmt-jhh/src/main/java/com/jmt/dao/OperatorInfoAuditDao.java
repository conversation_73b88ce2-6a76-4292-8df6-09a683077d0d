package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.jhh.dto.OperatorInfoAudit;
import com.jmt.model.jhh.dto.OperatorInfoAuditDto;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.jhh.vo.OperatorInfoAuditVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperatorInfoAuditDao {
    /**
     * 添加审核信息
     * @param investorInfoAudit
     */
    void add(OperatorInfoAudit investorInfoAudit);
    /**
     * 删除审核信息
     * @param operatorNo
     */
    void delete(String operatorNo);
    /**
     * 批量添加审核信息
     * @param salesmanInfos
     */
    void insertBatch(List<SalesmanInfo> salesmanInfos);
    /**
     * 分页获取审核信息
     * @param queryData
     * @return
     */
    List<OperatorInfoAuditVo> getPage(@Param("queryData") OperatorInfoAuditDto queryData);
    /**
     * 获取审核信息
     * @param id
     * @return
     */
    OperatorInfoAudit getInfo(Long id);
    /**
     * 删除审核信息
     * @param id
     */
    void deleteById(Long id);
    /**
     * 根据运营商编号获取审核信息
     * @param operatorNo
     * @return
     */
    List<OperatorInfoAudit> getInfoByOperatorNo(String operatorNo);
    /**
     * 修改审核信息
     * @param operatorInfoAudit
     */
    void update(OperatorInfoAudit operatorInfoAudit);
    /**
     * 根据id删除审核信息
     * @param id
     */
    void deleteByIdWithLong(Long id);
}
