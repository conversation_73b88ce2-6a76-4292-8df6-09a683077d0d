<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitPointsRecordMapper">

    <sql id="Base_Column_List">
        id,uaaId,points,recordType,pointSource,sourceId,
        remark,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitPointsRecord">
        select
        <include refid="Base_Column_List" />
        from profit_points_record
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_points_record
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitPointsRecord" useGeneratedKeys="true">
        insert into profit_points_record
        ( id,uaaId,points,recordType,pointSource,sourceId,
          remark,createTime,updateTime,isDelete)
        values (#{id},#{uaaId},#{points},#{recordType},#{pointSource},#{sourceId},
                #{remark},#{createTime},#{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitPointsRecord" useGeneratedKeys="true">
        insert into profit_points_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uaaId != null">uaaId,</if>
            <if test="points != null">points,</if>
            <if test="recordType != null">recordType,</if>
            <if test="pointSource != null">pointSource,</if>
            <if test="sourceId != null">sourceId,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uaaId != null">#{uaaId},</if>
            <if test="points != null">#{points},</if>
            <if test="recordType != null">#{recordType},</if>
            <if test="pointSource != null">#{pointSource},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitPointsRecord">
        update profit_points_record
        <set>
            <if test="uaaId != null">
                uaaId = #{uaaId},
            </if>
            <if test="points != null">
                points = #{points},
            </if>
            <if test="recordType != null">
                recordType = #{recordType},
            </if>
            <if test="pointSource != null">
                pointSource = #{pointSource},
            </if>
            <if test="sourceId != null">
                sourceId = #{sourceId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitPointsRecord">
        update profit_points_record
        set
            uaaId =  #{uaaId},
            points =  #{points},
            recordType =  #{recordType},
            pointSource =  #{pointSource},
            sourceId =  #{sourceId},
            remark =  #{remark},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>