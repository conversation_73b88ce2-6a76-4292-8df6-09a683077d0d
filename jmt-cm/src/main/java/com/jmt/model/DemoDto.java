package com.jmt.model;

import lombok.Data;

import java.util.Date;

/**
 * 对象注解写这里
 */
@Data
public class DemoDto {

    /**
     * 主键
     */
    private Long id;
    /**
     * 名称
     */
    private String demoName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除 0-否（默认） 1-删除
     */
    private Integer isDelete;

}
