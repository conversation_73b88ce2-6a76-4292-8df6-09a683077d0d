package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.admin.permission.SysPermissionResourceDto;
import com.jmt.service.permission.SysPermissionService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/permission/v1")
public class SysPermissionController extends BaseController {

    @Resource
    private SysPermissionService sysPermissionService;

    /**
     * 分页查询权限列表
     * @param pageQuery 分页查询条件，包含查询参数和分页信息
     * @param req HTTP请求对象，用于获取登录用户信息
     * @return 分页查询结果
     */
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<SysPermission> pageQuery, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        PageResult<SysPermission> page = sysPermissionService.getPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 添加新权限
     * @param sysPermission 权限实体，包含权限名称、类型等信息
     * @return 操作结果
     */
    @PostMapping("/addPermission")
    public String addPermission(@RequestBody SysPermission sysPermission) {
        sysPermissionService.addPermission(sysPermission);
        return super.responseSuccess("添加成功");
    }

    /**
     * 更新现有权限信息
     * @param sysPermission 权限实体，包含要更新的权限ID和新的权限信息
     * @return 操作结果
     */
    @PostMapping("/updatePermission")
    public String updatePermission(@RequestBody SysPermission sysPermission) {
        sysPermissionService.updatePermission(sysPermission);
        return super.responseSuccess("更新成功");
    }

    /**
     * 删除权限
     * @param sysPermission 权限实体，必须包含要删除的权限ID
     * @return 操作结果
     */
    @PostMapping("/deletePermission")
    public String deletePermission(@RequestBody SysPermission sysPermission) {
        sysPermissionService.deletePermission(sysPermission);
        return super.responseSuccess("删除成功");
    }

    /**
     * 获取权限树形结构数据
     * @param parentId 父权限ID，可选参数，为空时获取所有权限的树形结构
     * @return 权限树形结构数据
     */
    @GetMapping("/treeList")
    public String treeList(@RequestParam(required = false) Long parentId) {
        List<Map<String, Object>> treeList = sysPermissionService.getPermissionTree(parentId);
        return super.responseSuccess(treeList, "查询成功");
    }

    /**
     * 获取单个权限详细信息
     * @param permissionId 权限ID
     * @return 权限详细信息
     */
    @GetMapping("/getPermissionInfo")
    public String getPermissionInfo(@RequestParam Long permissionId) {
        SysPermission permission = sysPermissionService.getPermissionInfo(permissionId);
        return super.responseSuccess(permission, "查询成功");
    }

    /**
     * 为权限分配资源
     * @param dto 权限资源分配DTO，包含权限ID和资源ID列表
     * @return 操作结果
     */
    @PostMapping("/assignResource")
    public String assignResource(@RequestBody SysPermissionResourceDto dto) {
        sysPermissionService.assignResource(dto);
        return super.responseSuccess("分配资源成功");
    }
}