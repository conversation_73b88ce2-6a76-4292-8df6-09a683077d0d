package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.eq.dto.EqInfo;
import com.jmt.model.eq.dto.EqInfoDto;
import com.jmt.model.eq.vo.EqInfoVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EqInfoDao {
    /**
     * 获取分页数据
     *
     * @param queryData 查询参数
     * @return 分页数据
     */
    List<EqInfoVo> getPage(@Param("queryData") EqInfoDto queryData);
    /**
     * 获取详情
     *
     * @param id 主键
     * @return 详情
     */
    EqInfoVo getInfo(Long id);
    /**
     * 转移
     *
     * @param sourceOperatorNo 源操作员编号
     * @param targetOperatorNo 目标操作员编号
     */
    void transfer(@Param("sourceOperatorNo") String sourceOperatorNo, @Param("targetOperatorNo") String targetOperatorNo);
    /**
     * 分配
     *
     * @param existsEqNos 设备编号
     * @param investorNo 投资人编号
     */
    void assign(@Param("existsEqNos") List<String> existsEqNos, @Param("investorNo") String investorNo);
    /**
     * 查询已存在的设备编号
     *
     * @param eqNoList 设备编号
     * @return 已存在的设备编号
     */
    List<String> selectExistingEqNos(@Param("eqNoList") List<String> eqNoList);
    /**
     * 批量插入
     *
     * @param newEqList 新的设备信息
     */
    void batchInsert(@Param("newEqList") List<EqInfo> newEqList);

    /**
     * 校验设备是否存在（未被删除）
     *
     * @param eqNos 更新的设备信息
     */
    List<String> checkExistsDevices(@Param("eqNos") List<String> eqNos);

    List<EqInfoVo> getInfoByBusNo(String busNo);
    /**
     * 根据收益人编号获取设备信息
     *
     * @param sourceOperatorNo 操作员编号
     * @return 设备信息
     */
    EqInfo getInfoByOperatorNo(String sourceOperatorNo);
    /**
     * 转移设备上的运营商对应的业务员信息
     *
     * @param sourceDeployerNo 部署人编号
     * @return 设备信息
     */
    void transferSalesman(@Param("sourceDeployerNo") String sourceDeployerNo,@Param("targetDeployerNo") String targetDeployerNo);
    /**
     * 获取收益人设备详情
     */
    List<EqInfoVo> getInvestorEqInfo(String investorNo);
    /**
     * 获取运营商设备详情
     */
    List<EqInfoVo> getOperatorEqInfo(String operatorNo);
    /**
     * 获取业务员设备详情
     */
    List<EqInfoVo> getSalesmanEqInfo(String deployerNo);

    /**
     * 解除运营商设备
     */
    void clearOperatorEq(String operatorNo);
    /**
     * 解除收益人设备
     */
    void clearInvestorEq(String operatorNo);
}
