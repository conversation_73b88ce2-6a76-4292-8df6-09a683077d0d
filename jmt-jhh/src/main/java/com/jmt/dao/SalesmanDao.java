package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.jhh.dto.SalesmanInfoDto;
import com.jmt.model.jhh.vo.SalesmanVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalesmanDao {
    /**
     * 获取分页数据
     *
     * @param queryData 查询参数
     * @return 分页数据
     */
    List<SalesmanVo> getPage(@Param("queryData") SalesmanInfoDto queryData);
    /**
     * 添加业务员
     *
     * @param
     * @return 信息
     */
    void add(SalesmanInfo salesmanInfo);
    /**
     * 修改业务员
     *
     * @param salesmanInfo
     * @return 信息
     */
    void update(SalesmanInfo salesmanInfo);
    /**
     * 按id查询业务员
     *
     * @param id
     * @return 信息
     */
    SalesmanInfo getInfo(Long id);
    /**
     * 按id删除业务员
     *
     * @param id
     * @return 信息
     */
    void delete(Long id);
    /**
     * 转移业务员
     *
     * @param
     * @return 信息
     */
    void transfer(@Param("sourceOperatorNo") String sourceOperatorNo, @Param("targetOperatorNo") String targetOperatorNo);
    /**
     * 按uaaId查询业务员
     *
     * @param uaaId
     * @return 信息
     */
    SalesmanInfo getByUaaId(Long uaaId);
    /**
     * 获取业务员下拉框
     *
     * @return 信息
     */
    List<SalesmanVo> getSalesmanDropdown();
}
