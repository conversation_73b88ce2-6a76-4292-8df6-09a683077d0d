package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OperatorEqDao {
    List<EqBusinessSetupVo> getPage(EqOperatorStockDto queryData);

    EqOperatorStockVo getInfo(Integer id);

    void transferStock(@Param("sourceOperatorNo") String sourceOperatorNo, @Param("targetOperatorNo") String targetOperatorNo);

    void clearOperatorEq(@Param("operatorNo") String operatorNo);
}
