package com.jmt.service;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.InvestorEqDao;
import com.jmt.dao.OperatorEqDao;
import com.jmt.exception.BusinessException;
import com.jmt.dao.EqInfoDao;
import com.jmt.listener.EqInfoImportListener;
import com.jmt.model.eq.dto.*;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
@Service
public class EqInfoService {
    @Resource
    private EqInfoDao eqInfoDao;
    @Resource
    private EqInfoImportListener eqInfoImportListener;
    @Resource
    private OperatorEqDao operatorEqDao;
    @Resource
    private InvestorEqDao investorEqDao;
    /**
     * 分页查询设备信息列表
     * @param pageQuery
     * @return
     */
    public PageResult<EqInfoVo> getPage(PageQuery<EqInfoDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqInfoVo> page = eqInfoDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 查询设备信息详情
     *
     * @param id
     * @return
     */
    public EqInfoVo getInfo(Long id) {
        return eqInfoDao.getInfo(id);
    }
    /**
     * 设备信息转移
     *
     * @param requestTrans
     */
    public void transfer(RequestTrans requestTrans) {
        eqInfoDao.transfer(requestTrans.getSourceOperatorNo(), requestTrans.getTargetOperatorNo());
        EqInfo sourceEqInfo = eqInfoDao.getInfoByOperatorNo(requestTrans.getSourceOperatorNo()) ;
        EqInfo TargetEqInfo = eqInfoDao.getInfoByOperatorNo(requestTrans.getTargetOperatorNo());
        String sourceDeployerNo = sourceEqInfo.getDeployerNo();
        String targetDeployerNo = TargetEqInfo.getDeployerNo();
        eqInfoDao.transferSalesman(sourceDeployerNo, targetDeployerNo);
    }
    /**
     * 批量分配设备
     *
     * @param requestSendEq
     */
    public void assign(RequestSendEq requestSendEq) {
        List<String> eqNos = requestSendEq.getEqNos();
        if (eqNos.isEmpty()){
            throw new BusinessException("设备编号不能为空");
        }
        String investorNo = requestSendEq.getInvestorNo();
        List<String> existsEqNos = eqInfoDao.checkExistsDevices(eqNos);
        if (existsEqNos.isEmpty()){
            throw new BusinessException("设备编号不存在");
        }
        eqInfoDao.assign(existsEqNos, investorNo);
    }
    /**
     * 设备导入
     *
     * @param file
     */
    public void importEq(MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(), EqInfoImport.class, eqInfoImportListener).sheet().headRowNumber(1).doRead();
    }

    public List<EqInfoVo> getInfoByBusNo(String busNo) {
        return eqInfoDao.getInfoByBusNo(busNo);
    }
    /**
     * 查询收益人设备信息详情
     *
     * @param investorNo
     * @return
     */
    public List<EqInfoVo> getInvestorEqInfo(String investorNo) {

        return eqInfoDao.getInvestorEqInfo(investorNo);
    }
    /**
     * 查询运营商设备信息详情
     *
     * @param operatorNo
     * @return
     */
    public List<EqInfoVo> getOperatorEqInfo(String operatorNo) {

        return eqInfoDao.getOperatorEqInfo(operatorNo);
    }
    /**
     * 获取业务员设备信息详情
     *
     * @param deployerNo
     * @return
     */
    public List<EqInfoVo> getSalesmanEqInfo(String deployerNo) {

        return eqInfoDao.getSalesmanEqInfo(deployerNo);
    }
    /**
     * 获取用户编号
     */
//    private String getNo(String response){
//        Gson gson = new Gson();
//        JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
//        JsonElement dataElement = jsonObject.get("data");
//        JhhUserInfoVo userInfo = gson.fromJson(dataElement, JhhUserInfoVo.class);
//        return userInfo.getUserNo();
//    }
//    private String getResponse(GetNoRequest getNoRequest, Integer type){
//        HttpServletRequest request = getNoRequest.getRequest();
//        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
//        Integer userType = loginUaaUser.getUserType();
//        if (!userType.equals(type)){
//            throw new BusinessException("用户类型错误");
//        }
//        String response = jhhFeignClient.getUserInfo(request);
//        return response;
//    }
    /**
     * 分页查询收益人设备信息列表
     *
     * @param pageQuery
     * @return
     */
    public PageResult<EqInfoVo> getInvestorEqPage(PageQuery<EqInfoDto> pageQuery, String investorNo) {
        EqInfoDto eqInfoDto = new EqInfoDto();
        BeanUtils.copyProperties(pageQuery.getQueryData(),eqInfoDto);
        eqInfoDto.setOperatorNo(investorNo);
        pageQuery.setQueryData(eqInfoDto);
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqInfoVo> page = eqInfoDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 分页查询运营商设备信息列表
     *
     * @param pageQuery
     * @return
     */
    public PageResult<EqInfoVo> getOperatorEqPage(PageQuery<EqInfoDto> pageQuery, String operatorNo) {
        EqInfoDto eqInfoDto = new EqInfoDto();
        BeanUtils.copyProperties(pageQuery.getQueryData(),eqInfoDto);
        eqInfoDto.setOperatorNo(operatorNo);
        pageQuery.setQueryData(eqInfoDto);
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqInfoVo> page = eqInfoDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }

    public PageResult<EqInfoVo> getSalesmanEqPage(PageQuery<EqInfoDto> pageQuery,String deployerNo) {
        EqInfoDto eqInfoDto = new EqInfoDto();
        BeanUtils.copyProperties(pageQuery.getQueryData(),eqInfoDto);
        eqInfoDto.setOperatorNo(deployerNo);
        pageQuery.setQueryData(eqInfoDto);
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqInfoVo> page = eqInfoDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 解除设备与运营商关系
     * @param operatorNo
     * @return
     */
    public void clearOperatorEq(String operatorNo) {
        eqInfoDao.clearOperatorEq(operatorNo);
        operatorEqDao.clearOperatorEq(operatorNo);
    }
    /**
     * 删除投资方设备关系
     * @param operatorNo
     * @return
     */
    public void clearInvestorEq(String operatorNo) {
        eqInfoDao.clearInvestorEq(operatorNo);
        investorEqDao.clearInvestorEq(operatorNo);
    }
}
