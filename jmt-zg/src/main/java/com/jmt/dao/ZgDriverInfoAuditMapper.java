package com.jmt.dao;

import com.jmt.model.zg.ZgDriverInfoAudit;

/**
* <AUTHOR>
* @description 针对表【zg_driver_info_audit(筷圣直供配送员信息审核记录表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgDriverInfoAudit
*/
public interface ZgDriverInfoAuditMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgDriverInfoAudit record);

    int insertSelective(ZgDriverInfoAudit record);

    ZgDriverInfoAudit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgDriverInfoAudit record);

    int updateByPrimaryKey(ZgDriverInfoAudit record);

    ZgDriverInfoAudit selectByDriverNo(String driverNo);
}
