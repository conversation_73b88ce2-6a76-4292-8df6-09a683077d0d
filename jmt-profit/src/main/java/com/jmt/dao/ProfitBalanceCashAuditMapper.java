package com.jmt.dao;

import com.jmt.model.profit.ProfitBalanceCashAudit;

/**
* <AUTHOR>
* @description 针对表【profit_balance_cash _audit(余额提现申请审核记录表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitBalanceCashAudit
*/
public interface ProfitBalanceCashAuditMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitBalanceCashAudit record);

    int insertSelective(ProfitBalanceCashAudit record);

    ProfitBalanceCashAudit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitBalanceCashAudit record);

    int updateByPrimaryKey(ProfitBalanceCashAudit record);

}
