<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBusinessInfoDao">


    <sql id="Base_Column_List">
        id, userId, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, devloperNo, busPhoto,
        busLicense, identityCardFront, identityCardBack, categoryId, measure,
        ydeskNum, fdeskNum, comsumeAvg, openTime, visitorAvg, busStar, remark,
        auditStatus, reason, createTime, updateTime, isDelete
    </sql>

    <!-- 插入商家信息 -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBusinessInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_business_info (
        userId, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, devloperNo, busPhoto,
        busLicense, identityCardFront, identityCardBack, categoryId, measure,
        ydeskNum, fdeskNum, comsumeAvg, openTime, visitorAvg, busStar, remark,
        auditStatus, reason, createTime, updateTime, isDelete
        ) VALUES (
        #{userId}, #{busNo}, #{busName}, #{linkman}, #{telPhone}, #{country}, #{province}, #{city}, #{area},
        #{address}, #{longitude}, #{latitude}, #{operatorNo}, #{refereeNo}, #{devloperNo}, #{busPhoto},
        #{busLicense}, #{identityCardFront}, #{identityCardBack}, #{categoryId}, #{measure},
        #{ydeskNum}, #{fdeskNum}, #{comsumeAvg}, #{openTime}, #{visitorAvg}, #{busStar}, #{remark},
        #{auditStatus}, #{reason}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <!-- 更新商家信息 -->
    <update id="updateById" parameterType="com.jmt.model.cm.entity.CmBusinessInfo">
        UPDATE cm_business_info
        <set>
            <if test="userId != null">userId = #{userId},</if>
            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="busName != null">busName = #{busName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo},</if>
            <if test="devloperNo != null">devloperNo = #{devloperNo},</if>
            <if test="busPhoto != null">busPhoto = #{busPhoto},</if>
            <if test="busLicense != null">busLicense = #{busLicense},</if>
            <if test="identityCardFront != null">identityCardFront = #{identityCardFront},</if>
            <if test="identityCardBack != null">identityCardBack = #{identityCardBack},</if>
            <if test="categoryId != null">categoryId = #{categoryId},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="ydeskNum != null">ydeskNum = #{ydeskNum},</if>
            <if test="fdeskNum != null">fdeskNum = #{fdeskNum},</if>
            <if test="comsumeAvg != null">comsumeAvg = #{comsumeAvg},</if>
            <if test="openTime != null">openTime = #{openTime},</if>
            <if test="visitorAvg != null">visitorAvg = #{visitorAvg},</if>
            <if test="busStar != null">busStar = #{busStar},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE cm_business_info
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询商家信息 -->
    <select id="selectById" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByUserId" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE userId = #{userId} AND isDelete = 0
    </select>

    <!-- 查询所有商家信息 -->
<!--    <select id="selectAll" resultMap="BaseResultMap">-->
<!--        SELECT <include refid="Base_Column_List"/>-->
<!--        FROM cm_business_info-->
<!--        WHERE is_delete = 0-->
<!--    </select>-->

    <!-- 条件查询商家信息 -->
    <select id="selectByCondition" parameterType="com.jmt.model.cm.entity.CmBusinessInfo" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        <where>
            isDelete = 0
            <if test="userId != null">AND userId = #{userId}</if>
            <if test="busNo != null and busNo != ''">AND busNo LIKE CONCAT('%', #{busNo}, '%')</if>
            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName}, '%')</if>
            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman}, '%')</if>
            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone}</if>
            <if test="country != null and country != ''">AND country = #{country}</if>
            <if test="province != null and province != ''">AND province = #{province}</if>
            <if test="city != null and city != ''">AND city = #{city}</if>
            <if test="area != null and area != ''">AND area = #{area}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address}, '%')</if>
            <if test="operatorNo != null and operatorNo != ''">AND operatorNo = #{operatorNo}</if>
            <if test="refereeNo != null and refereeNo != ''">AND refereeNo = #{refereeNo}</if>
            <if test="devloperNo != null and devloperNo != ''">AND devloperNo = #{devloperNo}</if>
            <if test="categoryId != null">AND categoryId = #{categoryId}</if>
            <if test="auditStatus != null">AND auditStatus = #{auditStatus}</if>
        </where>
        ORDER BY createTime
    </select>
    <select id="selectByBusinessNo" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE busNo = #{businessNo} AND isDelete = 0
    </select>


</mapper>