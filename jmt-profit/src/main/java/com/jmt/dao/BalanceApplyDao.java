package com.jmt.dao;

import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.vo.BalanceRechargeOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface BalanceApplyDao {
    /**
     * 获取申请次数
     *
     * @return 提现申请列表
     */
    int getCountByUaaIdAndCreateTime(@Param("applyUser") Long applyUser, @Param("startDay")Date startDay, @Param("endDay") Date endDay);
    /**
     * 增加提现申请
     *
     * @return 提现申请列表
     */
    void add(BalanceApply balanceApply);
    /**
     * 获取提现申请列表
     *
     * @return 提现申请列表
     */
    List<BalanceApply> getPage(@Param("queryData") BalanceApplyDto queryData);
    /**
     * 根据id查询
     *
     * @return 提现申请列表
     */
    BalanceApply getInfo(Long id);
    /**
     * 修改提现申请
     *
     * @return 提现申请列表
     */
    void update(BalanceApply balanceApply);
}
