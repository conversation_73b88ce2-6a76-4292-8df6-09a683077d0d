package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.DropdownVO;
import com.jmt.model.zg.ZgSupplierInfo;
import com.jmt.service.SupplierService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 供应商管理控制器
 * 提供供应商的禁用、启用、分页查询、详情查询、审核及下拉框数据获取等功能
 */
@RestController
@RequestMapping("/zg/supplier/v1")
public class SupplierController extends BaseController {

    @Resource
    private SupplierService supplierService;

    /**
     * 禁用供应商
     * @param userId 供应商信息（需包含供应商ID）
     * @return 操作结果
     */
    @PostMapping("/disableSupplier")
    public String disableSupplier(@RequestParam Long userId) {
        supplierService.disableSupplierInfo(userId);
        return super.responseSuccess("禁用成功");
    }

    /**
     * 启用供应商
     * @param userId 供应商信息（需包含供应商ID）
     * @return 操作结果
     */
    @PostMapping("/enableSupplier")
    public String enableSupplier(@RequestParam Long userId) {
        supplierService.enableSupplierInfo(userId);
        return super.responseSuccess("启用成功");
    }

    /**
     * 分页查询供应商信息
     * @param pageQuery 分页查询参数，包含查询条件（如供应商名称、状态等）和分页信息（页码、页大小）
     * @return 供应商分页数据结果
     */
    @GetMapping("/page")
    public String getPage(@RequestBody PageQuery<ZgSupplierInfo> pageQuery) {
        PageResult<ZgSupplierInfo> page = supplierService.getSupplierInfoPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 根据ID查询供应商详情
     * @param supplierNo 供应商ID
     * @return 供应商详细信息
     */
    @GetMapping("/SupplierInfo")
    public String getSupplierInfo(@RequestParam String supplierNo) {
        ZgSupplierInfo supplier = supplierService.getSupplierInfo(supplierNo);
        return super.responseSuccess(supplier, "查询成功");
    }

    /**
     * 审核供应商信息
     * @param supplierNo 供应商编号
     * @param auditStatus 审核状态（0-待审核，1-审核通过，2-审核拒绝）
     * @param reason 审核意见（审核拒绝时必填）
     * @return 操作结果
     */
    @PostMapping("/audit")
    public String auditSupplier(@RequestParam String supplierNo, @RequestParam Integer auditStatus, @RequestParam String reason, HttpServletRequest req) {
        Long uaaId = LoginUserUtil.get(req).getUaaId();
        supplierService.auditSupplierInfo(supplierNo, auditStatus, reason,uaaId);
        return super.responseSuccess("审核操作成功");
    }

    /**
     * 获取供应商下拉框数据（用于选择场景）
     * @return 包含供应商ID和名称的下拉框数据列表
     */
    @GetMapping("/supplierDropdown")
    public String getSupplierDropdownData() {
        List<DropdownVO> supplierList = supplierService.getSupplierIdAndName();
        return super.responseSuccess(supplierList, "查询成功");
    }
}