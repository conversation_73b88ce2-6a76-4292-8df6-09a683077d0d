package com.jmt.timer.job;

import com.jmt.service.ProfitService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CalculateBatchJob implements Job {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProfitService profitService;

    @Override
    public void execute(JobExecutionContext context) {
        logger.info("开始执行预分润批量计算任务");
        try {
            int processedCount = profitService.calculateBatchPreProfit();
            logger.info("预分润批量计算完成，处理条数：" + processedCount);
        } catch (Exception e) {
            logger.error("预分润批量计算任务执行失败", e);
        }
    }
}
