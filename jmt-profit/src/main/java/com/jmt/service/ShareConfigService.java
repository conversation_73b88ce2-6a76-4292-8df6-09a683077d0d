package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.dao.ShareConfigDao;
import com.jmt.dao.ShareProjectDao;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.BusinessException;
import com.jmt.model.profit.dto.ShareConfig;
import com.jmt.model.profit.dto.ShareConfigDto;
import com.jmt.model.profit.dto.ShareConfigPageDto;
import com.jmt.model.profit.dto.ShareProject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
public class ShareConfigService extends BaseService {
    @Resource
    private ShareConfigDao shareConfigDao;
    @Resource
    private ShareProjectDao shareProjectDao;
    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;
    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<ShareConfig> getPage(PageQuery<ShareConfigPageDto> pageQuery, String operatorNo) {
        ShareConfigPageDto shareConfigDto = new ShareConfigPageDto();
        BeanUtils.copyProperties(pageQuery.getQueryData(),shareConfigDto);
        shareConfigDto.setOperatorNo(operatorNo);
        pageQuery.setQueryData(shareConfigDto);
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<ShareConfig> page = shareConfigDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 添加
     * @param shareConfigDto
     *
     */
    public void add(ShareConfigDto shareConfigDto) {
        if (shareConfigDto.getShareType() == 0 &&
                (shareConfigDto.getBusinessRatio()==null || shareConfigDto.getInvestorRatio()==null
                || shareConfigDto.getOperatorRatio()==null|| shareConfigDto.getPlatformRatio()==null)){
            throw new RuntimeException("请填写正确的分润比例");
        }
        if (shareConfigDto.getShareType() == 1 &&
                (shareConfigDto.getBusinessAmount()==null || shareConfigDto.getInvestorAmount()==null
                || shareConfigDto.getPlatformAmount()==null|| shareConfigDto.getOperatorAmount()==null)){
            throw new RuntimeException("请填写正确的分润补贴");
        }
        if (shareConfigDto.getProjectId() == null){
            throw new RuntimeException("请选择项目");
        }
        Long projectId = shareConfigDto.getProjectId();
        ShareProject shareProject = shareProjectDao.getInfoByParentId(projectId);
        if (shareProject == null){
            throw new RuntimeException("请选择正确的项目");
        }
        String operatorInfoByOperatorNo = jhhOperatorFeignClient.getOperatorInfoByOperatorNo(shareConfigDto.getOperatorNo());
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(operatorInfoByOperatorNo).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        OperatorInfo operatorInfo = gson.fromJson(dataElement, OperatorInfo.class);
        if (operatorInfo == null){
            throw new BusinessException("该运营商不存在");
        }
        ShareConfig shareConfig = new ShareConfig();
        BeanUtils.copyProperties(shareConfigDto,shareConfig);
        LocalDateTime now = LocalDateTime.now();
        shareConfig.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        shareConfig.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        shareConfig.setIsDelete(0);
        shareConfig.setProjectName(shareProject.getProjectName());
        //默认启用
        shareConfig.setUseStauts(0);
        shareConfig.setOperatorNo(shareConfigDto.getOperatorNo());
        shareConfigDao.add(shareConfig);
    }
    /**
     * 删除
     * @param id
     */
    public void delete(Long id) {
        shareConfigDao.delete(id);
    }
    /**
     * 修改
     * @param shareConfigDto
     */
    public void update(ShareConfigDto shareConfigDto) {

    }
    /**
     * 获取详情
     * @param operatorNo
     * @return
     */
    public List<ShareConfigDto> getInfo(String operatorNo) {
        return shareConfigDao.getInfo(operatorNo);
    }
    /**
     * 删除
     * @param operatorNo
     */
    public void deleteByNo(String operatorNo) {
        shareConfigDao.deleteByNo(operatorNo);
    }
}
