package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmWorkEqServiceConvertor;
import com.jmt.dao.CmWorkEqServiceDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmWorkEqServiceDTO;
import com.jmt.model.cm.entity.CmWorkEqService;
import com.jmt.model.cm.vo.CmWorkEqServiceVO;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@Service
public class CmWorkEqSvService extends BaseService {

    @Resource
    private CmWorkEqServiceDao cmWorkEqServiceDao;

    @Resource
    private CmWorkEqServiceConvertor cmWorkEqServiceConvertor;
    @Transactional
    public int create(CmWorkEqServiceDTO dto) {
        CmWorkEqService cmWorkEqService = cmWorkEqServiceConvertor.toEntity(dto);
        // 生成工单编号（使用日期+随机数）
        String workNo = WorkNoGeneratorUtil.generate("EQS");

        cmWorkEqService.setWorkNo(workNo);
        cmWorkEqService.setWorkStatus(0);
        cmWorkEqService.setCreateTime(new Date());
        cmWorkEqService.setUpdateTime(new Date());
        cmWorkEqService.setIsDelete(0);
        // 先插入主表
        int result = cmWorkEqServiceDao.insert(cmWorkEqService);
        return result;
    }

    @Transactional
    public int update(CmWorkEqServiceDTO dto) {
        // 验证工单是否存在
        CmWorkEqService cmWorkEqService = cmWorkEqServiceDao.selectById(dto.getId());
        if (cmWorkEqService == null) {
            throw new RuntimeException("工单不存在");
        }
        // 只能修改待审核状态的工单
        if (cmWorkEqService.getWorkStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmWorkEqService cmWorkEq = cmWorkEqServiceConvertor.toEntity(dto);

        cmWorkEq.setUpdateTime(new Date());
        int result = cmWorkEqServiceDao.update(cmWorkEq);
        return result;
    }


    @Transactional
    public int delete(Long id) {
        CmWorkEqService cmWorkEqService = cmWorkEqServiceDao.selectById(id);
        if (cmWorkEqService == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmWorkEqServiceDao.delete(id);
    }

    @Transactional
    public CmWorkEqServiceVO getById(Long id) {
        return cmWorkEqServiceConvertor.toVo(cmWorkEqServiceDao.selectById(id));
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmWorkEqServiceDTO> getPage(PageQuery<CmWorkEqServiceDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        CmWorkEqServiceDTO cmWorkEqServiceDTO = pageQuery.getQueryData();
        List<CmWorkEqServiceDTO> list = cmWorkEqServiceDao.selectByCondition(cmWorkEqServiceDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason) {
        CmWorkEqService cmWorkEqService = cmWorkEqServiceDao.selectById(id);
        if (cmWorkEqService == null) {
            throw new RuntimeException("工单不存在");
        }
        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmWorkEqService.getWorkStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        CmWorkEqService update = new CmWorkEqService();
        update.setId(id);
        update.setWorkStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmWorkEqServiceDao.update(update);
    }
}
