<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitRechargeRecordMapper">

    <sql id="Base_Column_List">
        id,seqNo,orderNo,rechargeUser,rechargeType,rechargeAmount,
        rechargeStatus,reason,createTime,updateTime,isDelete
    </sql>

    <!-- 修正：resultMap改为resultType，删除对BaseResultMap的引用 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitRechargeRecord">
        select
        <include refid="Base_Column_List" />
        from profit_recharge_record
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_recharge_record
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitRechargeRecord" useGeneratedKeys="true">
        insert into profit_recharge_record
        ( id,seqNo,orderNo,rechargeUser,rechargeType,rechargeAmount,
          rechargeStatus,reason,createTime,updateTime,isDelete)
        values (#{id},#{seqNo},#{orderNo},#{rechargeUser},#{rechargeType},#{rechargeAmount},
                #{rechargeStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitRechargeRecord" useGeneratedKeys="true">
        insert into profit_recharge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="seqNo != null">seqNo,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="rechargeUser != null">rechargeUser,</if>
            <if test="rechargeType != null">rechargeType,</if>
            <if test="rechargeAmount != null">rechargeAmount,</if>
            <if test="rechargeStatus != null">rechargeStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="seqNo != null">#{seqNo},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="rechargeUser != null">#{rechargeUser},</if>
            <if test="rechargeType != null">#{rechargeType},</if>
            <if test="rechargeAmount != null">#{rechargeAmount},</if>
            <if test="rechargeStatus != null">#{rechargeStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitRechargeRecord">
        update profit_recharge_record
        <set>
            <if test="seqNo != null">
                seqNo = #{seqNo},
            </if>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="rechargeUser != null">
                rechargeUser = #{rechargeUser},
            </if>
            <if test="rechargeType != null">
                rechargeType = #{rechargeType},
            </if>
            <if test="rechargeAmount != null">
                rechargeAmount = #{rechargeAmount},
            </if>
            <if test="rechargeStatus != null">
                rechargeStatus = #{rechargeStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitRechargeRecord">
        update profit_recharge_record
        set
            seqNo =  #{seqNo},
            orderNo =  #{orderNo},
            rechargeUser =  #{rechargeUser},
            rechargeType =  #{rechargeType},
            rechargeAmount =  #{rechargeAmount},
            rechargeStatus =  #{rechargeStatus},
            reason =  #{reason},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>