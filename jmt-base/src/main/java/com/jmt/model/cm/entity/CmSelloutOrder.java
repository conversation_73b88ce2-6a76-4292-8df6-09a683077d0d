package com.jmt.model.cm.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 筷圣餐谋商户卖光光订单表
* @TableName cm_sellout_order
*/
@Data
public class CmSelloutOrder implements Serializable {

    /**
     *
     */
    @NotNull(message="[]不能为空")
    private Long id;
    /**
     * 信息ID cm_supply_req_info的id
     */
    private Long infoId;
    /**
     * 订单编号
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String orderNo;
    /**
     * 订单名称
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String orderName;
    /**
     * 商家编号
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busNo;
    /**
     * 支付方式
     0-积分支付
     1-余额支付
     2-微信支付
     3-支付宝
     */
    private Integer payType;
    /**
     * 支付金额
     */
    private Integer payAmount;
    /**
     * 订单状态
     0-未支付
     1-支付成功
     2-支付失败
     3-取消支付
     */
    private Integer orderStatus;
    /**
     * 驳回必填
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String reason;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

}
