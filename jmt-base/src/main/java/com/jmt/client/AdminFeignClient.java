package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-common",path ="/admin",configuration = FeignConfig.class)
public interface AdminFeignClient {

    @GetMapping("/user/v1/getInfo")
    String getInfo(@RequestParam(value = "userId") Long userId);
}
