package com.jmt.dao;

import com.jmt.model.profit.dto.BalanceCashApplyAudit;
import com.jmt.model.profit.dto.PointsCashApplyAudit;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BalanceApplyAuditDao {
    /**
     * 根据申请编号获取审核信息
     * @param applyNo
     * @return
     */
    List<BalanceCashApplyAudit> getInfoByApplyNo(String applyNo);
    /**
     * 修改审核信息
     * @param applyAudit
     */
    void update(BalanceCashApplyAudit applyAudit);
    /**
     * 新增审核信息
     * @param applyAudit
     */
    void add(BalanceCashApplyAudit applyAudit);
}
