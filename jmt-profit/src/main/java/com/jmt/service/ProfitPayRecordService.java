package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.ProfitPayRecordMapper;
import com.jmt.enums.PayStatusEnum;
import com.jmt.model.profit.ProfitPayRecord;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ProfitPayRecordService extends BaseService {
    @Resource
    private ProfitPayRecordMapper profitPayRecordMapper;

    @Transactional
    public int insert(ProfitPayRecord record) {
        record.setSeqNo(WorkNoGeneratorUtil.generate("PEQ"));
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setIsDelete(0);
        record.setPayStatus(PayStatusEnum.UNPAID.getCode());
        return profitPayRecordMapper.insert(record);
    }
    @Transactional
    public int updateById(ProfitPayRecord record) {
        ProfitPayRecord profitPayRecord = profitPayRecordMapper.selectBySeqNo(record.getSeqNo());
        if (profitPayRecord == null) {
            throw new RuntimeException("支付记录不存在");
        }
        if(profitPayRecord.getPayStatus() == 1){
            throw new RuntimeException("支付已完成，无法修改");
        }
        if(record.getPayStatus() == 2 && StringUtils.isBlank(record.getReason())){
            throw new RuntimeException("支付失败必填");
        }
        record.setUpdateTime(new Date());
        return profitPayRecordMapper.updateByPrimaryKey(record);
    }





}
