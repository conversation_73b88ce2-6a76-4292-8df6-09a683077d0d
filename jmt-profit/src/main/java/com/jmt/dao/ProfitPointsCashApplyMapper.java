package com.jmt.dao;

import com.jmt.model.profit.ProfitPointsCashApply;

/**
* <AUTHOR>
* @description 针对表【profit_points_cash_apply(积分兑现申请表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitPointsCashApply
*/
public interface ProfitPointsCashApplyMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitPointsCashApply record);

    int insertSelective(ProfitPointsCashApply record);

    ProfitPointsCashApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitPointsCashApply record);

    int updateByPrimaryKey(ProfitPointsCashApply record);

}
