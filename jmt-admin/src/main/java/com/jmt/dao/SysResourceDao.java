package com.jmt.dao;

import com.jmt.model.admin.resource.SysResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【sys_resource(系统资源表)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.Resource.SysResource
*/
public interface SysResourceDao {

    int deleteByPrimaryKey(Long id);

    int insert(SysResource record);

    int insertSelective(SysResource record);

    SysResource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysResource record);

    int updateByPrimaryKey(SysResource record);

    Set<SysResource> findByResourceIds(@Param("resourceIds") List<Long> resourceIds);

    List<SysResource> getPage(SysResource sysUser);
    /**
     * 查询所有可用资源（未删除的）
     * @return 资源列表
     */
    List<SysResource> selectAllAvailable();
}
