package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.*;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.enums.PayStatusEnum;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.BusinessException;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.entity.ProfitPayRecord;
import com.jmt.model.profit.vo.BalanceRechargeOrderVo;
import com.jmt.model.uaa.UaaUserBankcard;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.DateUtil;
import com.jmt.util.ProfitUserNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class BalanceCashService extends BaseService {
    //最低提现余额
    private static final BigDecimal MIN_BALANCE = new BigDecimal("10");
    //最高提现余额
    private static final BigDecimal MAX_BALANCE = new BigDecimal("50000");
    //需财务审核的金额下限
    private static final BigDecimal MIN_AUDIT_BALANCE = new BigDecimal("1000");
    //每日提现上限
    private static final int MAX_DAY_COUNT = 5;
    //每周提现上限
    private static final int MAX_WEEK_COUNT = 10;
    //每月提现上限
    private static final int MAX_MONTH_COUNT = 20;
    //最小充值金额
    private static final BigDecimal MIN_RECHARGE_AMOUNT = new BigDecimal("10");
    //失败备注
    private static final String FAIL_REMARK = "提现失败 退还余额";
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private BalanceRecordDao balanceRecordDao;
    @Resource
    private BalanceApplyDao balanceApplyDao;
    @Resource
    private BalanceApplyAuditDao balanceApplyAuditDao;
    @Resource
    private BalanceRechargeOrderDao balanceRechargeOrderDao;
    @Resource
    private ProfitPayRecordDao profitPayRecordDao;
    // 用户锁缓存（每个用户一个锁对象）
    private final ConcurrentHashMap<Long, Object> userLocks = new ConcurrentHashMap<>();
    // 锁缓存（按申请单ID加锁，防止并发审核）
    private final ConcurrentHashMap<Long, Object> applyLocks = new ConcurrentHashMap<>();
    /**
     * 申请兑现
     *
     * @param balanceApplyDto
     * @param applyUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void apply(BalanceApplyDto balanceApplyDto, Long applyUser) {
        // 1. 校验用户存在
        BalanceApply balanceApply = new BalanceApply();
        BeanUtils.copyProperties(balanceApplyDto, balanceApply);
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        // 2. 获取余额配置，校验积分是否满足兑现条件
        BigDecimal balance = balanceApplyDto.getBalance();
        validatePointsCashCondition(userWallet,balance);
        // 3. 校验提现频次限制（每天/每周/每月）
        Date now = new Date();
        Date startDay = DateUtil.getDayStart(now);
        Date endDay = DateUtil.getDayEnd(now);
        Date startWeek = DateUtil.getWeekStart(now);
        Date endWeek = DateUtil.getWeekEnd(now);
        Date startMonth = DateUtil.getMonthStart(now);
        Date endMonth = DateUtil.getMonthEnd(now);
        int countDay = balanceApplyDao.getCountByUaaIdAndCreateTime(applyUser, startDay, endDay);
        int countWeek = balanceApplyDao.getCountByUaaIdAndCreateTime(applyUser, startWeek, endWeek);
        int countMonth = balanceApplyDao.getCountByUaaIdAndCreateTime(applyUser, startMonth, endMonth);
        if (countDay >= MAX_DAY_COUNT){
            throw new RuntimeException("今日已超过最大提现次数限制");
        }
        if (countWeek >= MAX_WEEK_COUNT){
            throw new RuntimeException("本周已超过最大提现次数限制");
        }
        if (countMonth >= MAX_MONTH_COUNT){
            throw new RuntimeException("本月已超过最大提现次数限制");
        }
        //查看银行卡是否存在
        Long bankId = balanceApplyDto.getBankId();
        String byId = uaaFeignClient.getById(bankId);
        Gson gsonBank = new Gson();
        JsonObject jsonObjectBank = JsonParser.parseString(byId).getAsJsonObject();
        JsonElement dataElementBank = jsonObjectBank.get("data");
        UaaUserBankcard userBankcard = gsonBank.fromJson(dataElementBank, UaaUserBankcard.class);
        if (userBankcard == null){
            throw new BusinessException("银行卡不存在");
        }
        synchronized (userLocks){
            try {
                // 4. 扣减用户余额
                String sourceUaaId = uaaFeignClient.getByUaaId(applyUser);
                UaaUserWallet sourceWallet = getWallet(sourceUaaId);
                BigDecimal sourceBalance = sourceWallet.getBalance();
                uaaFeignClient.decreaseBalance(applyUser, balance);
                String targetUaaId = uaaFeignClient.getByUaaId(applyUser);
                UaaUserWallet targetWallet = getWallet(targetUaaId);
                BigDecimal targetBalance = targetWallet.getBalance();
                if (!validatePointsCashCondition(balance,sourceBalance,targetBalance)){
                    throw new BusinessException("退还失败");
                }
                // 5.根据是否达到金额下限决定是否需要审核
                if (balance.compareTo(MIN_AUDIT_BALANCE) > 0) {
                    balanceApply.setAuditStatus(AuditStatusEnum.PENDING.getDescription());// 待审核
                }else{
                    balanceApply.setAuditStatus(AuditStatusEnum.APPROVED.getDescription());// 无需审核
                    //调用第三方汇款接口
                }
                recordBalanceChangeOutcome(applyUser, balance,balanceApplyDto.getReason(), now);
                // 6.创建余额提现申请表
                balanceApply.setCreateTime(now);
                balanceApply.setUpdateTime(now);
                balanceApply.setIsDelete(0);
                String applyNo = ProfitUserNoUtil.generate("BA");
                balanceApply.setApplyNo(applyNo);
                balanceApply.setApplyUser(applyUser);
                balanceApplyDao.add(balanceApply);
            }catch (Exception e){
                // 扣减失败，回滚提现申请,退还余额,并更改状态
                BigDecimal sourceBalance = getBalance(applyUser);
                uaaFeignClient.increaseBalance(balanceApplyDto.getApplyUser(), balanceApplyDto.getBalance());
                BigDecimal targetBalance = getBalance(applyUser);
                if (!validatePointsCashCondition(balance,sourceBalance,targetBalance)){
                    throw new BusinessException("退还失败");
                }
                recordBalanceChangeIncome(applyUser, balanceApplyDto.getBalance(),balanceApplyDto.getApplyNo(),FAIL_REMARK, now);
                balanceApply.setAuditStatus(AuditStatusEnum.PENDING.getDescription());
                balanceApplyDao.add(balanceApply);
                throw e;
            }
        }

    }
    /**
     * 校验积分兑现条件
     */
    private void validatePointsCashCondition(UaaUserWallet userWallet, BigDecimal balance) {
        // 校验最低提现余额
        if (balance.compareTo(MIN_BALANCE)<0){
            throw new BusinessException("积分低于最低提现限制");
        }
        // 校验用户余额是否足够
        if (userWallet.getBalance().compareTo(balance)<0) {
            throw new BusinessException("用户余额不足");
        }
        // 校验最高兑现余额
        if (balance.compareTo(MAX_BALANCE)>0) {
            throw new BusinessException("积分高于最高提现限制");
        }
    }
    /**
     * 获取用户余额提现申请列表
     * @param pageQuery
     * @return
     */
    public PageResult<BalanceApply> getPage(PageQuery<BalanceApplyDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<BalanceApply> page = balanceApplyDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    @Transactional(rollbackFor = Exception.class)
    public void audit(BalanceApplyAuditDto balanceApplyAuditDto, Long auditUser) {
        Long id = balanceApplyAuditDto.getId();
        Date now = new Date();
        String auditStatus = balanceApplyAuditDto.getAuditStatus();
        String reason = balanceApplyAuditDto.getReason();
        //查询余额兑现申请
        BalanceApply balanceApply = balanceApplyDao.getInfo(id);
        Long applyUser = balanceApply.getApplyUser();
        if (balanceApply == null){
            throw new BusinessException("申请不存在");
        }
        //校验申请状态
        if (!balanceApply.getAuditStatus().equals(AuditStatusEnum.PENDING.getDescription())){
            throw new BusinessException("申请已处理");
        }
        //校验审核状态
        if (!(auditStatus.equals(AuditStatusEnum.APPROVED.getDescription())||auditStatus.equals(AuditStatusEnum.REJECTED.getDescription()))){
            throw new BusinessException("审核状态有误");
        }
        synchronized (applyLocks){
            try {
                //更新申请表申请状态
                balanceApply.setUpdateTime(now);
                balanceApply.setReason(reason);
                balanceApply.setAuditStatus(auditStatus);
                balanceApplyDao.update(balanceApply);
                //更新审核表申请状态
                BalanceCashApplyAudit applyAudit = new BalanceCashApplyAudit();
                applyAudit.setAuditUser(auditUser);
                applyAudit.setApplyNo(balanceApply.getApplyNo());
                applyAudit.setReason(reason);
                applyAudit.setIsDelete(0);
                applyAudit.setUpdateTime(now);
                applyAudit.setAuditStatus(auditStatus);
                updateAudits(applyAudit,balanceApply, auditUser);
                //计算兑现金额(审核通过)
                if (auditStatus.equals(AuditStatusEnum.APPROVED.getDescription())){
                    //审核通过，开始打款
                    applyAudit.setAuditStatus(AuditStatusEnum.COMPLETED.getDescription());
                    balanceApply.setAuditStatus(AuditStatusEnum.COMPLETED.getDescription());
                    balanceApplyDao.update(balanceApply);
                    updateAudits(applyAudit,balanceApply,applyUser);
                } else{
                    // 4.2 审核拒绝，退还余额
                    BigDecimal sourceBalance = getBalance(applyUser);
                    uaaFeignClient.increaseBalance(applyUser, balanceApply.getBalance());
                    BigDecimal targetBalance = getBalance(applyUser);
                    if (!validatePointsCashCondition(balanceApply.getBalance(),sourceBalance,targetBalance)){
                        throw new BusinessException("退还失败");
                    }
                    //记录余额变动
                    recordBalanceChangeIncome(applyUser, balanceApply.getBalance(), balanceApply.getApplyNo(), reason, now);
                }
            }catch (Exception e){
                // 审核失败,回滚提现申请,退还余额,并更改状态
                BigDecimal sourceBalance = getBalance(applyUser);
                uaaFeignClient.increaseBalance(applyUser, balanceApply.getBalance());
                BigDecimal targetBalance = getBalance(applyUser);
                if (!validatePointsCashCondition(balanceApply.getBalance(),sourceBalance,targetBalance)){
                    throw new BusinessException("回滚失败");
                }
                //记录余额变动
                recordBalanceChangeIncome(applyUser, balanceApply.getBalance(), balanceApply.getApplyNo(), reason, now);
                balanceApply.setAuditStatus(AuditStatusEnum.PENDING.getDescription());
                throw e;
            }
        }

    }
    /**
     * 创建订单
     *
     * @param createOrderDto
     * @return
     */
    public void createOrder(CreateOrderDto createOrderDto, Long rechargeUser) {
        // 1. 校验充值金额
        BigDecimal payAmount = createOrderDto.getPayAmount();
        if (payAmount.compareTo(MIN_RECHARGE_AMOUNT) <= 0){
            throw new BusinessException( "充值金额不能小于" + MIN_RECHARGE_AMOUNT);
        }
        // 2. 创建充值订单
        Date now = new Date();
        BalanceRechargeOrder balanceRechargeOrder = new BalanceRechargeOrder();
        balanceRechargeOrder.setOrderNo(ProfitUserNoUtil.generate("BR"));
        balanceRechargeOrder.setCreateTime(now);
        balanceRechargeOrder.setUpdateTime(now);
        balanceRechargeOrder.setIsDelete(0);
        balanceRechargeOrder.setRechargeUser(rechargeUser);
        balanceRechargeOrder.setPayAmount(payAmount);
        balanceRechargeOrder.setOrderStatus(PayStatusEnum.UNPAID.getCode());
        balanceRechargeOrder.setOrderName("余额充值");
        balanceRechargeOrder.setPayType(createOrderDto.getPayType());
        balanceRechargeOrderDao.add(balanceRechargeOrder);
    }
    private  boolean pay(BalanceRechargeOrder balanceRechargeOrder) {
        // 模拟支付
        // 示例返回成功
        return true;
    }

    /**
     * 获取余额充值订单列表
     * @param pageQuery
     * @return
     */
    public PageResult<BalanceRechargeOrderVo> getBalancePage(PageQuery<BalanceRechargeOrderDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<BalanceRechargeOrderVo> page = balanceRechargeOrderDao.getBalancePage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }

    public void handlePayCallback(String orderNo,Long rechargeUser) {
        String byUaaId = uaaFeignClient.getByUaaId(rechargeUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        BalanceRechargeOrder balanceRechargeOrder = balanceRechargeOrderDao.getInfoByOrderNo(orderNo);
        Date now = new Date();
        if (balanceRechargeOrder == null || balanceRechargeOrder.getOrderStatus() != PayStatusEnum.UNPAID.getCode()) { // 非待支付状态
            throw new BusinessException("订单不存在或已支付");
        }
        ProfitPayRecord profitPayRecord = profitPayRecordDao.selectByOrderNo(orderNo);
        if (profitPayRecord.getPayStatus() == PayStatusEnum.SUCCESS.getCode()){
            try {
                //更改订单状态
                balanceRechargeOrder.setOrderStatus(PayStatusEnum.SUCCESS.getCode());
                balanceRechargeOrder.setId(balanceRechargeOrder.getId());
                balanceRechargeOrderDao.update(balanceRechargeOrder);
                // 增加余额
                BigDecimal sourceBalance = getBalance(rechargeUser);
                uaaFeignClient.increaseBalance(rechargeUser, balanceRechargeOrder.getPayAmount());
                BigDecimal targetBalance = getBalance(rechargeUser);
                if (!validatePointsCashCondition(balanceRechargeOrder.getPayAmount(),sourceBalance,targetBalance)){
                    throw new BusinessException("余额处理失败");
                }
                //记录余额变动
                recordBalanceChangeIncome(rechargeUser, balanceRechargeOrder.getPayAmount(), balanceRechargeOrder.getOrderNo(), balanceRechargeOrder.getReason(), now);
            }catch (Exception e){
                // 余额增加失败，回滚订单状态
                balanceRechargeOrder.setOrderStatus(PayStatusEnum.CANCELLED.getCode()); // 余额处理失败
                balanceRechargeOrderDao.update(balanceRechargeOrder);
                throw new BusinessException("余额处理失败");
            }

        }else{
            balanceRechargeOrder.setOrderStatus(PayStatusEnum.FAILED.getCode());
            balanceRechargeOrderDao.update(balanceRechargeOrder);
        }
    }
    private void updateAudits(BalanceCashApplyAudit applyAudit,BalanceApply balanceApply, Long auditUser) {
        List<BalanceCashApplyAudit> applyAudits = balanceApplyAuditDao.getInfoByApplyNo(balanceApply.getApplyNo());
        if (applyAudits != null && applyAudits.size() > 0) {
            log.info("有多个相同编号的审核信息");
            for (BalanceCashApplyAudit applyAudit1 : applyAudits){
                if (applyAudit1.getAuditUser().equals(auditUser)){
                    log.info("有i相同uuid的记录");
                    applyAudit.setId(applyAudit1.getId());
                    balanceApplyAuditDao.update(applyAudit);
                }else {
                    log.info("无i相同uuid的记录");
                    applyAudit.setCreateTime(new Date());
                    balanceApplyAuditDao.add(applyAudit);
                }
            }
        }else{
            log.info("首次审核");
            applyAudit.setCreateTime(new Date());
            balanceApplyAuditDao.add(applyAudit);
        }
    }
    //记录余额变动(只能收入)
    private void recordBalanceChangeIncome(Long applyUser,BigDecimal Amount,String ApplyNo,String reason,Date now) {
        BalanceRecord balanceRecord = new BalanceRecord();
        balanceRecord.setUaaId(applyUser);
        balanceRecord.setBalance(Amount);
        balanceRecord.setRecordType(0);
        balanceRecord.setBalanceSource(1);
        balanceRecord.setSourceId(ApplyNo);
        balanceRecord.setRemark(reason);
        balanceRecord.setCreateTime(now);
        balanceRecord.setUpdateTime(now);
        balanceRecord.setIsDelete(0);
        balanceRecordDao.add(balanceRecord);
    }
    //记录余额变动(只能支出)
    private void recordBalanceChangeOutcome(Long applyUser,BigDecimal Amount,String reason,Date now) {
        BalanceRecord balanceRecord = new BalanceRecord();
        balanceRecord.setUaaId(applyUser);
        balanceRecord.setBalance(Amount);
        balanceRecord.setRecordType(1);
        balanceRecord.setRemark(reason);
        balanceRecord.setCreateTime(now);
        balanceRecord.setUpdateTime(now);
        balanceRecord.setIsDelete(0);
        balanceRecordDao.add(balanceRecord);
    }
    //反序列化
    private UaaUserWallet getWallet(String wallet) {
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(wallet).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        return gson.fromJson(dataElement, UaaUserWallet.class);
    }
    //验证流程
    private Boolean validatePointsCashCondition(BigDecimal balance, BigDecimal sourceBalance,BigDecimal targetBalance) {
        if (sourceBalance.compareTo(targetBalance)>0){
            return sourceBalance.subtract(targetBalance).compareTo(balance) == 0;
        }else {
            return targetBalance.subtract(sourceBalance).compareTo(balance) == 0;
        }

    }
    //获取金额
    private BigDecimal getBalance(Long applyUser) {
        String sourceUaaId = uaaFeignClient.getByUaaId(applyUser);
        UaaUserWallet sourceWallet = getWallet(sourceUaaId);
        BigDecimal sourceBalance = sourceWallet.getBalance();
        return sourceBalance;
    }
}
