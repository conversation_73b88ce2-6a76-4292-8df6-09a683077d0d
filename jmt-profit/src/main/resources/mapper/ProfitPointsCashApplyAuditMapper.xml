<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitPointsCashApplyAuditMapper">

    <sql id="Base_Column_List">
        id,auditUser,applyNo,auditStatus,reason,createTime,
        updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitPointsCashApplyAudit">
        select
        <include refid="Base_Column_List" />
        from profit_points_cash_apply_audit
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_points_cash_apply_audit
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitPointsCashApplyAudit" useGeneratedKeys="true">
        insert into profit_points_cash_apply_audit
        ( id,auditUser,applyNo,auditStatus,reason,createTime,
          updateTime,isDelete)
        values (#{id},#{auditUser},#{applyNo},#{auditStatus},#{reason},#{createTime},
                #{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitPointsCashApplyAudit" useGeneratedKeys="true">
        insert into profit_points_cash_apply_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="auditUser != null">auditUser,</if>
            <if test="applyNo != null">applyNo,</if>
            <if test="auditStatus != null">auditStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitPointsCashApplyAudit">
        update profit_points_cash_apply_audit
        <set>
            <if test="auditUser != null">
                auditUser = #{auditUser},
            </if>
            <if test="applyNo != null">
                applyNo = #{applyNo},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitPointsCashApplyAudit">
        update profit_points_cash_apply_audit
        set
            auditUser =  #{auditUser},
            applyNo =  #{applyNo},
            auditStatus =  #{auditStatus},
            reason =  #{reason},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>
