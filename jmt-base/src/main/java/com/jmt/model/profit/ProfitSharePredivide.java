package com.jmt.model.profit;

import lombok.Data;

import java.util.Date;

/**
 * 收益预分润表
 * @TableName profit_share_predivide
 */
@Data
public class ProfitSharePredivide {
    /**
     * 主键
     */
    private Long id;

    /**
     * 来自所有收益订单的orderNo
     */
    private String orderNo;

    /**
     * 收益分润项目名称
     */
    private String projectName;

    /**
     * 收益总积分
     */
    private Integer profitTotalPoints;

    /**
     * 运营商编号 jhh_operator_info的operatorNo
     */
    private String operatorNo;

    /**
     * 设备收益人编号 jhh_investor_info的investorNo
     */
    private String investorNo;

    /**
     * 商家编码 cm_business_info的businessNo
     */
    private String businessNo;

    /**
     * 运营商补贴积分，当shareType=1时填写
     */
    private Integer operatorPoints;

    /**
     * 设备收益人补贴积分，当shareType=1时填写
     */
    private Integer investorPoints;

    /**
     * 平台补贴积分，当shareType=1时填写
     */
    private Integer platformPoints;

    /**
     * 商家补贴积分，当shareType=1时填写
     */
    private Integer businessPoints;

    /**
     * 预分润状态
     0-未分润
     1-已分润
     2-无效分润
     */
    private Integer shareStauts;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

}