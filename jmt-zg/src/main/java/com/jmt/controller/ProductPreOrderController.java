package com.jmt.controller;

import com.github.pagehelper.PageInfo;
import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.ZgProductDeliveryOrder;
import com.jmt.model.zg.ZgProductPreOrder;
import com.jmt.service.ProductPreOrderService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品预订订单管理控制器
 * 提供商品预订订单的分页查询、查看、确认、拒绝以及商品配送单生成功能
 */
@RestController
@RequestMapping("/zg/preOrder/v1")
public class ProductPreOrderController extends BaseController {

    @Resource
    private ProductPreOrderService productPreOrderService;

    /**
     * 分页查询商品预订订单
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 商品预订订单分页数据
     */
    @GetMapping("/page")
    public String getPage(@RequestBody PageQuery<ZgProductPreOrder> pageQuery) {
        PageInfo<ZgProductPreOrder> page = productPreOrderService.getDriverInfoPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 商品预订单查看接口
     * @param orderNo 订单编号
     * @return 商品预订单详细信息
     */
    @GetMapping("/view")
    public String viewPreOrder(@RequestParam String orderNo) {
        ZgProductPreOrder preOrder = productPreOrderService.getPreOrderByOrderNo(orderNo);
        return super.responseSuccess(preOrder, "查询成功");
    }

    /**
     * 商品预订单确认接口
     * @param orderNo 订单编号
     * @return 操作结果
     */
    @PostMapping("/confirm")
    public String confirmPreOrder(@RequestParam String orderNo) {
        boolean result = productPreOrderService.confirmPreOrderByOrderNo(orderNo);
        return result ? super.responseSuccess("订单确认成功") : super.responseFail("订单确认失败");
    }

    /**
     * 商品预订单拒绝接口
     * @param orderNo 订单编号
     * @param reason 拒绝原因
     * @return 操作结果
     */
    @PostMapping("/reject")
    public String rejectPreOrder(@RequestParam String orderNo, @RequestParam String reason) {
        boolean result = productPreOrderService.rejectPreOrderByOrderNo(orderNo, reason);
        return result ? super.responseSuccess("订单拒绝成功") : super.responseFail("订单拒绝失败");
    }

    /**
     * 商品配送单生成接口
     * @param preOrderNo 商品预订单编号
     * @return 生成的商品配送单信息
     */
    @PostMapping("/generateDeliveryOrder")
    public String generateDeliveryOrder(@RequestParam String preOrderNo) {
        ZgProductDeliveryOrder deliveryOrder = productPreOrderService.generateDeliveryOrderByOrderNo(preOrderNo);
        return super.responseSuccess(deliveryOrder, "商品配送单生成成功");
    }
}