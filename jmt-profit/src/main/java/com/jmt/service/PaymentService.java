package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.ProfitBalanceRecordDao;
import com.jmt.dao.ProfitPayRecordDao;
import com.jmt.dao.ProfitPointsRecordDao;
import com.jmt.enums.PayStatusEnum;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.BalancePayDTO;
import com.jmt.model.profit.dto.PointsPayDTO;
import com.jmt.model.profit.dto.ProfitPayRecordDTO;
import com.jmt.model.profit.entity.ProfitBalanceRecord;
import com.jmt.model.profit.entity.ProfitPayRecord;
import com.jmt.model.profit.entity.ProfitPointsRecord;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@Service
public class PaymentService extends BaseService {

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private ProfitBalanceRecordDao profitBalanceRecordDao;

    @Resource
    private ProfitPayRecordDao profitPayRecordDao;

    @Resource
    private ProfitPointsRecordDao profitPointsRecordDao;

    /**
     * 支付流水分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<ProfitPayRecordDTO> getPage(PageQuery<ProfitPayRecordDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ProfitPayRecordDTO profitPayRecordDTO = pageQuery.getQueryData();
        List<ProfitPayRecordDTO> list = profitPayRecordDao.selectByCondition(profitPayRecordDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 余额支付
     */
    @Transactional(rollbackFor = Exception.class)
    public void balancePay(BalancePayDTO dto) {
        if (dto.getBalance() == null || dto.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("金额必须大于0");
        }
        if (StringUtils.isBlank(dto.getOrderNo())) {
            throw new IllegalArgumentException("订单号不能为空");
        }

        try {
            // 验证支付密码
            validatePayPassword(dto.getUaaId(), dto.getPayPassword());
            // 扣减余额
            String decreaseResult = uaaFeignClient.decreaseBalance(dto.getUaaId(), dto.getBalance());
            if (decreaseResult != null && (decreaseResult.contains("失败") || decreaseResult.contains("余额不足"))) {
                throw new RuntimeException("余额扣减失败,余额不足");
            }

            String increaseResult = uaaFeignClient.increaseBalance(dto.getBusUaaId(), dto.getBalance());
            if (increaseResult != null && increaseResult.contains("失败")) {
                throw new RuntimeException("余额增加失败" );
            }

            // 记录支付流水
            ProfitPayRecord payRecord = buildPayRecord(dto.getUaaId(),  dto.getOrderNo(),PayTypeEnum.BALANCE_PAY,PayStatusEnum.SUCCESS);
            payRecord.setPayAmount(dto.getBalance());
            profitPayRecordDao.insert(payRecord);

            // 记录余额收支
            ProfitBalanceRecord balanceRecord = buildBalanceRecord(dto.getUaaId(), dto.getBalance());
            profitBalanceRecordDao.insert(balanceRecord);

        } catch (Exception e) {
            throw new RuntimeException("余额支付处理出现异常: " + e.getMessage(), e);
        }
    }

    /**
     * 积分支付
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void pointsPay(PointsPayDTO dto) {
        if (dto.getPoints() == null || dto.getPoints() <= 0) {
            throw new IllegalArgumentException("金额必须大于0");
        }
        if (StringUtils.isBlank(dto.getOrderNo())) {
            throw new IllegalArgumentException("订单号不能为空");
        }

        try {
            // 验证支付密码
            validatePayPassword(dto.getUaaId(), dto.getPayPassword());

            // 扣减积分
            String decreaseResult = uaaFeignClient.decreasePoints(dto.getUaaId(), dto.getPoints());
            if (decreaseResult != null && (decreaseResult.contains("失败") || decreaseResult.contains("不足"))) {
                throw new RuntimeException("积分扣减失败,积分不足");
            }

            String increaseResult = uaaFeignClient.increasePoints(dto.getBusUaaId(), dto.getPoints());
            if (increaseResult != null && increaseResult.contains("失败")) {
                throw new RuntimeException("积分增加失败" );
            }



            ProfitPayRecord payRecord = buildPayRecord(dto.getUaaId() , dto.getOrderNo(),PayTypeEnum.POINTS_PAY,PayStatusEnum.SUCCESS);
            payRecord.setPayAmount(new BigDecimal(dto.getPoints()));
            profitPayRecordDao.insert(payRecord);

            // 记录积分收支
            ProfitPointsRecord pointsRecord = buildPointsRecord(dto.getUaaId(), dto.getPoints());
            profitPointsRecordDao.insert(pointsRecord);

        } catch (Exception e) {
            throw new RuntimeException("余额支付处理出现异常: " + e.getMessage(), e);
        }
    }

    /**
     * 验证支付密码
     * @param uaaId
     * @param payPassword
     */
    private void validatePayPassword(Long uaaId, String payPassword) {
        boolean isValid = uaaFeignClient.validatePayPassword(uaaId, payPassword);
        if (!isValid) {
            throw new RuntimeException("支付密码验证失败");
        }
    }

    private ProfitPayRecord buildPayRecord(Long uaaId, String orderNo, PayTypeEnum payType, PayStatusEnum payStatus) {
        return ProfitPayRecord.builder()
                .seqNo(WorkNoGeneratorUtil.generate("PEQ"))
                .orderNo(orderNo)
                .payUser(uaaId)
                .payType(payType != null ? payType.getCode() : null)
                .payStatus(payStatus != null ? payStatus.getCode() : null)
                .createTime(new Date())
                .updateTime(new Date())
                .isDelete(0)
                .build();
    }

    private ProfitBalanceRecord buildBalanceRecord(Long uaaId, BigDecimal amount) {
        return ProfitBalanceRecord.builder()
                .uaaId(uaaId)
                .balance(amount)
                .recordType(1) // 支出
                .remark("订单支付")
                .createTime(new Date())
                .updateTime(new Date())
                .isDelete(0)
                .build();
    }

    private ProfitPointsRecord buildPointsRecord(Long uaaId, Integer amount) {
        return ProfitPointsRecord.builder()
                .uaaId(uaaId)
                .points(amount)
                .recordType(1) // 支出
                .remark("订单支付")
                .createTime(new Date())
                .updateTime(new Date())
                .isDelete(0)
                .build();
    }

    /**
     * 生成二维码图片字节数组
     * @param content 二维码内容（不能为空）
     * @param width 二维码宽度（像素）
     * @param height 二维码高度（像素）
     * @return 二维码PNG图片的字节数组
     * @throws IllegalArgumentException 如果内容为空或尺寸不合法
     * @throws WriterException 二维码生成失败
     * @throws IOException 流操作异常
     */
    public byte[] generateQRCode(String content, int width, int height)
            throws WriterException, IOException {

        // 参数校验
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("二维码内容不能为空");
        }
        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("二维码尺寸必须大于0");
        }

        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new EnumMap<>(EncodeHintType.class);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8"); // 编码格式
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M); // 中等纠错级别
        hints.put(EncodeHintType.MARGIN, 1); // 推荐保留1像素边距（完全去白边可能影响扫描）

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 生成二维码矩阵
            BitMatrix bitMatrix = new MultiFormatWriter().encode(
                    content,
                    BarcodeFormat.QR_CODE,
                    width,
                    height,
                    hints
            );

            // 转换为PNG格式
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", baos);
            return baos.toByteArray();
        }
    }

}
