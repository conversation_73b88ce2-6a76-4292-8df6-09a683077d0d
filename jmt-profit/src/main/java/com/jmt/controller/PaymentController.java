package com.jmt.controller;

import com.google.gson.JsonObject;
import com.jmt.base.BaseController;
import com.jmt.lkl.LakalaCounterOrderService;
import com.jmt.lkl.LakalaPaymentService;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.BusinessException;
import com.jmt.model.profit.dto.*;
import com.jmt.service.PaymentService;
import com.jmt.service.ProfitPayRecordService;
import com.jmt.util.LakalaResponseUtil;
import com.jmt.util.LoginUserUtil;
import com.lkl.laop.sdk.request.V3CcssCounterOrderCloseRequest;
import com.lkl.laop.sdk.request.V3CcssCounterOrderSpecialCreateRequest;
import com.lkl.laop.sdk.request.V3LabsTransPreorderRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/profit/payment")
public class PaymentController extends BaseController {
    @Resource
    private PaymentService paymentService;

    @Resource
    private LakalaCounterOrderService lakalaCounterOrderService;

    @Resource
    private ProfitPayRecordService profitPayRecordService;

    @Resource
    private LakalaPaymentService lakalaPaymentService;

    /**
     * 支付流水列表
     * @param pageQuery
     * @return
     */
    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<ProfitPayRecordDTO> pageQuery){
        PageResult<ProfitPayRecordDTO> pageResult = paymentService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 余额支付
     * @param dto
     * @param req
     * @return
     */
    @PostMapping("/v1/balancePay")
    public String balancePay(@RequestBody BalancePayDTO dto, HttpServletRequest  req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            dto.setUaaId(loginUser.getUaaId());
            paymentService.balancePay(dto);
            return super.responseSuccess("余额支付成功");
        } catch (RuntimeException e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 积分支付
     * @param dto
     * @param req
     * @return
     */
    @PostMapping("/v1/pointsPay")
    public String pointsPay(@RequestBody PointsPayDTO dto, HttpServletRequest  req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            dto.setUaaId(loginUser.getUaaId());
            paymentService.pointsPay(dto);
            return super.responseSuccess("余额支付成功");
        } catch (RuntimeException e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 拉卡拉收银台 创建订单
     * @param dto
     * @return
     */
    @PostMapping("/v1/lklCreateOrder")
    public String counterCreateOrder(@RequestBody CreateCounterOrderDTO dto, HttpServletRequest req) {
        //插入支付流水
//        LoginUaaUser loginUser = LoginUserUtil.get(req);
//        ProfitPayRecord profitPayRecord = new ProfitPayRecord();
//        profitPayRecord.setPayUser(loginUser.getUaaId());
//        profitPayRecord.setOrderNo(dto.getOrderNo());
//
//        // TODO: 支付方式确认
//        if ("ALIPAY".equalsIgnoreCase(dto.getPayType())) {
//            profitPayRecord.setPayType(PayTypeEnum.ALIPAY_PAY.getCode());
//        } else if ("WECHAT".equalsIgnoreCase(dto.getPayType())) {
//            profitPayRecord.setPayType(PayTypeEnum.WECHAT_PAY.getCode());
//        }
//        profitPayRecord.setPayAmount(BigDecimal.valueOf(dto.getAmount()).divide(BigDecimal.valueOf(100)));
//        profitPayRecordService.insert(profitPayRecord);

        try {
            V3CcssCounterOrderSpecialCreateRequest createRequest =
                    lakalaCounterOrderService.createBaseOrderRequest(
                            dto.getOrderNo(),
                            dto.getMerchantCode(),
                            dto.getAmount(),
                            dto.getOrderTime(),
                            dto.getNotifyUrl(),
                            dto.getPayType()
                    );

            // 设置商品信息
            lakalaCounterOrderService.setGoodsInfo(
                    createRequest,
                    dto.getGoodsName(),
                    dto.getGoodsAmt(),
                    dto.getGoodsPricingUnit(),
                    dto.getGoodsNum(),
                    dto.getPlatformType(),
                    dto.getPlatformName(),
                    dto.getGoodsType()
            );

            // 设置可选参数
            if (StringUtils.isNotBlank(dto.getShopName())) {
                createRequest.setShopName(dto.getShopName());
            }
            if (StringUtils.isNotBlank(dto.getCounterRemark())) {
                createRequest.setCounterRemark(dto.getCounterRemark());
            }
            if (StringUtils.isNotBlank(dto.getBusiTypeParam())) {
                createRequest.setBusiTypeParam(dto.getBusiTypeParam());
            }

            String response = lakalaCounterOrderService.createOrder(createRequest);

            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkSuccess(responseObj);

            String counterUrl = LakalaResponseUtil.getCounterUrl(responseObj);
            return responseSuccess(counterUrl,"创建成功");
        } catch (BusinessException e) {
            return responseFail(e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 拉卡拉收银台 关单
     * @param dto
     * @return
     */
    @PostMapping("/v1/lklCloseOrder")
    public String closeOrder(@RequestBody LklCounterOrderDTO dto) {
        try {
            V3CcssCounterOrderCloseRequest request = LakalaCounterOrderService.createCloseRequest(
                    dto.getMerchantNo(),dto.getOutOrderNo(),dto.getChannelId()
            );

            String response = lakalaCounterOrderService.closeOrder(request);
            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkSuccess(responseObj);
            return responseSuccess(responseObj,"关单成功");
        } catch (BusinessException e) {
            return responseFail(e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 拉卡拉收银台 订单查询
     * @param dto
     * @return
     */
    @PostMapping("/v1/lklQueryOrder")
    public String queryOrderByOutOrderNo(@RequestBody LklCounterOrderDTO dto) {
        try {
            String response = LakalaCounterOrderService.queryByOutOrderNo(
                    dto.getMerchantNo(),dto.getOutOrderNo(),dto.getChannelId());

            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkSuccess(responseObj);
            return responseSuccess(responseObj,"查询成功");
        } catch (BusinessException e) {
            return responseFail(e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 拉卡拉 微信支付
     * @param dto
     * @return
     */
    @PostMapping("/v1/lklWechatPay")
    public String lklWechatPay(@RequestBody LklWechatPayDTO dto) {
        try {
            V3LabsTransPreorderRequest request = lakalaPaymentService.createWechatPaymentRequest(dto);
            String response = lakalaPaymentService.executePayment(request);

            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkBBSSuccess(responseObj);
            return responseSuccess(responseObj,"微信支付响应");
        } catch (BusinessException e) {
            return responseFail(e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 拉卡拉 支付宝支付
     * @param dto
     * @return
     */
    @PostMapping(value ="/v1/lklAlipay", produces = MediaType.IMAGE_PNG_VALUE)
    public byte[] lklAlipay(@RequestBody LklAlipayDTO dto) throws Exception {
//        try {
            V3LabsTransPreorderRequest request = lakalaPaymentService.createAlipayPaymentRequest(dto);
            String response = lakalaPaymentService.executePayment(request);

            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkBBSSuccess(responseObj);
            String qrCodeUrl = LakalaResponseUtil.getAliQRCodeUrl(responseObj);
            byte[] bytes = paymentService.generateQRCode(qrCodeUrl, 300, 300);
            return bytes;
       //     return responseSuccess(responseObj,"支付宝支付响应");
//        } catch (BusinessException e) {
//            return responseFail(e.getMessage());
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
    }

    /**
     * 拉卡拉 银联支付
     * @param dto
     * @return
     */
    @PostMapping(value="/v1/lklUnionPay", produces = MediaType.IMAGE_PNG_VALUE)
    public byte[] lklUnionPay(@RequestBody LklUnionPayDTO dto) throws Exception {
//        try {
            V3LabsTransPreorderRequest request = lakalaPaymentService.createUnionPayPaymentRequest(dto);
            String response = lakalaPaymentService.executePayment(request);

            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkBBSSuccess(responseObj);
            String qrCodeUrl = LakalaResponseUtil.getUnionQRCodeUrl(responseObj);
            byte[] bytes = paymentService.generateQRCode(qrCodeUrl, 300, 300);
            return bytes;
//            return super.responseSuccess(responseObj,"银联支付响应");
//        } catch (BusinessException e) {
//            return super.responseFail(e.getMessage());
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
    }

}
