<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysResourceDao">
    <resultMap id="BaseResultMap" type="com.jmt.model.admin.resource.SysResource">
        <id property="id" column="id" />
        <result property="resKey" column="resKey" />
        <result property="resName" column="resName" />
        <result property="resUrl" column="resUrl" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>
    <sql id="Base_Column_List">
        id, resKey, resName, resUrl, createTime, updateTime, isDelete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from sys_resource where id = #{id}
    </select>
    <select id="findByResourceIds" resultType="com.jmt.model.admin.resource.SysResource">
        select <include refid="Base_Column_List" /> from sys_resource where isDelete = 0 and id in <foreach collection="resourceIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>
    <select id="selectComboList" parameterType="string" resultMap="BaseResultMap">
        SELECT id, resKey, resName FROM sys_resource WHERE isDelete = 0<if test="keyword != null and keyword != ''"> AND (resName LIKE CONCAT('%', #{keyword}, '%') OR resKey LIKE CONCAT('%', #{keyword}, '%'))</if> ORDER BY resName ASC
    </select>
    <select id="getPage" resultType="com.jmt.model.admin.resource.SysResource" parameterType="com.jmt.model.admin.resource.SysResource">
        SELECT <include refid="Base_Column_List" /> FROM sys_resource t
        <where>
            <if test="id != null">AND t.id = #{id}</if>
            <if test="resKey != null and resKey != ''">AND t.resKey = #{resKey}</if>
            <if test="resName != null and resName != ''">AND t.resName = #{resName}</if>
            <if test="resUrl != null and resUrl != ''">AND t.resUrl = #{resUrl}</if>
            <if test="createTime != null">AND t.createTime >= #{createTime}</if>
            <if test="updateTime != null">AND t.updateTime &lt;= #{updateTime}</if>
            <if test="isDelete != null">AND t.isDelete = #{isDelete}</if>
        </where>
        ORDER BY t.createTime DESC
    </select>
    <select id="selectAllAvailable" resultMap="BaseResultMap">
        SELECT id, resKey, resName, resUrl, createTime, updateTime, isDelete FROM sys_resource WHERE isDelete = 0 ORDER BY createTime DESC
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update sys_resource set isDelete = 1, updateTime = NOW() where id = #{id} and isDelete = 0
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.resource.SysResource" useGeneratedKeys="true">
        insert into sys_resource (id, resKey, resName, resUrl, createTime, updateTime, isDelete) values (#{id}, #{resKey}, #{resName}, #{resUrl}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.resource.SysResource" useGeneratedKeys="true">
        insert into sys_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="resKey != null">resKey,</if>
            <if test="resName != null">resName,</if>
            <if test="resUrl != null">resUrl,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="resKey != null">#{resKey},</if>
            <if test="resName != null">#{resName},</if>
            <if test="resUrl != null">#{resUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.resource.SysResource">
        update sys_resource
        <set>
            <if test="resKey != null">resKey = #{resKey},</if>
            <if test="resName != null">resName = #{resName},</if>
            <if test="resUrl != null">resUrl = #{resUrl},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.resource.SysResource">
        update sys_resource set resKey = #{resKey}, resName = #{resName}, resUrl = #{resUrl}, createTime = #{createTime}, updateTime = #{updateTime}, isDelete = #{isDelete} where id = #{id}
    </update>
</mapper>