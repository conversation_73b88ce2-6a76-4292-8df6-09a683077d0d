package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.cm.dto.CmWorkEqDeployDTO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmWorkEqDeployVO;
import com.jmt.service.CmWorkEqDeployService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/cm/workEqDeploy")
public class CmWorkEqDeployController extends BaseController {
    @Resource
    private CmWorkEqDeployService cmWorkEqDeployService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<CmWorkEqDeployDTO> pageQuery){
        PageResult<CmWorkEqDeployDTO> pageResult = cmWorkEqDeployService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 新增
     * @param cmWorkEqDeploy
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmWorkEqDeployDTO cmWorkEqDeploy) {
        try {
            cmWorkEqDeployService.create(cmWorkEqDeploy);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增工单失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmWorkEqDeployDTO dto) {
        try {
            cmWorkEqDeployService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmWorkEqDeployService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmWorkEqDeployVO cmWorkEqDeploy = cmWorkEqDeployService.getById(id);
        if(cmWorkEqDeploy == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmWorkEqDeploy,"查询成功");
    }


    /**
     * 审核申请工单
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                @RequestParam Integer auditStatus, @RequestParam String reason) {
        try {
            cmWorkEqDeployService.auditBusiness(id, auditStatus,reason);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 部署工单自动确定
     * @param id
     * @return
     */
    @PostMapping("/v1/autoCompleteDeployOrder")
    public String autoCompleteDeployOrder(@RequestParam Long id) {
        try {
            cmWorkEqDeployService.autoCompleteDeployOrder(id);
            return super.responseSuccess("工单完成");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }

    }



}
