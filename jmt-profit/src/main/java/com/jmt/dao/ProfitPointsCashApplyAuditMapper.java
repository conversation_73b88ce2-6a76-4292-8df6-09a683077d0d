package com.jmt.dao;

import com.jmt.model.profit.ProfitPointsCashApplyAudit;

/**
* <AUTHOR>
* @description 针对表【profit_points_cash_apply_audit(积分兑现申请审核记录表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitPointsCashApplyAudit
*/
public interface ProfitPointsCashApplyAuditMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitPointsCashApplyAudit record);

    int insertSelective(ProfitPointsCashApplyAudit record);

    ProfitPointsCashApplyAudit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitPointsCashApplyAudit record);

    int updateByPrimaryKey(ProfitPointsCashApplyAudit record);

}
