package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.profit.dto.ShareConfigDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-profit",path ="/profit",configuration = FeignConfig.class)
public interface ProfitFeignClient {
    @PostMapping(value = "/config/v1/add")
    String add(@RequestBody ShareConfigDto shareConfigDto);
    @PostMapping(value = "/config/v1/deleteByNo")
    String deleteByNo(String operatorNo);
}
