package com.jmt.constant;

/**
 * JMT常量类
 * <AUTHOR>
 */
public class JmtConstant {

    /**
     * 根节点id
     */
    public static final Long PARENT_ID = 0L;

    /**
     * 启用
     */
    public static final int ENABLE = 1;

    /**
     * 禁用
     */
    public static final int DISABLE = 0;

    /**
     * 结果返回0
     */
    public static final int COUNT_ZERO = 0;

    /**
     * 设置查询不分页
     */
    public static final long NO_PAGE = -1;

    /**
     * ZERO_LONG
     */
    public static final Long ZERO_LONG = 0L;

    /**
     * ONE_LONG
     */
    public static final Long ONE_LONG = 1L;

    /**
     * TWO_LONG
     */
    public static final Long TWO_LONG = 2L;

    /**
     * THREE_LONG
     */
    public static final Long THREE_LONG = 3L;

    /**
     * FOUR_LONG
     */
    public static final Long FOUR_LONG = 4L;

    /**
     * FIVE_LONG
     */
    public static final Long FIVE_LONG = 5L;

    /**
     * 数组取值
     */
    public class Number {

        public static final int ZERO = 0;

        public static final int ONE = 1;

        public static final int TWO = 2;

        public static final int THREE = 3;

        public static final int FOUR = 4;

        public static final int FIVE = 5;

        public static final int FIFTEEN = 15;

        public static final int HUNDRED = 100;

        public static final int THOUSAND = 1000;

    }

    public static final String DEFAULT_NICKNAME = "Jmt";

    public static final String DEFAULT_PASSWORD = "Jmt123456";

    public static final String AUTHORIZATION = "Authorization";

    public static final String BEARER = "Bearer";

    public static final String LOGIN_NAME = "loginName";
}

