<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysRolePermissionDao">


    <resultMap id="BaseResultMap" type="com.jmt.model.admin.role.SysRolePermission">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="permissId" property="permissId" jdbcType="BIGINT" /> <!-- 数据库列名permissId -->
        <result column="roleId" property="roleId" jdbcType="BIGINT" /> <!-- 数据库列名roleId -->
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" /> <!-- 数据库列名createTime -->
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" /> <!-- 数据库列名updateTime -->
        <result column="isDelete" property="isDelete" jdbcType="TINYINT" /> <!-- 数据库列名isDelete -->
    </resultMap>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM sys_role_permission
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 插入记录（全字段） -->
    <insert id="insert" parameterType="com.jmt.model.admin.role.SysRolePermission">
        INSERT INTO sys_role_permission (id, permissId, roleId,
                                         createTime, updateTime, isDelete)
        VALUES (#{id,jdbcType=BIGINT}, #{permissId,jdbcType=BIGINT},
                #{roleId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
    </insert>

    <!-- 插入记录（选择性字段） -->
    <insert id="insertSelective" parameterType="com.jmt.model.admin.role.SysRolePermission">
        INSERT INTO sys_role_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="permissId != null">permissId,</if>
            <if test="roleId != null">roleId,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="permissId != null">#{permissId,jdbcType=BIGINT},</if>
            <if test="roleId != null">#{roleId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT * FROM sys_role_permission
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.role.SysRolePermission">
        UPDATE sys_role_permission
        <set>
            <if test="permissId != null">permissId = #{permissId,jdbcType=BIGINT},</if> <!-- 数据库列名permissId -->
            <if test="roleId != null">roleId = #{roleId,jdbcType=BIGINT},</if> <!-- 数据库列名roleId -->
            <if test="createTime != null">createTime = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">updateTime = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">isDelete = #{isDelete,jdbcType=TINYINT},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据主键更新全字段 -->
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.role.SysRolePermission">
        UPDATE sys_role_permission
        SET permissId = #{permissId,jdbcType=BIGINT}, <!-- 数据库列名permissId -->
        roleId = #{roleId,jdbcType=BIGINT}, <!-- 数据库列名roleId -->
        createTime = #{createTime,jdbcType=TIMESTAMP},
        updateTime = #{updateTime,jdbcType=TIMESTAMP},
        isDelete = #{isDelete,jdbcType=TINYINT}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据角色ID查询权限ID列表 -->
    <select id="findByRoleId" resultType="java.lang.Long">
        SELECT permissId FROM sys_role_permission <!-- 数据库列名permissId -->
        WHERE roleId = #{id,jdbcType=BIGINT} <!-- 数据库列名roleId -->
        AND isDelete = 0
    </select>

    <!-- 逻辑删除角色的权限关联 -->
    <update id="updateIsDeleteByRoleId">
        UPDATE sys_role_permission
        SET isDelete = #{isDelete,jdbcType=TINYINT}
        WHERE roleId = #{roleId,jdbcType=BIGINT} <!-- 数据库列名roleId -->
    </update>

    <!-- 批量插入角色权限关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_role_permission (permissId, roleId, createTime, isDelete) <!-- 数据库列名permissId -->
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.permissId,jdbcType=BIGINT},
            #{item.roleId,jdbcType=BIGINT},
            NOW(),
            0)
        </foreach>
    </insert>


    <select id="findByRoleIds" resultType="java.lang.Long">
        SELECT permissId FROM sys_role_permission
        WHERE roleId IN <!-- 数据库列名roleId -->
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId,jdbcType=BIGINT}
        </foreach>
        AND isDelete = 0
    </select>

</mapper>