package com.jmt.dao;

import com.jmt.model.profit.ProfitPayRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【profit_pay_record(支付流水表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitPayRecord
*/
public interface ProfitPayRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitPayRecord record);

    int insertSelective(ProfitPayRecord record);

    ProfitPayRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitPayRecord record);

    int updateByPrimaryKey(ProfitPayRecord record);
    ProfitPayRecord selectBySeqNo(@Param("seqNo") String seqNo);
    int updateBySeqNoSelective(ProfitPayRecord record);

    List<ProfitPayRecord> selectUnprocessedPayRecords(int code);
}
