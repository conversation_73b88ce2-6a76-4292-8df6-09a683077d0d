<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.PointsRecordDao">
	<insert id="insert">
		INSERT INTO profit_points_record
		(uaaId,points,recordType,pointSource,sourceId,remark,createTime,updateTime,isDelete)
		VALUES
		(#{uaaId},#{points},#{recordType},#{pointSource},#{sourceId},#{remark},#{createTime},#{updateTime},#{isDelete})
	</insert>
</mapper>