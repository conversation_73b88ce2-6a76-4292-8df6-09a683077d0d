package com.jmt.controller;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseController;
import com.jmt.client.JhhInvestorFeignClient;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.client.JhhSalesmanFeignClient;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.eq.dto.EqInfoDto;
import com.jmt.model.eq.dto.RequestSendEq;
import com.jmt.model.eq.dto.RequestTrans;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.jhh.dto.InvestorInfo;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.EqInfoService;
import com.jmt.util.LoginUserUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/eq/v1")
@Service
public class EqInfoController extends BaseController {
    @Resource
    private EqInfoService eqInfoService;
    @Resource
    private JhhInvestorFeignClient jhhFeignClient;
    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;
    @Resource
    private JhhSalesmanFeignClient jhhSalesmanFeignClient;
    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<EqInfoDto> pageQuery) {
        PageResult<EqInfoVo> page = eqInfoService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        EqInfoVo eqInfoVo = eqInfoService.getInfo(id);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }
    /**
     * 设备迁移
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/transfer")
    public String transfer(@RequestBody RequestTrans requestTrans) {
        eqInfoService.transfer(requestTrans);
        return super.responseSuccess("迁移成功");
    }
    /**
     * 设备收益人分配设备
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/assign")
    public String assign(@RequestBody RequestSendEq requestSendEq) {
        eqInfoService.assign(requestSendEq);
        return super.responseSuccess("分配成功");
    }
    /**
     * 设备码导入
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/import")
    public String importEq(@RequestParam("file") MultipartFile file) throws IOException {
        eqInfoService.importEq(file);
        return super.responseSuccess("导入成功");
    }
    /**
     * 根据busNo查询设备信息
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/getInfoByBusNo")
    public String getInfoByBusNo(@RequestParam String busNo) {
        List<EqInfoVo> eqInfoVo = eqInfoService.getInfoByBusNo(busNo);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }
    /**
     * 查询收益人设备信息详情
     *
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getInvestorEqInfo")
    public String getInvestorEqInfo(HttpServletRequest request) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long uaaId = loginUaaUser.getUaaId();
        String investorInfoByUaaId = jhhFeignClient.getInvestorInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(investorInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        InvestorInfo investorInfo = gson.fromJson(dataElement,InvestorInfo.class);
        String investorNo = investorInfo.getInvestorNo();
        List<EqInfoVo> eqInfoVo = eqInfoService.getInvestorEqInfo(investorNo);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }
    /**
     * 查询运营商设备信息
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getOperatorEqInfo")
    public String getOperatorEqInfo(HttpServletRequest request) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long uaaId = loginUaaUser.getUaaId();
        String operatorInfoByUaaId= jhhOperatorFeignClient.getOperatorInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(operatorInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        OperatorInfo operatorInfo = gson.fromJson(dataElement, OperatorInfo.class);
        String operatorNo = operatorInfo.getOperatorNo();
        List<EqInfoVo> eqInfoVo = eqInfoService.getOperatorEqInfo(operatorNo);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }
    /**
     * 查询业务员设备信息
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanEqInfo")
    public String getSalesmanEqInfo(HttpServletRequest request) {
        Long uaaId = getNo(request);
        String salesmanInfoByUaaId = jhhSalesmanFeignClient.getSalesmanInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(salesmanInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        SalesmanInfo salesmanInfo = gson.fromJson(dataElement, SalesmanInfo.class);
        String deployerNo = salesmanInfo.getSalesmanNo();
        List<EqInfoVo> eqInfoVo = eqInfoService.getSalesmanEqInfo(deployerNo);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }
    /**
     * 分页查询收益人设备信息列表
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInvestorEqPage")
    public String getInvestorEqPage(@RequestBody PageQuery<EqInfoDto> pageQuery,HttpServletRequest request) {
        Long uaaId = getNo(request);
        String investorInfoByUaaId = jhhFeignClient.getInvestorInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(investorInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        InvestorInfo investorInfo = gson.fromJson(dataElement, InvestorInfo.class);
        String investorNo = investorInfo.getInvestorNo();
        PageResult<EqInfoVo> page = eqInfoService.getInvestorEqPage(pageQuery,investorNo);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 分页查询运营商设备信息列表
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getOperatorEqPage")
    public String getOperatorEqPage(@RequestBody PageQuery<EqInfoDto> pageQuery,HttpServletRequest request) {
        Long uaaId = getNo(request);
        String operatorInfoByUaaId= jhhOperatorFeignClient.getOperatorInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(operatorInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        OperatorInfo operatorInfo = gson.fromJson(dataElement, OperatorInfo.class);
        String operatorNo = operatorInfo.getOperatorNo();
        PageResult<EqInfoVo> page = eqInfoService.getOperatorEqPage(pageQuery,operatorNo);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 分页查询业务员设备信息列表
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanEqPage")
    public String getSalesmanEqPage(@RequestBody PageQuery<EqInfoDto> pageQuery,HttpServletRequest request) {
        Long uaaId = getNo(request);
        String salesmanInfoByUaaId = jhhSalesmanFeignClient.getSalesmanInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(salesmanInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        SalesmanInfo salesmanInfo = gson.fromJson(dataElement, SalesmanInfo.class);
        String deployerNo = salesmanInfo.getSalesmanNo();
        PageResult<EqInfoVo> page = eqInfoService.getSalesmanEqPage(pageQuery,deployerNo);
        return super.responseSuccess(page,"查询成功");
    }
    private Long getNo(HttpServletRequest request) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long uaaId = loginUaaUser.getUaaId();
        return uaaId;
    }
    /**
     * 运营商关联设备解除
     * @param operatorNo
     * @return
     */
    @PostMapping(value = "/unbind")
    public String unbind(@RequestParam String operatorNo) {
        eqInfoService.clearOperatorEq(operatorNo);
        return super.responseSuccess("解除成功");
    }
    /**
     * 解除收益人设备关联
     * @param operatorNo
     * @return
     */
    @PostMapping(value = "/unbindInvestor")
    public String unbindInvestor(@RequestParam String operatorNo) {
        eqInfoService.clearInvestorEq(operatorNo);
        return super.responseSuccess("解除成功");
    }
}
