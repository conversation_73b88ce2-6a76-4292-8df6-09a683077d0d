<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysUserDao">
	<resultMap id="BaseResultMap" type="com.jmt.model.admin.user.SysUser">
		<id property="id" column="id" />
		<result property="userName" column="userName" />
		<result property="uaaId" column="uaaId" />
		<result property="isFirstLogin" column="isFirstLogin" />
		<result property="createTime" column="createTime" />
		<result property="updateTime" column="updateTime" />
		<result property="isDelete" column="isDelete" />
	</resultMap>
	<sql id="Base_Column_List">
		id, userName, uaaId, isFirstLogin, createTime, updateTime, isDelete
	</sql>
	<select id="getPage" parameterType="com.jmt.model.admin.user.SysUser" resultType="com.jmt.model.admin.user.SysUser">
		SELECT * FROM sys_user t
		<where>
			<if test="isDelete == null">AND t.isDelete = 0</if>
			<if test="isDelete != null">AND t.isDelete = #{isDelete}</if>
			<if test="userName != null and userName != ''">AND t.userName LIKE CONCAT('%', #{userName}, '%')</if>
			<if test="id != null">AND t.id = #{id}</if>
			<if test="uaaId != null">AND t.uaaId = #{uaaId}</if>
			<if test="isFirstLogin != null">AND t.isFirstLogin = #{isFirstLogin}</if>
			<if test="createTime != null">AND t.createTime &gt;= #{createTime}</if>
			<if test="updateTime != null">AND t.updateTime &lt;= #{updateTime}</if>
		</where>
		ORDER BY t.createTime DESC
	</select>
	<select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.admin.user.SysUser">
		select * from sys_user t where t.id = #{userId} AND t.isDelete = 0
	</select>
	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from sys_user where id = #{id} AND isDelete = 0
	</select>
	<update id="deleteByPrimaryKey" parameterType="com.jmt.model.admin.user.SysUser">
		update sys_user set isDelete = #{isDelete}, updateTime = #{updateTime} where id = #{id}
	</update>
	<insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.user.SysUser" useGeneratedKeys="true">
		insert into sys_user (id, userName, uaaId, isFirstLogin, createTime, updateTime, isDelete) values (#{id}, #{userName}, #{uaaId}, #{isFirstLogin}, #{createTime}, #{updateTime}, #{isDelete})
	</insert>
	<insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.user.SysUser" useGeneratedKeys="true">
		insert into sys_user
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="userName != null and userName != ''">userName,</if>
			<if test="uaaId != null">uaaId,</if>
			<if test="isFirstLogin != null">isFirstLogin,</if>
			<if test="createTime != null">createTime,</if>
			<if test="updateTime != null">updateTime,</if>
			<if test="isDelete != null">isDelete,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="userName != null and userName != ''">#{userName},</if>
			<if test="uaaId != null">#{uaaId},</if>
			<if test="isFirstLogin != null">#{isFirstLogin},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="isDelete != null">#{isDelete},</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.user.SysUser">
		update sys_user
		<set>
			<if test="userName != null and userName != ''">userName = #{userName},</if>
			<if test="uaaId != null">uaaId = #{uaaId},</if>
			<if test="isFirstLogin != null">isFirstLogin = #{isFirstLogin},</if>
			<if test="createTime != null">createTime = #{createTime},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="isDelete != null">isDelete = #{isDelete},</if>
		</set>
		where id = #{id}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.user.SysUser">
		update sys_user set userName = #{userName}, uaaId = #{uaaId}, isFirstLogin = #{isFirstLogin}, createTime = #{createTime}, updateTime = #{updateTime}, isDelete = #{isDelete} where id = #{id}
	</update>
	<select id="selectComboList" parameterType="string" resultType="com.jmt.model.admin.user.SysUser">
		SELECT id, userName FROM sys_user WHERE isDelete = 0<if test="keyword != null and keyword != ''"> AND userName LIKE CONCAT('%', #{keyword}, '%')</if> ORDER BY userName ASC
	</select>
	<update id="batchUpdateIsDelete" parameterType="map">
		UPDATE sys_user SET isDelete = #{isDelete}, updateTime = #{updateTime} WHERE id IN <foreach collection="userIds" item="userId" open="(" separator="," close=")">#{userId}</foreach> AND isDelete = 0
	</update>
</mapper>