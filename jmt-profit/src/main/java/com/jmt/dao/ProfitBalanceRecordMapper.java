package com.jmt.dao;

import com.jmt.model.profit.ProfitBalanceRecord;

/**
* <AUTHOR>
* @description 针对表【profit_balance_record(余额收支记录表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitBalanceRecord
*/
public interface ProfitBalanceRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitBalanceRecord record);

    int insertSelective(ProfitBalanceRecord record);

    ProfitBalanceRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitBalanceRecord record);

    int updateByPrimaryKey(ProfitBalanceRecord record);

}
