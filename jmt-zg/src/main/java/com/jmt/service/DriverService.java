package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.ZgDriverInfoAuditMapper;
import com.jmt.dao.ZgDriverInfoMapper;
import com.jmt.dao.ZgUserMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.zg.*;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class DriverService extends BaseService {
    @Resource
    private ZgDriverInfoMapper driverInfoMapper;
    @Resource
    private ZgDriverInfoAuditMapper driverAuditMapper;
    @Resource
    private ZgUserMapper userMapper;
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private PasswordEncoder passwordEncoder;

    /**
     * 启用司机信息（恢复正常状态）
     */
    public void enableDriverInfo(Long id) {
        ZgDriverInfo driverInfo = driverInfoMapper.selectByUserIdKey(id);
        driverInfo.setIsDelete(0);
        driverInfo.setUpdateTime(new Date());
        driverInfoMapper.updateByPrimaryKeySelective(driverInfo);
    }

    /**
     * 禁用司机信息（逻辑删除）
     */
    public void disableDriverInfo(Long id) {
        ZgDriverInfo driverInfo = driverInfoMapper.selectByUserIdKey(id);
        driverInfo.setIsDelete(1);
        driverInfo.setUpdateTime(new Date());
        driverInfoMapper.updateByPrimaryKeySelective(driverInfo);
    }

    /**
     * 司机信息分页查询
     */
    public PageResult<ZgDriverInfo> getDriverInfoPage(PageQuery<ZgDriverInfo> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgDriverInfo queryData = pageQuery.getQueryData();
        List<ZgDriverInfo> list = driverInfoMapper.getDriverInfoPage(queryData);
        return new PageResult<>(list);
    }

    /**
     * 根据用户ID查询司机信息
     */
    public ZgDriverInfo getDriverInfo(Long id) {
        return driverInfoMapper.selectByUserIdKey(id);
    }

    /**
     * 根据司机编号查询司机信息
     */
    public ZgDriverInfo getDriverInfo(String driverNo) {
        return driverInfoMapper.selectByDriverNo(driverNo);
    }

    /**
     * 申请成为司机（创建用户、司机信息及审核记录）
     */
    public void beDriver(DriverAndAuditUserDto dto) {
        Date now = new Date();

        // 创建用户信息
        ZgUser user = new ZgUser();
        user.setUserName(dto.getDrivername());
        user.setNickName(dto.getDrivername());
        user.setCreateTime(now);
        user.setUpdateTime(now);
        user.setRoleType(2); // 角色类型：司机
        user.setIsDelete(0);
        user.setCountry(dto.getCountry());
        user.setProvince(dto.getProvince());
        user.setCity(dto.getCity());
        user.setAddress(dto.getAddress());
        user.setTelPhone(dto.getTelphone());
        user.setIsFirstLogin(1);
        userMapper.insert(user);

        // 创建司机信息
        ZgDriverInfo driverInfo = new ZgDriverInfo();
        driverInfo.setUserId(user.getId());
        driverInfo.setCreateTime(now);
        driverInfo.setUpdateTime(now);
        driverInfo.setDriverNo(dto.getDriverno());
        driverInfo.setDriverName(dto.getDrivername());
        driverInfo.setIdNo(dto.getIdno());
        driverInfo.setAuditStatus(0); // 待审核
        driverInfo.setTelPhone(dto.getTelphone());
        driverInfo.setAreaScope(dto.getAreascope());
        driverInfo.setCarPhoto(dto.getCarphoto());
        driverInfo.setCarNumber(dto.getCarnumber());
        driverInfo.setMaxLoad(dto.getMaxload());
        driverInfo.setDriverStar(dto.getDriverstar());
        driverInfo.setIsRealAuth(dto.getIsrealauth());
        driverInfo.setCountry(dto.getCountry());
        driverInfo.setProvince(dto.getProvince());
        driverInfo.setCity(dto.getCity());
        driverInfo.setAddress(dto.getAddress());
        driverInfo.setLatitude(dto.getLatitude());
        driverInfo.setLongitude(dto.getLongitude());
        driverInfo.setRemark(dto.getRemark());
        driverInfo.setIsDelete(0);
        driverInfoMapper.insertSelective(driverInfo);

        // 创建审核记录
        ZgDriverInfoAudit audit = new ZgDriverInfoAudit();
        audit.setAuditUser(dto.getUserId());
        audit.setDriverNo(dto.getDriverno());
        audit.setCreateTime(now);
        audit.setUpdateTime(now);
        audit.setAuditStatus("0"); // 待审核
        audit.setIsDelete(0);
        driverAuditMapper.insertSelective(audit);
    }

    /**
     * 审核司机信息（更新状态，审核通过时同步注册到UAA）
     */
    public Integer auditDriverInfo(String driverNo, Integer auditStatus, String reason, Long uaaId) {
        // 查询审核记录和司机信息
        ZgDriverInfoAudit audit = driverAuditMapper.selectByDriverNo(driverNo);
        ZgDriverInfo driverInfo = driverInfoMapper.selectByDriverNo(driverNo);

        // 设置审核备注
        if (reason != null && !reason.isEmpty()) {
            driverInfo.setReason(reason);
            audit.setReason(reason);
        }

        // 更新司机信息状态
        driverInfo.setUpdateTime(new Date());
        driverInfo.setAuditStatus(auditStatus);
        driverInfoMapper.updateByPrimaryKeySelective(driverInfo);

        // 更新审核记录
        audit.setAuditUser(uaaId);
        audit.setUpdateTime(new Date());
        audit.setAuditStatus(String.valueOf(auditStatus));

        // 审核通过时，注册到UAA
        if (auditStatus == 1) {
            UaaUserDTO uaaUser = new UaaUserDTO();
            uaaUser.setLoginName(driverInfo.getDriverName());
            uaaUser.setNickname(JmtConstant.DEFAULT_NICKNAME);
            uaaUser.setPassword(passwordEncoder.encode(JmtConstant.DEFAULT_PASSWORD));
            uaaUser.setCountry(driverInfo.getCountry());
            uaaUser.setProvince(driverInfo.getProvince());
            uaaUser.setCity(driverInfo.getCity());
            uaaUser.setAddress(driverInfo.getAddress());

            // 调用UAA服务注册用户，并更新本地用户的uaaId
            String registerResp = uaaFeignClient.registerUser(uaaUser);
            JsonObject respJson = JsonParser.parseString(registerResp).getAsJsonObject();
            UaaUser user = new Gson().fromJson(respJson.get("data"), UaaUser.class);

            ZgUser localUser = userMapper.selectByPrimaryKey(driverInfo.getUserId());
            localUser.setUaaId(user.getUaaId());
            userMapper.updateByPrimaryKeySelective(localUser);
        }

        return driverAuditMapper.updateByPrimaryKeySelective(audit);
    }

    /**
     * 获取司机下拉列表（id和名称）
     */
    public List<DropdownVO> getDriverIdAndName() {
        List<Map<String, String>> driverMaps = driverInfoMapper.getDriverNoAndName();
        List<DropdownVO> result = new ArrayList<>();
        int seq = 1;
        for (Map<String, String> map : driverMaps) {
            result.add(new DropdownVO(seq++, map.get("driverNo"), map.get("driverName")));
        }
        return result;
    }
}