<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.OperatorDao">
    <select id="getInfo" resultType="com.jmt.model.jhh.dto.OperatorInfo">
        SELECT uaaId,operatorName,operatorNo,headImg,contractNo,dutyName,telPhone,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_operator_info WHERE id = #{id}
        AND isDelete = 0
    </select>
    <select id="getPage" resultType="com.jmt.model.jhh.vo.OperatorVo">
        SELECT uaaId,operatorName,operatorNo,headImg,contractNo,dutyName,telPhone,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_operator_info
        <where>
            <if test="queryData != null and queryData.operatorNo!=null">
                and operatorNo LIKE CONCAT('%',#{queryData.operatorNo},'%')
            </if>
            <if test="queryData != null and queryData.operatorName!=null">
                and operatorName LIKE CONCAT('%',#{queryData.operatorName},'%')
            </if>
            <if test="true">
                and isDelete = 0
            </if>
        </where>
    </select>
    <select id="getOperatorSelect" resultType="com.jmt.model.jhh.vo.OperatorSelectVo">
        SELECT id,operatorNo,operatorName
        FROM jhh_operator_info
        WHERE isDelete = 0
        ORDER BY operatorName ASC
    </select>
    <select id="getByOperatorNo" resultType="com.jmt.model.jhh.dto.OperatorInfo">
        SELECT id,operatorNo,operatorName,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_operator_info
        WHERE operatorNo = #{operatorNo}
        AND isDelete = 0
    </select>
    <select id="getByUaaId" resultType="com.jmt.model.jhh.dto.OperatorInfo">
        SELECT id,uaaId,operatorName,operatorNo,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_operator_info
        WHERE uaaId = #{uaaId}
        AND isDelete = 0
    </select>
    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO jhh_operator_info(uaaId,operatorName,operatorNo,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete)
        VALUES
            (#{uaaId},#{operatorName},#{operatorNo},#{headImg},#{contractNo},#{dutyName},#{telPhone},#{isProfitLimit},#{limitAmount},#{contractPicUrl},#{licensePicUrl},#{auditStatus},#{reason},#{isFirstLogin},#{createTime},#{updateTime},#{isDelete})
    </insert>
    <update id="update">
        UPDATE jhh_operator_info
        <set>
            <if test="operatorName!=null">
                operatorName = #{operatorName},
            </if>
            <if test="operatorNo!=null">
                operatorNo = #{operatorNo},
            </if>
            <if test="headImg!=null">
                headImg = #{headImg},
            </if>
            <if test="contractNo!=null">
                contractNo = #{contractNo},
            </if>
            <if test="dutyName!=null">
                dutyName = #{dutyName},
            </if>
            <if test="telPhone!=null">
                telPhone = #{telPhone},
            </if>
            <if test="isProfitLimit!=null">
                isProfitLimit = #{isProfitLimit},
            </if>
            <if test="limitAmount!=null">
                limitAmount = #{limitAmount},
            </if>
            <if test="contractPicUrl!=null">
                contractPicUrl = #{contractPicUrl},
            </if>
            <if test="licensePicUrl!=null">
                licensePicUrl = #{licensePicUrl},
            </if>
            <if test="auditStatus!=null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason!=null">
                reason = #{reason},
            </if>
            <if test="isFirstLogin!=null">
                isFirstLogin = #{isFirstLogin},
            </if>
            <if test="isDelete!=null">
                isDelete = #{isDelete},
            </if>
            <if test="uaaId!=null">
                uaaId = #{uaaId},
            </if>
            <if test="updateTime!=null">
                updateTime = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
        AND isDelete = 0
    </update>
    <update id="delete">
        UPDATE jhh_operator_info
        SET isDelete= 1
        WHERE id = #{id}
        AND isDelete = 0
    </update>
</mapper>