<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmWorkEqApplyDetailDao">

    <sql id="Base_Column_List">
        id, workNo, busNo, busName, linkman, telPhone, country, province, city,
        area, address, longitude, latitude, operatorNo, refereeNo, eqType, eqNum, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmWorkEqApplyDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_work_eq_apply_detail (
        workNo, busNo, busName, linkman, telPhone, country, province, city,
        area, address, longitude, latitude, operatorNo, refereeNo, eqType, eqNum, createTime, updateTime, isDelete
        )
        VALUES ( #{workNo,jdbcType=VARCHAR}, #{busNo,jdbcType=VARCHAR}, #{busName,jdbcType=VARCHAR}, #{linkman,jdbcType=VARCHAR},
        #{telPhone,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, #{operatorNo,jdbcType=VARCHAR},
        #{refereeNo,jdbcType=VARCHAR}, #{eqType,jdbcType=VARCHAR}, #{eqNum,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER}
        )
    </insert>

    <update id="update" parameterType="com.jmt.model.cm.entity.CmWorkEqApplyDetail">
        UPDATE cm_work_eq_apply_detail
        <set>
            <if test="workNo != null">workNo = #{workNo,jdbcType=VARCHAR},</if>
            <if test="busNo != null">busNo = #{busNo,jdbcType=VARCHAR},</if>
            <if test="busName != null">busName = #{busName,jdbcType=VARCHAR},</if>
            <if test="linkman != null">linkman = #{linkman,jdbcType=VARCHAR},</if>
            <if test="telPhone != null">telPhone = #{telPhone,jdbcType=VARCHAR},</if>
            <if test="country != null">country = #{country,jdbcType=VARCHAR},</if>
            <if test="province != null">province = #{province,jdbcType=VARCHAR},</if>
            <if test="city != null">city = #{city,jdbcType=VARCHAR},</if>
            <if test="area != null">area = #{area,jdbcType=VARCHAR},</if>
            <if test="address != null">address = #{address,jdbcType=VARCHAR},</if>
            <if test="longitude != null">longitude = #{longitude,jdbcType=VARCHAR},</if>
            <if test="latitude != null">latitude = #{latitude,jdbcType=VARCHAR},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo,jdbcType=VARCHAR},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo,jdbcType=VARCHAR},</if>
            <if test="eqType != null">eqType = #{eqType,jdbcType=VARCHAR},</if>
            <if test="eqNum != null">eqNum = #{eqNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">createTime = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">updateTime = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">isDelete = #{isDelete,jdbcType=INTEGER},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
        AND isDelete = 0
    </update>


    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_work_eq_apply_detail
        SET isDelete = 1,
        updateTime = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteByWorkNo" parameterType="java.lang.String">
        UPDATE cm_work_eq_apply_detail
        SET isDelete = 1,
        updateTime = NOW()
        WHERE workNo = #{workNo,jdbcType=VARCHAR}
    </update>


    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmWorkEqApplyDetail">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_apply_detail
        WHERE id = #{id,jdbcType=BIGINT}
        AND isDelete = 0
    </select>

    <select id="selectByWorkNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmWorkEqApplyDetail">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_apply_detail
        WHERE workNo = #{workNo,jdbcType=VARCHAR}
        AND isDelete = 0
    </select>

    <select id="selectByBusNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmWorkEqApplyDetail">
        SELECT <include refid="Base_Column_List"/> FROM cm_work_eq_apply_detail
        WHERE busNo = #{busNo,jdbcType=VARCHAR}
        AND isDelete = 0
    </select>

    <select id="selectByEqType" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmWorkEqApplyDetail">
        SELECT <include refid="Base_Column_List"/> FROM cm_work_eq_apply_detail
        WHERE eqType = #{eqType,jdbcType=VARCHAR}
        AND isDelete = 0
    </select>

    <select id="selectAll" resultType="com.jmt.model.cm.entity.CmWorkEqApplyDetail">
        SELECT <include refid="Base_Column_List"/> FROM cm_work_eq_apply_detail
        WHERE isDelete = 0
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cm_work_eq_apply_detail (
        workNo, busNo, busName, linkman, telPhone,
        country, province, city, area, address,
        longitude, latitude, operatorNo, refereeNo,
        eqType, eqNum, createTime, updateTime, isDelete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.workNo,jdbcType=VARCHAR},
            #{item.busNo,jdbcType=VARCHAR},
            #{item.busName,jdbcType=VARCHAR},
            #{item.linkman,jdbcType=VARCHAR},
            #{item.telPhone,jdbcType=VARCHAR},
            #{item.country,jdbcType=VARCHAR},
            #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR},
            #{item.area,jdbcType=VARCHAR},
            #{item.address,jdbcType=VARCHAR},
            #{item.longitude,jdbcType=VARCHAR},
            #{item.latitude,jdbcType=VARCHAR},
            #{item.operatorNo,jdbcType=VARCHAR},
            #{item.refereeNo,jdbcType=VARCHAR},
            #{item.eqType,jdbcType=VARCHAR},
            #{item.eqNum,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDelete,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
</mapper>