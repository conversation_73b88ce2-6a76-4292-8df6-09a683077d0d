package com.jmt.controller;
import com.jmt.base.BaseController;
import com.jmt.client.CommonFeignClient;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.jhh.dto.SalesmanInfoDto;
import com.jmt.model.jhh.vo.SalesmanSelectVo;
import com.jmt.model.jhh.vo.SalesmanVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.SalesmanService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/jhh/salesman/v1")
public class SalesmanController extends BaseController{
    @Resource
    private SalesmanService salesmanService;
   @Resource
   private CommonFeignClient commonFeignClient;
    /**
     * 分页查询业务员信息
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<SalesmanInfoDto> pageQuery){
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<SalesmanVo> page = salesmanService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 新增业务员
     * @param  salesmanInfoDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody SalesmanInfoDto salesmanInfoDto) {
        logger.info("新增员工:{}",salesmanInfoDto);
        salesmanService.add(salesmanInfoDto);
        return super.responseSuccess("新增成功");
    }
    /**
     * 上传图片
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/upload")
    public String upload(@RequestParam("file") MultipartFile multipartFile) {
        logger.info("上传图片:{}", multipartFile);
        commonFeignClient.upload(multipartFile);
        return super.responseSuccess("上传成功");
    }
    /**
     * 修改业务员信息
     * @param salesmanInfoDto 修改参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody SalesmanInfoDto salesmanInfoDto) {
        logger.info("修改参数:{}",salesmanInfoDto);
        salesmanService.update(salesmanInfoDto);
        return super.responseSuccess("修改成功");
    }
    /**
     * 删除业务员
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    public String delete(@RequestParam Long id) {
        logger.info("删除参数:{}",id);
        salesmanService.delete(id);
        return super.responseSuccess("删除成功");
    }
    /**
     * 获取业务员下拉框数据
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanDropdown")
    public String getSalesmanDropdown() {
        List<SalesmanSelectVo> salesmanList = salesmanService.getSalesmanDropdown();
        return super.responseSuccess(salesmanList,"查询成功");
    }
    /**
     * 按uaaid查询业务员信息
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanInfoByUaaId")
    public String getSalesmanInfoByUaaId(@RequestParam Long uaaId) {
        SalesmanInfo salesmanInfo = salesmanService.getSalesmanInfoByUaaId(uaaId);
        return super.responseSuccess(salesmanInfo,"查询成功");
    }
    /**
     * 按id查询业务员信息
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanInfoById")
    public String getSalesmanInfoById(@RequestParam Long id) {
        SalesmanInfo salesmanInfo = salesmanService.getInfo(id);
        return super.responseSuccess(salesmanInfo,"查询成功");
    }
}
