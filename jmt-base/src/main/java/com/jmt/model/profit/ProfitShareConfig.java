package com.jmt.model.profit;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收益分润配置表
 * @TableName profit_share_config
 */
@Data
public class ProfitShareConfig {
    /**
     * 主键
     */
    private Long id;

    /**
     * 分润项目 shareprofit_project的主键ID
     */
    private Long projectId;

    /**
     * 分润项目名称
     */
    private String projectName;

    /**
     * 运营商编号 jhh_operator_info的operatorNo
     */
    private String operatorNo;

    /**
     * 0-比例
     1-补贴
     */
    private Integer shareType;

    /**
     * 运营商分润比例，当shareType=0时填写
     */
    private Double operatorRatio;

    /**
     * 设备收益人分润比例，当shareType=0时填写
     */
    private Double investorRatio;

    /**
     * 平台分润比例，当shareType=0时填写
     */
    private Double platformRatio;

    /**
     * 商家分润比例，当shareType=0时填写
     */
    private Double businessRatio;

    /**
     * 运营商补贴金额，当shareType=1时填写
     */
    private BigDecimal operatorAmount;

    /**
     * 设备收益人补贴金额，当shareType=1时填写
     */
    private BigDecimal investorAmount;

    /**
     * 平台补贴金额，当shareType=1时填写
     */
    private BigDecimal platformAmount;

    /**
     * 商家补贴金额，当shareType=1时填写
     */
    private BigDecimal businessAmount;

    /**
     * 0-启用
     1-禁用
     */
    private Integer useStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProfitShareConfig other = (ProfitShareConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getProjectId() == null ? other.getProjectId() == null : this.getProjectId().equals(other.getProjectId()))
                && (this.getProjectName() == null ? other.getProjectName() == null : this.getProjectName().equals(other.getProjectName()))
                && (this.getOperatorNo() == null ? other.getOperatorNo() == null : this.getOperatorNo().equals(other.getOperatorNo()))
                && (this.getShareType() == null ? other.getShareType() == null : this.getShareType().equals(other.getShareType()))
                && (this.getOperatorRatio() == null ? other.getOperatorRatio() == null : this.getOperatorRatio().equals(other.getOperatorRatio()))
                && (this.getInvestorRatio() == null ? other.getInvestorRatio() == null : this.getInvestorRatio().equals(other.getInvestorRatio()))
                && (this.getPlatformRatio() == null ? other.getPlatformRatio() == null : this.getPlatformRatio().equals(other.getPlatformRatio()))
                && (this.getBusinessRatio() == null ? other.getBusinessRatio() == null : this.getBusinessRatio().equals(other.getBusinessRatio()))
                && (this.getOperatorAmount() == null ? other.getOperatorAmount() == null : this.getOperatorAmount().equals(other.getOperatorAmount()))
                && (this.getInvestorAmount() == null ? other.getInvestorAmount() == null : this.getInvestorAmount().equals(other.getInvestorAmount()))
                && (this.getPlatformAmount() == null ? other.getPlatformAmount() == null : this.getPlatformAmount().equals(other.getPlatformAmount()))
                && (this.getBusinessAmount() == null ? other.getBusinessAmount() == null : this.getBusinessAmount().equals(other.getBusinessAmount()))
                && (this.getUseStatus() == null ? other.getUseStatus() == null : this.getUseStatus().equals(other.getUseStatus()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProjectId() == null) ? 0 : getProjectId().hashCode());
        result = prime * result + ((getProjectName() == null) ? 0 : getProjectName().hashCode());
        result = prime * result + ((getOperatorNo() == null) ? 0 : getOperatorNo().hashCode());
        result = prime * result + ((getShareType() == null) ? 0 : getShareType().hashCode());
        result = prime * result + ((getOperatorRatio() == null) ? 0 : getOperatorRatio().hashCode());
        result = prime * result + ((getInvestorRatio() == null) ? 0 : getInvestorRatio().hashCode());
        result = prime * result + ((getPlatformRatio() == null) ? 0 : getPlatformRatio().hashCode());
        result = prime * result + ((getBusinessRatio() == null) ? 0 : getBusinessRatio().hashCode());
        result = prime * result + ((getOperatorAmount() == null) ? 0 : getOperatorAmount().hashCode());
        result = prime * result + ((getInvestorAmount() == null) ? 0 : getInvestorAmount().hashCode());
        result = prime * result + ((getPlatformAmount() == null) ? 0 : getPlatformAmount().hashCode());
        result = prime * result + ((getBusinessAmount() == null) ? 0 : getBusinessAmount().hashCode());
        result = prime * result + ((getUseStatus() == null) ? 0 : getUseStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectName=").append(projectName);
        sb.append(", operatorNo=").append(operatorNo);
        sb.append(", shareType=").append(shareType);
        sb.append(", operatorRatio=").append(operatorRatio);
        sb.append(", investorRatio=").append(investorRatio);
        sb.append(", platformRatio=").append(platformRatio);
        sb.append(", businessRatio=").append(businessRatio);
        sb.append(", operatorAmount=").append(operatorAmount);
        sb.append(", investorAmount=").append(investorAmount);
        sb.append(", platformAmount=").append(platformAmount);
        sb.append(", businessAmount=").append(businessAmount);
        sb.append(", useStatus=").append(useStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}