package com.jmt.controller;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseController;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.ShareConfig;
import com.jmt.model.profit.dto.ShareConfigDto;
import com.jmt.model.profit.dto.ShareConfigPageDto;
import com.jmt.service.ShareConfigService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/profit/config/v1")
public class ShareConfigController extends BaseController {

    @Resource
    private ShareConfigService shareConfigService;
    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;
    private Long getNo(HttpServletRequest request) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long uaaId = loginUaaUser.getUaaId();
        return uaaId;
    }
    /**
     * 运营商分润项目配置分页查询接口
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<ShareConfigPageDto> pageQuery, HttpServletRequest request){
        logger.info("分页查询参数:{}",pageQuery);
        Long uaaId = getNo(request);
        String operatorInfoByUaaId= jhhOperatorFeignClient.getOperatorInfoByUaaId(uaaId);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(operatorInfoByUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        OperatorInfo operatorInfo = gson.fromJson(dataElement, OperatorInfo.class);
        String operatorNo = operatorInfo.getOperatorNo();
        PageResult<ShareConfig> page = shareConfigService.getPage(pageQuery,operatorNo);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 运营商分润项目配置新增接口
     * @param shareConfigDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody ShareConfigDto shareConfigDto){
        shareConfigService.add(shareConfigDto);
        return super.responseSuccess("新增成功");
    }
//    private String getNoForConfig(Long uaaId) {
//        String operatorInfoByUaaId= jhhOperatorFeignClient.getOperatorInfoByUaaId(uaaId);
//        Gson gson = new Gson();
//        JsonObject jsonObject = JsonParser.parseString(operatorInfoByUaaId).getAsJsonObject();
//        JsonElement dataElement = jsonObject.get("data");
//        OperatorInfo operatorInfo = gson.fromJson(dataElement, OperatorInfo.class);
//        if (operatorInfo == null){
//            return null;
//        }
//        String operatorNo = operatorInfo.getOperatorNo();
//        return operatorNo;
//    }
    /**
     * 运营商分润项目配置删除接口
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    public String delete(Long id){
        logger.info("删除参数:{}",id);
        shareConfigService.delete(id);
        return super.responseSuccess("删除成功");
    }
    /**
     * 运营商分润项目配置修改接口
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody ShareConfigDto shareConfigDto){
        shareConfigService.update(shareConfigDto);
        return super.responseSuccess("修改成功");
    }
    /**
     * 运营商分润项目配置查询接口
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(String operatorNo){
        List<ShareConfigDto> shareConfigDto = shareConfigService.getInfo(operatorNo);
        return super.responseSuccess(shareConfigDto,"查询成功");
    }
    /**
     * 运营商分润项目配置删除接口(根据运营商编号删)
     */
    @ResponseBody
    @PostMapping(value = "/deleteByNo")
    public String deleteByNo(String operatorNo){
        shareConfigService.deleteByNo(operatorNo);
        return super.responseSuccess("删除成功");
    }

}
