package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.dto.RequestTrans;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.OperatorEqService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eq/operatorEq/v1")
public class OperatorEqController extends BaseController {
    @Resource
    private OperatorEqService operatorEqService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<EqOperatorStockDto> pageQuery) {
        PageResult<EqBusinessSetupVo> page = operatorEqService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Integer id) {
        EqOperatorStockVo eqOperatorStockVo = operatorEqService.getInfo(id);
        return super.responseSuccess(eqOperatorStockVo,"查询成功");
    }
    /**
     * 迁移
     * @param requestTrans
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/transferStock")
    public String transferStock(@RequestBody RequestTrans requestTrans) {
        operatorEqService.transferStock(requestTrans);
        return super.responseSuccess("迁移成功");
    }
    /**
     * 清空运营商数据
     *
     */
    @ResponseBody
    @PostMapping(value = "/clearOperatorEq")
    public String clearOperatorEq(@RequestBody String operatorNo) {
        operatorEqService.clearOperatorEq(operatorNo);
        return super.responseSuccess("清空成功");
    }
}
