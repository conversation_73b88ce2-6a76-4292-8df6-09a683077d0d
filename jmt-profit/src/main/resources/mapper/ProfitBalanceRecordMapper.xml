<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitBalanceRecordMapper">

    <sql id="Base_Column_List">
        id,uaaId,balance,recordType,balanceSource,sourceId,
        remark,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitBalanceRecord">
        select
        <include refid="Base_Column_List" />
        from profit_balance_record
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_balance_record
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitBalanceRecord" useGeneratedKeys="true">
        insert into profit_balance_record
        ( id,uaaId,balance,recordType,balanceSource,sourceId,
          remark,createTime,updateTime,isDelete)
        values (#{id},#{uaaId},#{balance},#{recordType},#{balanceSource},#{sourceId},
                #{remark},#{createTime},#{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitBalanceRecord" useGeneratedKeys="true">
        insert into profit_balance_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uaaId != null">uaaId,</if>
            <if test="balance != null">balance,</if>
            <if test="recordType != null">recordType,</if>
            <if test="balanceSource != null">balanceSource,</if>
            <if test="sourceId != null">sourceId,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uaaId != null">#{uaaId},</if>
            <if test="balance != null">#{balance},</if>
            <if test="recordType != null">#{recordType},</if>
            <if test="balanceSource != null">#{balanceSource},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitBalanceRecord">
        update profit_balance_record
        <set>
            <if test="uaaId != null">
                uaaId = #{uaaId},
            </if>
            <if test="balance != null">
                balance = #{balance},
            </if>
            <if test="recordType != null">
                recordType = #{recordType},
            </if>
            <if test="balanceSource != null">
                balanceSource = #{balanceSource},
            </if>
            <if test="sourceId != null">
                sourceId = #{sourceId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitBalanceRecord">
        update profit_balance_record
        set
            uaaId =  #{uaaId},
            balance =  #{balance},
            recordType =  #{recordType},
            balanceSource =  #{balanceSource},
            sourceId =  #{sourceId},
            remark =  #{remark},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>