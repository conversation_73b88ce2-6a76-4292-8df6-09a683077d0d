<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.OperatorInfoAuditDao">
    <insert id="add">
        INSERT INTO jhh_operator_info_audit(auditUser,operatorNo,auditStatus,reason,createTime,updateTime,isDelete)
        VALUES
            (#{auditUser},#{operatorNo},#{auditStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
    </insert>
    <insert id="insertBatch">
        INSERT INTO jhh_salesman_info(auditUser,operatorNo,salesmanName,salesmanNo,headImg,salesmanType,dutyName,telPhone,fStatus,isFirstLogin,createTime,updateTime,isDelete)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.auditUser},#{item.operatorNo},#{item.salesmanName},#{item.salesmanNo},#{item.headImg},#{item.salesmanType},#{item.dutyName},#{item.telPhone},#{item.fStatus},#{item.isFirstLogin},#{item.createTime},#{item.updateTime},#{item.isDelete})
        </foreach>
    </insert>

    <update id="delete">
        UPDATE jhh_operator_info_audit
        SET isDelete = 1
        WHERE operatorNo = #{operatorNo}
        AND isDelete = 0
    </update>
    <update id="deleteById">
        UPDATE jhh_operator_info_audit
        SET isDelete = 1
        WHERE id = #{id}
        AND isDelete = 0
    </update>
    <update id="deleteByIdWithLong">
        UPDATE jhh_operator_info_audit
        SET isDelete = 1
        WHERE id = #{id}
        AND isDelete = 0
    </update>
    <select id="getPage" resultType="com.jmt.model.jhh.vo.OperatorInfoAuditVo">
        SELECT
        auditUser,
        operatorNo,
        auditStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM jhh_operator_info_audit
        <where>
            <if test="queryData != null and queryData.operatorNo!=null">
                AND operatorNo = #{queryData.operatorNo}
            </if>
            <if test="queryData != null and queryData.auditStatus!=null">
                AND auditStatus = #{queryData.auditStatus}
            </if>
            <if test="queryData != null and queryData.reason!=null">
                AND reason LIKE CONCAT('%',#{queryData.reason},'%')
            </if>
            <if test="queryData != null and queryData.createTime!=null">
                AND createTime >= #{queryData.createTime}
            </if>
            <if test="queryData != null and queryData.updateTime!=null">
                AND updateTime >= #{queryData.updateTime}
            </if>
            <if test="queryData != null and queryData.isDelete!=null">
                AND isDelete = #{queryData.isDelete}
            </if>
            <if test="queryData != null and queryData.auditUser!=null">
                AND auditUser = #{queryData.auditUser}
            </if>
            <if test="true">
                AND isDelete = 0
            </if>
        </where>
        ORDER BY createTime DESC
    </select>

    <select id="getInfo" resultType="com.jmt.model.jhh.dto.OperatorInfoAudit">
        SELECT
        id,
        auditUser,
        operatorNo,
        auditStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM jhh_operator_info_audit
        WHERE
            id = #{id}
        AND isDelete = 0
    </select>
    <select id="getInfoByOperatorNo" resultType="com.jmt.model.jhh.dto.OperatorInfoAudit">
        SELECT
        id,
        auditUser,
        operatorNo,
        auditStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM jhh_operator_info_audit
        WHERE
            operatorNo = #{operatorNo}
        AND isDelete = 0
    </select>


    <update id="update">
        UPDATE jhh_operator_info_audit
        <set>
            <if test="operatorNo != null">
                operatorNo = #{operatorNo},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
            <if test="auditUser != null">
                auditUser = #{auditUser},
            </if>
        </set>
        WHERE id = #{id}
        AND isDelete = 0
    </update>
</mapper>