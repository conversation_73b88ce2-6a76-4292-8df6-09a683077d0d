<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.SalesmanDao">
    <update id="delete">
        update jhh_salesman_info set isDelete = 1 where id = #{id}
        AND isDelete = 0
    </update>
    <update id="deleteById">
        update jhh_salesman_info set isDelete = 1 where id = #{id}
        AND isDelete = 0
    </update>
    <select id="getPage" resultType="com.jmt.model.jhh.vo.SalesmanVo">
        SELECT s.uaaId,s.salesmanName,s.salesmanNo,s.headImg,s.salesmanType,s.operatorNo,s.dutyName,s.telPhone,s.is<PERSON><PERSON><PERSON>,s.fStatus,s.isDelete,o.operatorName AS operatorName
        FROM jhh_salesman_info s LEFT JOIN jhh_operator_info o ON s.operatorNo = o.operatorNo
        <where>
            <if test="queryData.salesmanNo!=null">
                and s.salesmanNo = #{queryData.salesmanNo}
            </if>
            <if test="queryData.salesmanName!=null">
                s.salesmanName LIKE CONCAT('%',#{queryData.salesman})
            </if>
            <if test="queryData.operatorNo!=null">
                and s.operatorNo = #{queryData.operatorNo}
            </if>
            <if test="true">
                AND s.isDelete = 0
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.jmt.model.jhh.dto.SalesmanInfo">
        SELECT id,uaaId,salesmanName,salesmanNo,headImg,salesmanType,operatorNo,dutyName,telPhone,isFirstLogin,fStatus,isDelete
        FROM jhh_salesman_info WHERE id = #{id} AND isDelete = 0
    </select>
    <select id="getByUaaId" resultType="com.jmt.model.jhh.dto.SalesmanInfo">
        SELECT id,uaaId,salesmanName,salesmanNo,headImg,salesmanType,operatorNo,dutyName,telPhone,isFirstLogin,fStatus,isDelete
        FROM jhh_salesman_info
        WHERE uaaId = #{uaaId}
        AND isDelete = 0
    </select>
    <select id="getSalesmanDropdown" resultType="com.jmt.model.jhh.vo.SalesmanSelectVo">
        SELECT id,salesmanName,salesmanNo
        FROM jhh_salesman_info
        WHERE isDelete = 0
    </select>
    <insert id="add">
        INSERT INTO jhh_salesman_info(uaaId,salesmanName,salesmanNo,headImg,salesmanType,operatorNo,dutyName,telPhone,isFirstLogin,fStatus,createTime,updateTime,isDelete)
        VALUES
            (#{uaaId},#{salesmanName},#{salesmanNo},#{headImg},#{salesmanType},#{operatorNo},#{dutyName},#{telPhone},#{isFirstLogin},#{fStatus},#{createTime},#{updateTime},#{isDelete})
    </insert>
    <update id="update">
        UPDATE jhh_salesman_info
        <set>
            <if test="salesmanName!=null">
                salesmanName = #{salesmanName},
            </if>
            <if test="salesmanNo!=null">
                salesmanNo = #{salesmanNo},
            </if>
            <if test="headImg!=null">
                headImg = #{headImg},
            </if>
            <if test="salesmanType!=null">
                salesmanType = #{salesmanType},
            </if>
            <if test="operatorNo!=null">
                operatorNo = #{operatorNo},
            </if>
            <if test="dutyName!=null">
                dutyName = #{dutyName},
            </if>
            <if test="telPhone!=null">
                telPhone = #{telPhone},
            </if>
            <if test="isFirstLogin!=null">
                isFirstLogin = #{isFirstLogin},
            </if>
            <if test="fStatus!=null">
                fStatus = #{fStatus},
            </if>
            <if test="isDelete!=null">
                isDelete = #{isDelete},
            </if>
            <if test="uaaId!=null">
                uaaId = #{uaaId},
            </if>
            <if test="updateTime!=null">
                updateTime = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
        AND isDelete = 0
    </update>
    <update id="transfer">
        UPDATE jhh_salesman_info
        SET operatorNo = #{targetOperatorNo}
        WHERE operatorNo = #{sourceOperatorNo}
          AND isdelete = 0
          AND fStatus = 0
    </update>
</mapper>