package com.jmt.model.jhh.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class SalesmanInfoDto {
    private Long id;

    private String salesmanName;

    private String salesmanNo;

    private String headImg;

    private Integer salesmanType;

    private String operatorNo;

    private String dutyName;

    private String telPhone;

    private Integer isFirstLogin;
    @Pattern(regexp = "^[01]$", message = "状态只能是0和1")
    @NotBlank(message = "状态不能为空")
    private Integer fStatus;

    private Integer isDelete;
}