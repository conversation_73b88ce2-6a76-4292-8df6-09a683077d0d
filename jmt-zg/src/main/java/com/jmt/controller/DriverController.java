package com.jmt.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.DropdownVO;
import com.jmt.model.zg.ZgDriverInfo;
import com.jmt.model.zg.ZgDriverInfoVo;
import com.jmt.service.DriverService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 配送员管理控制器
 * 提供配送员信息的查询、启用/禁用、审核以及下拉框数据等功能
 */
@RestController
@RequestMapping("/zg/driver/v1")
public class DriverController extends BaseController {

    @Resource
    private DriverService driverService;

    /**
     * 禁用配送员账号
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/disableDriver")
    public String disableDriver(@RequestParam Long userId) {
        driverService.disableDriverInfo(userId);
        return super.responseSuccess("禁用成功");
    }

    /**
     * 启用配送员账号
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/enableDriver")
    public String enableDriver(@RequestParam Long userId) {
        driverService.enableDriverInfo(userId);
        return super.responseSuccess("启用成功");
    }

    /**
     * 分页查询配送员信息
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 配送员分页数据
     */
    @GetMapping("/page")
    public String getPage(@RequestBody PageQuery<ZgDriverInfo> pageQuery) {
        PageResult<ZgDriverInfo> originalPage = driverService.getDriverInfoPage(pageQuery);
        PageResult<ZgDriverInfoVo> voPage = new PageResult<>();
        BeanUtil.copyProperties(originalPage, voPage);
        List<ZgDriverInfoVo> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(originalPage.getList())) {
            for (ZgDriverInfo driverInfo : originalPage.getList()) {
                ZgDriverInfoVo vo = BeanUtil.toBean(driverInfo, ZgDriverInfoVo.class);
                voList.add(vo);
            }
        }
        voPage.setList(voList);

        // 4. 返回转换后的VO分页结果
        return super.responseSuccess(voPage, "查询成功");
    }

    /**
     * 根据driverNo获取配送员详情
     * @param driverNo 配送员编号
     * @return 配送员详细信息
     */
    @GetMapping("/DriverInfo")
    public String getDriverInfo(@RequestParam String driverNo) {
        ZgDriverInfo driver = driverService.getDriverInfo(driverNo);
        return super.responseSuccess(driver, "查询成功");
    }

    /**
     * 审核配送员信息
     * @param driverNo 配送员编号
     * @param auditStatus 审核状态（0-待审核，1-审核通过，2-审核拒绝）
     * @param reason 审核意见
     * @return 操作结果
     */
    @PostMapping("/audit")
    public String auditDriver(@RequestParam String driverNo, @RequestParam Integer auditStatus, @RequestParam String reason,HttpServletRequest req) {
        Long uaaId = LoginUserUtil.get(req).getUaaId();
        driverService.auditDriverInfo(driverNo, auditStatus, reason,uaaId);
        return super.responseSuccess("审核操作成功");
    }

    /**
     * 获取配送员下拉框数据（用于选择框）
     * @return 包含配送员ID和姓名的下拉框数据列表
     */
    @GetMapping("/driverDropdown")
    public String getDriverDropdownData() {
        List<DropdownVO> driverList = driverService.getDriverIdAndName();
        return super.responseSuccess(driverList, "查询成功");
    }
}