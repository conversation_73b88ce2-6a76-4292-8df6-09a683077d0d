package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.ZgSupplierInfoAuditMapper;
import com.jmt.dao.ZgSupplierInfoMapper;
import com.jmt.dao.ZgUserMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.zg.*;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class SupplierService extends BaseService {
    // 审核状态常量
    private static final int AUDIT_STATUS_PENDING = 0;    // 待审核
    private static final int AUDIT_STATUS_APPROVED = 1;   // 审核通过
    private static final int AUDIT_STATUS_REJECTED = 2;   // 审核拒绝

    // 通用状态常量
    private static final int IS_DELETE_NO = 0;            // 未删除
    private static final int IS_DELETE_YES = 1;           // 已删除
    private static final int ROLE_TYPE_SUPPLIER = 1;      // 供应商角色
    private static final int IS_FIRST_LOGIN = 1;          // 首次登录

    @Resource
    private ZgSupplierInfoMapper zgSupplierInfoMapper;
    @Resource
    private ZgSupplierInfoAuditMapper zgSupplierInfoAuditMapper;
    @Resource
    private ZgUserMapper zgUserMapper;
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private PasswordEncoder passwordEncoder;

    /**
     * 启用供应商信息
     */
    public void enableSupplierInfo(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("供应商ID不能为空");
        }
        ZgSupplierInfo supplierInfo = zgSupplierInfoMapper.selectByUserIdKey(id);
        if (supplierInfo == null) {
            throw new IllegalStateException("供应商信息不存在：" + id);
        }
        supplierInfo.setUpdateTime(new Date());
        supplierInfo.setIsDelete(IS_DELETE_NO);
        zgSupplierInfoMapper.updateByPrimaryKeySelective(supplierInfo);
    }

    /**
     * 禁用供应商信息
     */
    public void disableSupplierInfo(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("供应商ID不能为空");
        }
        ZgSupplierInfo supplierInfo = zgSupplierInfoMapper.selectByUserIdKey(id);
        if (supplierInfo == null) {
            throw new IllegalStateException("供应商信息不存在：" + id);
        }
        supplierInfo.setUpdateTime(new Date());
        supplierInfo.setIsDelete(IS_DELETE_YES);
        zgSupplierInfoMapper.updateByPrimaryKeySelective(supplierInfo);
    }

    /**
     * 分页查询供应商信息
     */
    public PageResult<ZgSupplierInfo> getSupplierInfoPage(PageQuery<ZgSupplierInfo> pageQuery) {
        if (pageQuery == null) {
            throw new IllegalArgumentException("分页查询参数不能为空");
        }
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgSupplierInfo queryData = pageQuery.getQueryData();
        List<ZgSupplierInfo> list = zgSupplierInfoMapper.getSupplierInfoPage(queryData);
        return new PageResult<>(list);
    }

    /**
     * 根据ID查询供应商信息
     */
    public ZgSupplierInfo getSupplierInfo(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("供应商ID不能为空");
        }
        return zgSupplierInfoMapper.selectByUserIdKey(id);
    }

    /**
     * 根据供应商编号查询供应商信息
     */
    public ZgSupplierInfo getSupplierInfo(String supplierNo) {
        if (supplierNo == null || supplierNo.trim().isEmpty()) {
            throw new IllegalArgumentException("供应商编号不能为空");
        }
        return zgSupplierInfoMapper.selectBySupplierNo(supplierNo);
    }

    /**
     * 申请成为供应商（创建用户、供应商信息及审核记录）
     */
    public void beSupplierInfo(SupplierAndAuditUserDto supplierAndAuditUserDto) {
        if (supplierAndAuditUserDto == null) {
            throw new IllegalArgumentException("供应商申请信息不能为空");
        }
        Date now = new Date();

        // 创建用户信息
        ZgUser zgUser = new ZgUser();
        zgUser.setUserName(supplierAndAuditUserDto.getSuppliername());
        zgUser.setNickName(supplierAndAuditUserDto.getSuppliername());
        zgUser.setCreateTime(now);
        zgUser.setUpdateTime(now);
        zgUser.setRoleType(ROLE_TYPE_SUPPLIER);
        zgUser.setIsDelete(IS_DELETE_NO);
        zgUser.setCountry(supplierAndAuditUserDto.getCountry());
        zgUser.setProvince(supplierAndAuditUserDto.getProvince());
        zgUser.setCity(supplierAndAuditUserDto.getCity());
        zgUser.setAddress(supplierAndAuditUserDto.getAddress());
        zgUser.setTelPhone(supplierAndAuditUserDto.getTelphone());
        zgUser.setIsFirstLogin(IS_FIRST_LOGIN);
        zgUserMapper.insert(zgUser);

        // 创建供应商信息
        ZgSupplierInfo zgSupplierInfo = new ZgSupplierInfo();
        zgSupplierInfo.setUserId(zgUser.getId());
        zgSupplierInfo.setCreateTime(now);
        zgSupplierInfo.setUpdateTime(now);
        zgSupplierInfo.setAuditStatus(AUDIT_STATUS_PENDING);
        zgSupplierInfo.setSupplierNo(supplierAndAuditUserDto.getSupplierno());
        zgSupplierInfo.setSupplierName(supplierAndAuditUserDto.getSuppliername());
        zgSupplierInfo.setLinkman(supplierAndAuditUserDto.getLinkman());
        zgSupplierInfo.setTelPhone(supplierAndAuditUserDto.getTelphone());
        zgSupplierInfo.setCountry(supplierAndAuditUserDto.getCountry());
        zgSupplierInfo.setProvince(supplierAndAuditUserDto.getProvince());
        zgSupplierInfo.setCity(supplierAndAuditUserDto.getCity());
        zgSupplierInfo.setAddress(supplierAndAuditUserDto.getAddress());
        zgSupplierInfo.setLatitude(supplierAndAuditUserDto.getLatitude());
        zgSupplierInfo.setLongitude(supplierAndAuditUserDto.getLongitude());
        zgSupplierInfo.setSupplierPhoto(supplierAndAuditUserDto.getSupplierphoto());
        zgSupplierInfo.setSupplierLicense(supplierAndAuditUserDto.getSupplierlicense());
        zgSupplierInfo.setCategoryId(supplierAndAuditUserDto.getCategoryid());
        zgSupplierInfo.setSupplierStar(supplierAndAuditUserDto.getSupplierstar());
        zgSupplierInfo.setRemark(supplierAndAuditUserDto.getRemark());
        zgSupplierInfo.setIsDelete(IS_DELETE_NO);
        zgSupplierInfoMapper.insertSelective(zgSupplierInfo);

        // 创建审核记录
        ZgSupplierInfoAudit zgSupplierInfoAudit = new ZgSupplierInfoAudit();
        zgSupplierInfoAudit.setAuditUser(supplierAndAuditUserDto.getUserId());
        zgSupplierInfoAudit.setCreateTime(now);
        zgSupplierInfoAudit.setUpdateTime(now);
        zgSupplierInfoAudit.setSupplierNo(supplierAndAuditUserDto.getSupplierno());
        zgSupplierInfoAudit.setAuditStatus(String.valueOf(AUDIT_STATUS_PENDING));
        zgSupplierInfoAudit.setIsDelete(IS_DELETE_NO);
        zgSupplierInfoAuditMapper.insertSelective(zgSupplierInfoAudit);
    }

    /**
     * 批准供应商申请
     */
    public Integer grantedSupplier(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("审核记录ID不能为空");
        }
        ZgSupplierInfoAudit audit = zgSupplierInfoAuditMapper.selectByPrimaryKey(id);
        if (audit == null) {
            throw new IllegalStateException("审核记录不存在：" + id);
        }
        audit.setUpdateTime(new Date());
        audit.setAuditStatus(String.valueOf(AUDIT_STATUS_APPROVED));
        return zgSupplierInfoAuditMapper.updateByPrimaryKeySelective(audit);
    }

    /**
     * 拒绝供应商申请
     */
    public Integer rejectionSupplier(Long id, String rejectionReason) {
        if (id == null) {
            throw new IllegalArgumentException("审核记录ID不能为空");
        }
        if (rejectionReason == null || rejectionReason.trim().isEmpty()) {
            throw new IllegalArgumentException("拒绝原因不能为空");
        }
        ZgSupplierInfoAudit audit = zgSupplierInfoAuditMapper.selectByPrimaryKey(id);
        if (audit == null) {
            throw new IllegalStateException("审核记录不存在：" + id);
        }
        audit.setUpdateTime(new Date());
        audit.setAuditStatus(String.valueOf(AUDIT_STATUS_REJECTED));
        audit.setReason(rejectionReason);
        return zgSupplierInfoAuditMapper.updateByPrimaryKeySelective(audit);
    }

    /**
     * 审核供应商信息（更新状态并同步用户信息）
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer auditSupplierInfo(String supplierNo, Integer auditStatus, String reason, Long uuaId) {
        if (supplierNo == null || supplierNo.trim().isEmpty() || auditStatus == null || uuaId == null) {
            throw new IllegalArgumentException("供应商编号、审核状态和操作人ID不能为空");
        }

        // 查询审核记录和供应商信息
        ZgSupplierInfoAudit audit = zgSupplierInfoAuditMapper.selectByAuditSupplierNo(supplierNo);
        ZgSupplierInfo supplierInfo = zgSupplierInfoMapper.selectBySupplierNo(supplierNo);
        if (audit == null || supplierInfo == null) {
            throw new IllegalStateException("供应商或其审核记录不存在：" + supplierNo);
        }

        // 设置审核原因
        if (reason != null && !reason.trim().isEmpty()) {
            supplierInfo.setReason(reason);
            audit.setReason(reason);
        }

        // 更新供应商状态
        supplierInfo.setAuditStatus(auditStatus);
        supplierInfo.setUpdateTime(new Date());
        zgSupplierInfoMapper.updateByPrimaryKeySelective(supplierInfo);

        // 更新审核记录
        audit.setAuditUser(uuaId);
        audit.setUpdateTime(new Date());
        audit.setAuditStatus(String.valueOf(auditStatus));

        // 审核通过时同步创建UAA用户
        if (auditStatus == AUDIT_STATUS_APPROVED) {
            UaaUserDTO uaaUserDTO = new UaaUserDTO();
            uaaUserDTO.setLoginName(supplierInfo.getSupplierName());
            uaaUserDTO.setNickname(JmtConstant.DEFAULT_NICKNAME);
            uaaUserDTO.setPassword(passwordEncoder.encode(JmtConstant.DEFAULT_PASSWORD));
            uaaUserDTO.setCountry(supplierInfo.getCountry());
            uaaUserDTO.setProvince(supplierInfo.getProvince());
            uaaUserDTO.setCity(supplierInfo.getCity());
            uaaUserDTO.setAddress(supplierInfo.getAddress());

            // 调用UAA服务创建用户并更新本地用户关联ID
            String response = uaaFeignClient.registerUser(uaaUserDTO);
            JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
            JsonElement dataElement = jsonObject.get("data");
            UaaUser user = new Gson().fromJson(dataElement, UaaUser.class);

            ZgUser zgUser = zgUserMapper.selectByPrimaryKey(supplierInfo.getUserId());
            zgUser.setUaaId(user.getUaaId());
            zgUserMapper.updateByPrimaryKeySelective(zgUser);
        }

        return zgSupplierInfoAuditMapper.updateByPrimaryKeySelective(audit);
    }

    /**
     * 获取所有供应商的编号和名称（下拉选择用）
     */
    public List<DropdownVO> getSupplierIdAndName() {
        List<Map<String, String>> supplierMaps = zgSupplierInfoMapper.getSupplierNoAndName();
        List<DropdownVO> result = new ArrayList<>();
        int sequenceNo = 1;
        for (Map<String, String> supplierMap : supplierMaps) {
            String supplierNo = supplierMap.get("supplierNo");
            String supplierName = supplierMap.get("supplierName");
            result.add(new DropdownVO(sequenceNo++, supplierNo, supplierName));
        }
        return result;
    }
}