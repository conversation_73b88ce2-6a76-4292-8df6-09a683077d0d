package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUserAddress;
import com.jmt.service.UaaUserAddressService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
@Slf4j
@RestController
@RequestMapping("/uaa/address")
public class UaaUserAddressController extends BaseController {
    @Resource
    private UaaUserAddressService uaaUserAddressService;

    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<UaaUserAddress> pageQuery, HttpServletRequest req){
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        pageQuery.getQueryData().setUaaId(loginUser.getUaaId());
        PageResult<UaaUserAddress> pageResult = uaaUserAddressService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 新增
     * @param uaaUserAddress
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody UaaUserAddress uaaUserAddress, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        uaaUserAddress.setUaaId(loginUser.getUaaId());
        uaaUserAddressService.createAddress(uaaUserAddress);
        return super.responseSuccess("新增成功");
    }

    /**
     * 修改
     * @param uaaUserAddress
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody UaaUserAddress uaaUserAddress) {
        uaaUserAddressService.updateAddress(uaaUserAddress);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        uaaUserAddressService.deleteAddress(id);
        return super.responseSuccess("删除成功");
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        UaaUserAddress address = uaaUserAddressService.getAddressById(id);
        if(address == null){
            return super.responseFail("不存在该地址");
        }
        return super.responseSuccess(address,"查询成功");
    }

    /**
     * 根据uaaid查询
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/list")
    public String getByUaaId(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        List<UaaUserAddress> addresses = uaaUserAddressService.getAddressesByUaaId(loginUser.getUaaId());
        return super.responseSuccess(addresses,"查询成功");
    }

    /**
     * 设置默认地址
     * @param addressId
     * @param req
     * @return
     */
    @PostMapping("/v1/setDefault")
    public String setDefaultAddress(@RequestParam Long addressId, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            uaaUserAddressService.setDefaultAddress(loginUser.getUaaId(), addressId);
            return super.responseSuccess("默认地址设置成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }
}
