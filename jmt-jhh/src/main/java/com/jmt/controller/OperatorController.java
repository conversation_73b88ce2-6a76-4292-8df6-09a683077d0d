package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.client.CommonFeignClient;
import com.jmt.model.eq.dto.RequestTrans;
import com.jmt.model.jhh.dto.OperatorAuditDto;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.jhh.dto.OperatorInfoDto;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.OperatorService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


@RestController
@RequestMapping("/jhh/operator/v1")
public class OperatorController extends BaseController{
    @Resource
    private OperatorService operatorService;
    @Resource
    private CommonFeignClient commonFeignClient;
    /**
     * 根据id查看运营商信息
     * @param id 主键ID
     * @return String
     *
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        OperatorInfo operatorInfo= operatorService.getInfo(id);
        return super.responseSuccess(operatorInfo,"查询成功");
    }
    /**
     * 分页查询运营商信息
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<OperatorInfoDto> pageQuery) {
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<OperatorVo> page = operatorService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 新增运营商
     * @param operatorInfoDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody OperatorInfoDto operatorInfoDto) {
        logger.info("新增运营商:{}",operatorInfoDto);
        operatorService.add(operatorInfoDto);
        return super.responseSuccess("新增成功");
    }
    /**
     * 上传图片
     *
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/upload")
    public String upload(@RequestParam("file") MultipartFile multipartFile) {
        logger.info("上传图片:{}", multipartFile);
        commonFeignClient.upload(multipartFile);
        return super.responseSuccess("上传成功");
    }
    /**
     * 修改运营商
     * @param operatorInfoDto 修改参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody OperatorInfoDto operatorInfoDto) {
        logger.info("修改参数:{}",operatorInfoDto);
        operatorService.update(operatorInfoDto);
        return super.responseSuccess("修改成功");
    }
    /**
     * 删除运营商
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    public String delete(@RequestParam Long id) {
        logger.info("删除参数:{}",id);
        operatorService.delete(id);
        return super.responseSuccess("删除成功");
    }
    /**
     * 运营商状态变更
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/audit")
    public String audit(@RequestBody OperatorAuditDto operaInfoAuditDto, HttpServletRequest request) {
        logger.info("审核参数:{}",operaInfoAuditDto);
        operatorService.audit(operaInfoAuditDto, request);
        return super.responseSuccess("审核成功");
    }
    /**
     * 运营商下拉框数据接口
     */
    @ResponseBody
    @GetMapping(value = "/getOperatorSelect")
    public String getOperatorSelect() {
        List<OperatorVo> list = operatorService.getOperatorSelect();
        return super.responseSuccess(list,"查询成功");
    }
    /**
     * 运营商冻结接口
     */
    @ResponseBody
    @PostMapping(value = "/freeze")
    public String freeze(@RequestBody OperatorAuditDto operaInfoAuditDto, HttpServletRequest request) {
        operatorService.freeze(operaInfoAuditDto, request);
        return super.responseSuccess("冻结成功");
    }
    /**
     * 运营商迁移接口
     */
    @ResponseBody
    @PostMapping(value = "/transferOfOperator")
    public String transfer(@RequestBody RequestTrans requestTrans) {
        operatorService.transfer(requestTrans);
        return super.responseSuccess("迁移成功");
    }
    /**
     * 按uaaid查询
     */
    @ResponseBody
    @GetMapping(value = "/getOperatorInfoByUaaId")
    public String getOperatorInfoByUaaId(@RequestParam Long uaaId) {
        OperatorInfo operatorInfo = operatorService.getOperatorInfoByUaaId(uaaId);
        return super.responseSuccess(operatorInfo,"查询成功");
    }
    @ResponseBody
    @GetMapping(value = "/getOperatorInfoByOperatorNo")
    public String getOperatorInfoByOperatorNo(@RequestParam String operatorNo) {
        OperatorInfo operatorInfo = operatorService.getOperatorInfoByOperatorNo(operatorNo);
        return super.responseSuccess(operatorInfo,"查询成功");
    }
}
