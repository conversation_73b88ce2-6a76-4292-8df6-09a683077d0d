package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.ProfitBalanceRecordDao;
import com.jmt.dao.ProfitPayRecordDao;
import com.jmt.dao.ProfitPointsRecordDao;
import com.jmt.enums.PayStatusEnum;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.BalancePayDTO;
import com.jmt.model.profit.dto.PointsPayDTO;
import com.jmt.model.profit.dto.ProfitPayRecordDTO;
import com.jmt.model.profit.entity.ProfitBalanceRecord;
import com.jmt.model.profit.entity.ProfitPayRecord;
import com.jmt.model.profit.entity.ProfitPointsRecord;
import com.jmt.util.WorkNoGeneratorUtil;
import feign.FeignException;
import org.apache.commons.lang.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class PaymentService extends BaseService {

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private ProfitBalanceRecordDao profitBalanceRecordDao;

    @Resource
    private ProfitPayRecordDao profitPayRecordDao;

    @Resource
    private ProfitPointsRecordDao profitPointsRecordDao;

    /**
     * 支付流水分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<ProfitPayRecordDTO> getPage(PageQuery<ProfitPayRecordDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ProfitPayRecordDTO profitPayRecordDTO = pageQuery.getQueryData();
        List<ProfitPayRecordDTO> list = profitPayRecordDao.selectByCondition(profitPayRecordDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 余额支付
     */
    @Transactional(rollbackFor = Exception.class)
    public void balancePay(BalancePayDTO dto) {
        if (dto.getBalance() == null || dto.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("金额必须大于0");
        }
        if (StringUtils.isBlank(dto.getOrderNo())) {
            throw new IllegalArgumentException("订单号不能为空");
        }

        try {
            // 验证支付密码
            validatePayPassword(dto.getUaaId(), dto.getPayPassword());
            // 扣减余额
            uaaFeignClient.decreaseBalance(dto.getUaaId(), dto.getBalance());
            uaaFeignClient.increaseBalance(dto.getBusUaaId(), dto.getBalance());
            // 记录支付流水
            ProfitPayRecord payRecord = buildPayRecord(dto.getUaaId(),  dto.getOrderNo(),PayTypeEnum.BALANCE_PAY,PayStatusEnum.SUCCESS);
            payRecord.setPayAmount(dto.getBalance());
            profitPayRecordDao.insert(payRecord);

            // 记录余额收支
            ProfitBalanceRecord balanceRecord = buildBalanceRecord(dto.getUaaId(), dto.getBalance());
            profitBalanceRecordDao.insert(balanceRecord);

        } catch (FeignException e) {

            throw new RuntimeException("余额扣减失败: " + e.getMessage(), e);
        } catch (DataAccessException e) {

            throw new RuntimeException("支付记录保存失败", e);
        }
    }

    /**
     * 积分支付
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void pointsPay(PointsPayDTO dto) {
        if (dto.getPoints() == null || dto.getPoints() <= 0) {
            throw new IllegalArgumentException("金额必须大于0");
        }
        if (StringUtils.isBlank(dto.getOrderNo())) {
            throw new IllegalArgumentException("订单号不能为空");
        }

        try {
            // 验证支付密码
            validatePayPassword(dto.getUaaId(), dto.getPayPassword());
            // 扣减积分
            uaaFeignClient.decreasePoints(dto.getUaaId(), dto.getPoints());
            uaaFeignClient.increasePoints(dto.getBusUaaId(), dto.getPoints());

            ProfitPayRecord payRecord = buildPayRecord(dto.getUaaId() , dto.getOrderNo(),PayTypeEnum.POINTS_PAY,PayStatusEnum.SUCCESS);
            payRecord.setPayAmount(new BigDecimal(dto.getPoints()));
            profitPayRecordDao.insert(payRecord);

            // 记录积分收支
            ProfitPointsRecord pointsRecord = buildPointsRecord(dto.getUaaId(), dto.getPoints());
            profitPointsRecordDao.insert(pointsRecord);

        } catch (FeignException e) {

            throw new RuntimeException("积分扣减失败: " + e.getMessage(), e);
        } catch (DataAccessException e) {

            throw new RuntimeException("支付记录保存失败", e);
        }
    }

    /**
     * 验证支付密码
     * @param uaaId
     * @param payPassword
     */
    private void validatePayPassword(Long uaaId, String payPassword) {
        boolean isValid = uaaFeignClient.validatePayPassword(uaaId, payPassword);
        if (!isValid) {
            throw new RuntimeException("支付密码验证失败");
        }
    }

    private ProfitPayRecord buildPayRecord(Long uaaId, String orderNo, PayTypeEnum payType, PayStatusEnum payStatus) {
        return ProfitPayRecord.builder()
                .seqNo(WorkNoGeneratorUtil.generate("PEQ"))
                .orderNo(orderNo)
                .payUser(uaaId)
                .payType(payType != null ? payType.getCode() : null)
                .payStatus(payStatus != null ? payStatus.getCode() : null)
                .createTime(new Date())
                .updateTime(new Date())
                .isDelete(0)
                .build();
    }

    private ProfitBalanceRecord buildBalanceRecord(Long uaaId, BigDecimal amount) {
        return ProfitBalanceRecord.builder()
                .uaaId(uaaId)
                .balance(amount)
                .recordType(1) // 支出
                .remark("订单支付")
                .createTime(new Date())
                .updateTime(new Date())
                .isDelete(0)
                .build();
    }

    private ProfitPointsRecord buildPointsRecord(Long uaaId, Integer amount) {
        return ProfitPointsRecord.builder()
                .uaaId(uaaId)
                .points(amount)
                .recordType(1) // 支出
                .remark("订单支付")
                .createTime(new Date())
                .updateTime(new Date())
                .isDelete(0)
                .build();
    }

}
