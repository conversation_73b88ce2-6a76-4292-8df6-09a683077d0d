package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.ProfitPayRecordDao;
import com.jmt.dao.ProfitRechargeRecordDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.ProfitRechargeRecordDTO;
import com.jmt.model.profit.dto.RechargeDTO;
import com.jmt.model.profit.entity.ProfitPayRecord;
import com.jmt.model.profit.entity.ProfitRechargeRecord;
import com.jmt.util.WorkNoGeneratorUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jmt.enums.PayTypeEnum;

import javax.annotation.Resource;
import java.util.List;

@Service
public class RechargeService extends BaseService {
    @Resource
    private ProfitRechargeRecordDao profitRechargeRecordDao;
    @Resource
    private ProfitPayRecordDao profitPayRecordDao;

    @Resource
    private UaaFeignClient uaaFeignClient;

    /**
     * 分页查询退款流水
     * @param pageQuery
     * @return
     */
    public PageResult<ProfitRechargeRecordDTO> getPage(PageQuery<ProfitRechargeRecordDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ProfitRechargeRecordDTO profitPayRecordDTO = pageQuery.getQueryData();
        List<ProfitRechargeRecordDTO> list = profitRechargeRecordDao.selectByCondition(profitPayRecordDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }
    @Transactional(rollbackFor = Exception.class)
    public void submitRecharge(RechargeDTO dto) {
        ProfitPayRecord payRecord = profitPayRecordDao.selectByOrderNo(dto.getOrderNo());

        //  校验订单是否可退款
        if(payRecord.getPayStatus()!=1){
            throw new IllegalArgumentException("未完成支付，无法退款");
        }


        try {
            boolean refundSuccess = false;
            PayTypeEnum payType = PayTypeEnum.getByCode(payRecord.getPayType());

            switch (payType) {
                case POINTS_PAY:
                    refundSuccess = rechargeByPoints(payRecord,dto.getBusUaaId());

                    break;
                case BALANCE_PAY:
                    refundSuccess = rechargeByBalance(payRecord,dto.getBusUaaId());

                    break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
                default:
                    throw new RuntimeException("不支持的支付方式: " + payType.getMessage());
            }

            // 更新退款流水
            if (refundSuccess) {
                ProfitRechargeRecord record = buildRechargeRecord(payRecord,payRecord.getPayType());
                profitRechargeRecordDao.insert(record);
            } else {
                throw new RuntimeException("退款处理失败:" );
            }
        } catch (Exception e) {
            throw new RuntimeException("退款处理异常: " + e.getMessage());
        }
    }
    private ProfitRechargeRecord buildRechargeRecord(ProfitPayRecord payRecord, int rechargeType) {
        return ProfitRechargeRecord.builder()
                .seqNo(WorkNoGeneratorUtil.generate("REQ"))
                .orderNo(payRecord.getOrderNo())
                .rechargeUser(payRecord.getPayUser())
                .rechargeAmount(payRecord.getPayAmount())
                .rechargeType(rechargeType)
                .rechargeStatus(1)
                .isDelete(0)
                .build();
    }

    // 积分退款
    private boolean rechargeByPoints(ProfitPayRecord payRecord,Long busUaaId) {
        // 返还用户积分
        uaaFeignClient.increasePoints(payRecord.getPayUser(), payRecord.getPayAmount().intValue());
        uaaFeignClient.decreasePoints(busUaaId, payRecord.getPayAmount().intValue());
        return true;
    }

    // 余额退款
    private boolean rechargeByBalance(ProfitPayRecord payRecord,Long busUaaId) {
        // 返还用户余额
        uaaFeignClient.increaseBalance(payRecord.getPayUser(), payRecord.getPayAmount());
        uaaFeignClient.decreaseBalance(busUaaId, payRecord.getPayAmount());
        return true;
    }





}
