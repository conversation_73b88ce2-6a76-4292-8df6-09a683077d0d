package com.jmt.model.jhh.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SalesmanInfo {
    private Long id;

    private Long uaaId;

    private String salesmanName;

    private String salesmanNo;

    private String headImg;

    private Integer salesmanType;

    private String operatorNo;

    private String dutyName;

    private String telPhone;

    private Integer isFirstLogin;

    private Integer fStatus;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;
}
