package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.CmBusinessInfoService;
import com.jmt.service.CmUserService;
import com.jmt.util.LoginUserUtil;
import com.jmt.util.WorkNoGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/business")
public class CmBusinessInfoController extends BaseController {
    @Resource
    private CmBusinessInfoService cmBusinessInfoService;

    @Resource
    private CmUserService cmUserService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<CmBusinessInfo> pageQuery){
        PageResult<CmBusinessInfo> pageResult = cmBusinessInfoService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 新增
     * @param cmBusinessInfo
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmBusinessInfo cmBusinessInfo, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
        cmBusinessInfo.setUserId(cmUser.getId());
        cmBusinessInfo.setTelPhone(cmUser.getTelPhone());
        //随机生成商家编号
        String busNo = WorkNoGeneratorUtil.generate("BNO");
        cmBusinessInfo.setBusNo(busNo);
        try {
            int result = cmBusinessInfoService.createBusinessInfo(cmBusinessInfo);

            if (result > 0) {
                cmUserService.updateUserRoleType(cmBusinessInfo.getUserId(), 1);
                return super.responseSuccess("新增成功");
            } else {
                return super.responseFail("新增商家失败");
            }
        } catch (Exception e) {
            log.error("新增商家信息失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param cmBusinessInfo
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmBusinessInfo cmBusinessInfo) {
        cmBusinessInfoService.updateBusinessInfo(cmBusinessInfo);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmBusinessInfoService.deleteBusinessInfo(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.getBusinessInfoById(id);
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

    /**
     * 根据userId查询
     * @param userId
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/user/{userId}")
    public String getByUaaId(@PathVariable Long userId) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.selectByUserId(userId);
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

    /**
     * 审核商家
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                      @RequestParam Integer auditStatus,
                                      @RequestParam(required = false) String reason) {
        try {
             cmBusinessInfoService.auditBusiness(id, auditStatus, reason);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }

    }
    @ResponseBody
    @GetMapping("/v1/getByBusinessNo")
    public String getByBusinessNo(@RequestParam String businessNo) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.getBusinessInfoByBusinessNo(businessNo);
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

}
