//package com.jmt.service;
//
//import com.github.pagehelper.PageHelper;
//import com.jmt.base.BaseService;
//import com.jmt.model.DemoDto;
//import com.jmt.model.page.PageQuery;
//import com.jmt.model.page.PageResult;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//
//@Service
//public class DemoService extends BaseService {
//
//    @Resource
//    private DemoDao demoDao;
//
//    public PageResult<DemoDto> getPage(PageQuery<DemoDto> pageQuery) {
//        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
//        DemoDto demoDto = pageQuery.getQueryData();
//        List<DemoDto> list = demoDao.getPage(demoDto);
//        if (list != null) {
//            return new PageResult<>(list);
//        }
//        return null;
//    }
//
//    public DemoDto getInfo(Integer id) {
//        return demoDao.getInfo(id);
//    }
//
//    @Transactional
//    public Integer add(DemoDto sysUser){
//        return null;
//    }
//}
