package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.EqOperatorStockRecordDto;
import com.jmt.model.eq.dto.RequestTrans;
import com.jmt.model.eq.vo.EqOperatorStockRecordVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.SalesmanEqService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eq/SalesmanEq/v1")
public class SalesmanEqController extends BaseController {
    @Resource
    private SalesmanEqService salesmanEqService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<EqOperatorStockRecordDto> pageQuery) {
        PageResult<EqOperatorStockRecordVo> page = salesmanEqService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Integer id) {
        EqOperatorStockRecordVo eqOperatorStockVo = salesmanEqService.getInfo(id);
        return super.responseSuccess(eqOperatorStockVo,"查询成功");
    }
}
