package com.jmt.controller;


import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.uaa.dto.ChangePasswordDTO;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.uaa.UaaUser;
import com.jmt.service.UaaUserService;
import com.jmt.service.UaaUserWalletService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/uaa/*")
public class UaaController extends BaseController {

    @Resource
    private UaaUserService uaaUserService;

    @Resource
    private UaaUserWalletService uaaUserWalletService;

    /**
     * 新增账户
     * @param userDTO
     * @return
     */
    @PostMapping("/v1/addUaaUser")
    @ResponseBody
    public String registerUser(@RequestBody UaaUserDTO userDTO) {
        try {
            UaaUser user = uaaUserService.registerUser(userDTO);
            uaaUserWalletService.createWallet(user.getUaaId());
            return super.responseSuccess("用户新增成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 修改账户信息
     * @param uaaUser
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody UaaUser uaaUser, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        uaaUser.setUaaId(loginUser.getUaaId());
        uaaUserService.update(uaaUser);
        return super.responseSuccess("修改成功");
    }

    /**
     * 修改账户密码
     * @param dto
     * @return
     */
    @PostMapping("/v1/reset")
    public String changePassword(@RequestBody ChangePasswordDTO dto, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            dto.setUaaId(loginUser.getUaaId());
            uaaUserService.changePassword(dto);
            return super.responseSuccess("密码修改成功");
        } catch (RuntimeException e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 账号解锁
     */
    @PostMapping("/v1/unlock")
    @ResponseBody
    public String unlock() {
        return super.responseSuccess("解锁成功");
    }
}
