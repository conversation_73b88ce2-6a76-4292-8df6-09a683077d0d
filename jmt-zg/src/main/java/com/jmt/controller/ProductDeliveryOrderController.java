package com.jmt.controller;

import com.github.pagehelper.PageInfo;
import com.google.zxing.WriterException;
import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.ZgProductDeliveryOrder;
import com.jmt.service.ProductDeliveryOrderService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 商品配送单管理控制器
 * 提供商品配送单的分页查询、收货核销、二维码生成、确认以及拒绝等功能
 */
@RestController
@RequestMapping("/zg/DeliveryOrder/v1")
public class ProductDeliveryOrderController extends BaseController {

    @Resource
    private ProductDeliveryOrderService productDeliveryOrderService;

    /**
     * 商品配送单分页查询接口
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 商品配送单分页数据
     */
    @GetMapping("/page")
    public String getPage(@RequestBody PageQuery<ZgProductDeliveryOrder> pageQuery) {
        PageInfo<ZgProductDeliveryOrder> page = productDeliveryOrderService.getDeliveryOrderPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 商品配送单收货核销接口
     * @param orderNo 配送单编号
     * @return 操作结果（成功/失败）
     */
    @PostMapping("/verifyReceipt")
    public String verifyReceipt(@RequestParam String  orderNo) {
        boolean result = productDeliveryOrderService.verifyReceipt(orderNo);
        return result ? super.responseSuccess("订单收货核销成功") : super.responseFail("订单收货核销失败");
    }

    /**
     * 商品配送单收货核销二维码生成接口
     * @param orderNo 配送单编号
     * @return 二维码图片的字节数组（PNG格式）
     * @throws WriterException 二维码生成异常
     * @throws IOException IO异常
     */
    @GetMapping(value = "/generateReceiptQRCode", produces = MediaType.IMAGE_PNG_VALUE)
    public byte[] generateReceiptQRCode(@RequestParam String  orderNo) throws WriterException, IOException {
        return productDeliveryOrderService.generateReceiptQRCode(orderNo);
    }

    /**
     * 商品配送单确认接口
     * @param orderNo 配送单编号
     * @return 操作结果
     */
    @PostMapping("/confirm")
    public String confirmDeliveryOrder(@RequestParam String orderNo) {
        boolean result = productDeliveryOrderService.confirmDeliveryOrder(orderNo);
        return result ? super.responseSuccess("订单确认成功") : super.responseFail("订单确认失败");
    }

    /**
     * 商品配送单拒绝接口
     * @param orderNo 配送单编号
     * @param reason 拒绝原因
     * @return 操作结果
     */
    @PostMapping("/reject")
    public String rejectDeliveryOrder(@RequestParam String orderNo, @RequestParam String reason) {
        boolean result = productDeliveryOrderService.rejectDeliveryOrder(orderNo, reason);
        return result ? super.responseSuccess("订单拒绝成功") : super.responseFail("订单拒绝失败");
    }
}