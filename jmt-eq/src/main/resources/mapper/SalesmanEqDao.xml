<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.SalesmanEqDao">
    <select id="getPage" resultType="com.jmt.model.eq.vo.EqOperatorStockRecordVo">
        SELECT id,operatorNo,recordType,eqRecordTotal,eqAnum,eqBnum,eqCnum,evidence,remark,createTime,updateTime,isDelete
        FROM eq_operator_stock_record
        <where>
            <if test="queryData != null and queryData.operatorNo != null">
                operatorNo = #{queryData.operatorNo}
            </if>
            <if test="queryData != null and queryData.recordType != null">
                and recordType = #{queryData.recordType}
            </if>
            <if test="queryData != null and queryData.eqRecordTotal != null">
                and eqRecordTotal = #{queryData.eqRecordTotal}
            </if>
            <if test="queryData != null and queryData.eqAnum != null">
                and eqAnum = #{queryData.eqAnum}
            </if>
            <if test="queryData != null and queryData.eqBnum != null">
                and eqBnum = #{queryData.eqBnum}
            </if>
            <if test="queryData != null and queryData.eqCnum != null">
                and eqCnum = #{queryData.eqCnum}
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.jmt.model.eq.vo.EqOperatorStockRecordVo">
        SELECT id,operatorNo,recordType,eqRecordTotal,eqAnum,eqBnum,eqCnum,evidence,remark,createTime,updateTime,isDelete
        FROM eq_operator_stock_record
        WHERE id = #{id}
    </select>
</mapper>