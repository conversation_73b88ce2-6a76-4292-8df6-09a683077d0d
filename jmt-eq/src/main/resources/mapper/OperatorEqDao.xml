<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.OperatorEqDao">
    <update id="transferStock">
        UPDATE eq_operator_stock AS target
            INNER JOIN (
            SELECT
            operatorNo,
            eqStockTotal,
            eqAnum,
            eqBnum,
            eqCnum,
            onlineNum,
            offlineNum
            FROM eq_operator_stock
            WHERE operatorNo = #{sourceOperatorNo}
            AND isDelete = 0
            ) AS source ON 1=1
            SET
                target.eqStockTotal = target.eqStockTotal + source.eqStockTotal,
                target.eqAnum = target.eqAnum + source.eqAnum,
                target.eqBnum = target.eqBnum + source.eqBnum,
                target.eqCnum = target.eqCnum + source.eqCnum,
                target.onlineNum = target.onlineNum + source.onlineNum,
                target.offlineNum = target.offlineNum + source.offlineNum
        WHERE
            target.operatorNo = #{targetOperatorNo}
          AND target.isDelete = 0;
    </update>
    <update id="clearOperatorEq">
        UPDATE eq_operator_stock
        SET
            onlineNum = 0,
            offlineNum = 0,
            eqAnum = 0,
            eqBnum = 0,
            eqCnum = 0
        WHERE operatorNo = #{operatorNo}
          AND isDelete = 0
    </update>
    <select id="getPage" resultType="com.jmt.model.eq.vo.EqBusinessSetupVo">
        SELECT id,operatorNo,eqStockTotal,eqAnum,eqBnum,eqCnum,onlineNum,offlineNum,createTime,updateTime,isDelete
        FROM eq_operator_stock
        <where>
            <if test="queryData != null and operatorNo != null">
                operatorNo = #{operatorNo}
            </if>
            <if test="queryData != null and eqStockTotal != null">
                and eqStockTotal = #{eqStockTotal}
            </if>
            <if test="queryData != null and eqAnum != null">
                and eqAnum = #{eqAnum}
            </if>
            <if test="queryData != null and eqBnum != null">
                and eqBnum = #{eqBnum}
            </if>
            <if test="queryData != null and eqCnum != null">
                and eqCnum = #{eqCnum}
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.jmt.model.eq.vo.EqOperatorStockVo">
        SELECT id,operatorNo,eqStockTotal,eqAnum,eqBnum,eqCnum,onlineNum,offlineNum,createTime,updateTime,isDelete
        FROM eq_operator_stock
        WHERE id = #{id}
    </select>
</mapper>