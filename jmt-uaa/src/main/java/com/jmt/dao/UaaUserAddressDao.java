package com.jmt.dao;

import com.jmt.model.uaa.UaaUserAddress;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UaaUserAddressDao {
    Integer insert(UaaUserAddress uaaUserAddress);

    Integer updateById(UaaUserAddress uaaUserAddress);

    Integer deleteById(@Param("id") Long id);

    UaaUserAddress selectById(@Param("id") Long id);

    List<UaaUserAddress> selectByUaaId(@Param("uaaId") Long uaaId);

    UaaUserAddress findDefaultByUaaId(Long uaaId);

    Integer countByUaaId(Long uaaId);
}
