<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgSupplierInfoAuditMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgSupplierInfoAudit">
        <id property="id" column="id" />
        <result property="auditUser" column="auditUser" />
        <result property="supplierNo" column="supplierNo" />
        <result property="auditStatus" column="auditStatus" />
        <result property="reason" column="reason" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, auditUser, supplierNo, auditStatus, reason, createTime,
        updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info_audit
        where id = #{id}
    </select>
    <select id="selectByAuditSupplierNo" resultType="com.jmt.model.zg.ZgSupplierInfoAudit"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info_audit
        where supplierNo = #{supplierNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_supplier_info_audit
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgSupplierInfoAudit" useGeneratedKeys="true">
        insert into zg_supplier_info_audit
        (id, auditUser, supplierNo, auditStatus, reason, createTime,
         updateTime, isDelete)
        values (#{id}, #{auditUser}, #{supplierNo}, #{auditStatus}, #{reason}, #{createTime},
                #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgSupplierInfoAudit" useGeneratedKeys="true">
        insert into zg_supplier_info_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="auditUser != null">auditUser,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="auditStatus != null">auditStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgSupplierInfoAudit">
        update zg_supplier_info_audit
        <set>
            <if test="auditUser != null">
                auditUser = #{auditUser},
            </if>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgSupplierInfoAudit">
        update zg_supplier_info_audit
        set
            auditUser = #{auditUser},
            supplierNo = #{supplierNo},
            auditStatus = #{auditStatus},
            reason = #{reason},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>