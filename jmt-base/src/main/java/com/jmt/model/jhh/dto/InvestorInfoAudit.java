package com.jmt.model.jhh.dto;

import lombok.Data;

import java.util.Date;

@Data
public class InvestorInfoAudit {
    //收益人id
    private Long id;
    //uaaid
    private Long auditUser;
    //收益人编号
    private String investorNo;
    //审核状态
    private Integer auditStatus;
    //审核建议
    private String reason;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;
    //删除状态
    private Integer isDelete;
}
