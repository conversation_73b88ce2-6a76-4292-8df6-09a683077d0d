package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-jhh",path ="/jhh/investor/v1",configuration = FeignConfig.class)
public interface JhhInvestorFeignClient {
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam ("id") Long id);
    @GetMapping(value = "/getUserInfoByInvestorNo")
    public String getInfoByInvestorNo(@RequestParam("investorNo") String  investorNo);
    @GetMapping(value = "/getInvestorInfoByUaaId")
    String getInvestorInfoByUaaId(@RequestParam ("uaaId") Long uaaId);
}
