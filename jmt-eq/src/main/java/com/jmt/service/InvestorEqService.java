package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.InvestorEqDao;
import com.jmt.model.eq.dto.EqBusinessSetupDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InvestorEqService {
    @Resource
    private InvestorEqDao investorEqDao;
    /**
     * 按id查询
     * @param id
     * @return
     */
    public EqOperatorStockVo getInfo(Integer id) {
        return investorEqDao.getInfo(id);
    }
}
