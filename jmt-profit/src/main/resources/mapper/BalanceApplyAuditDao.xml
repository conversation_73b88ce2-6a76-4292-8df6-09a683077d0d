<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.BalanceApplyAuditDao">
	<insert id="add">
		INSERT INTO profit_balance_cash_audit
		    (auditUser,applyNo,auditStatus,reason,createTime,updateTime,isDelete)
		VALUES
		    (#{auditUser},#{applyNo},#{auditStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
	</insert>
	<update id="update">
		UPDATE profit_balance_cash_audit
		<set>
			<if test="applyNo != null">
				 applyNo = #{applyNo},
			</if>
			<if test="auditUser != null">
				auditUser = #{auditUser},
			</if>
			<if test="auditStatus != null">
				auditStatus = #{auditStatus},
			</if>
		</set>
	</update>
	<select id="getInfoByApplyNo" resultType="com.jmt.model.profit.dto.BalanceCashApplyAudit">
		SELECT
		    id,
			auditUser,
		    applyNo,
		    auditStatus,
		    reason,
		    createTime,
		    updateTime
		FROM profit_balance_cash_audit
		WHERE applyNo = #{applyNo}
		AND isDelete = 0
	</select>
</mapper>