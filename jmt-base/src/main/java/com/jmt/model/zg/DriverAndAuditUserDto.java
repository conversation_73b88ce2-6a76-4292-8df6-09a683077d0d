package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;
@Data
public class DriverAndAuditUserDto {
    /**
     * zg_user的id
     */
    private Long userId;
    private Long auditUser;
    /**
     * 配送员编号
     */
    private String driverno;

    /**
     * 配送员姓名
     */
    private String drivername;

    /**
     * 身份证号
     */
    private String idno;

    /**
     * 手机号
     */
    private String telphone;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 配送范围
     */
    private String areascope;

    /**
     * 车辆照片
     */
    private String carphoto;

    /**
     * 车牌号
     */
    private String carnumber;

    /**
     * 车辆最大载重，单位KG
     */
    private Integer maxload;

    /**
     * 星级 1-5 标识1-5个星
     */
    private Integer driverstar;

    /**
     * 备注说明
     */
    private String remark;



    /**
     * 是否已实名认证 0-否 1-是
     */
    private Integer isrealauth;

    /**
     * 更新时间
     */
    private Date createtime;

    /**
     * 更新时间
     */
    private Date updatetime;

    /**
     * 是否删除  0-否 1-是
     */
    private Integer isdelete;

}
