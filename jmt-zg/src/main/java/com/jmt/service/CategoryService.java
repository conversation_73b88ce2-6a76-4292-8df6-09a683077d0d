package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgCategoryMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.ZgCategory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CategoryService extends BaseService {
    @Resource
    private ZgCategoryMapper categoryMapper;

    /**
     * 新增分类
     * 自动维护创建/更新时间，默认标记为未删除
     */
    public void addCategory(ZgCategory category) {
        category.setId(null); // 确保ID自增
        Date now = new Date();
        category.setCreateTime(now);
        category.setUpdateTime(now);
        category.setIsDelete(0);
        categoryMapper.insert(category);
    }

    /**
     * 更新分类
     * 维护更新时间，保留未删除状态
     */
    public void updateCategory(ZgCategory category) {
        category.setUpdateTime(new Date());
        category.setIsDelete(0); // 确保更新时不修改删除状态（删除有单独方法）
        categoryMapper.updateByPrimaryKeySelective(category);
    }

    /**
     * 逻辑删除分类
     * 标记isDelete=1，更新操作时间
     */
    public void deleteCategory(ZgCategory category) {
        category.setUpdateTime(new Date());
        category.setIsDelete(1);
        categoryMapper.updateByPrimaryKeySelective(category);
    }

    /**
     * 构建分类树形结构（用于下拉选择等场景）
     * 处理父子层级关系，顶级节点为parentId=null或0
     */
    public List<Map<String, Object>> buildCategoryTreeForSelect() {
        // 查询所有未删除的分类
        ZgCategory query = new ZgCategory();
        query.setIsDelete(0);
        List<ZgCategory> allCategories = categoryMapper.getCategoryPage(query);

        if (allCategories.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建父ID到子分类的映射（快速查找子节点）
        Map<Long, List<ZgCategory>> parentToChildren = allCategories.stream()
                .collect(Collectors.groupingBy(
                        cat -> cat.getParentId() == null ? 0L : cat.getParentId()
                ));

        // 顶级节点：parentId为null或0的分类
        List<ZgCategory> rootNodes = parentToChildren.getOrDefault(0L, Collections.emptyList());

        // 递归构建树形结构
        return rootNodes.stream()
                .map(root -> buildTreeNode(root, parentToChildren))
                .collect(Collectors.toList());
    }

    /**
     * 递归构建单个分类节点及其子节点
     */
    private Map<String, Object> buildTreeNode(ZgCategory category, Map<Long, List<ZgCategory>> parentToChildren) {
        Map<String, Object> node = new LinkedHashMap<>();
        node.put("id", category.getId());
        node.put("categoryName", category.getCategoryName());
        node.put("categoryNo", category.getCategoryNo());
        node.put("parentId", category.getParentId());

        // 递归处理子节点
        List<Map<String, Object>> childrenNodes = parentToChildren.getOrDefault(category.getId(), Collections.emptyList())
                .stream()
                .map(child -> buildTreeNode(child, parentToChildren))
                .collect(Collectors.toList());

        node.put("children", childrenNodes);
        return node;
    }

    /**
     * 分类分页查询
     */
    public PageResult<ZgCategory> getCategoryPage(PageQuery<ZgCategory> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgCategory queryData = pageQuery.getQueryData();
        List<ZgCategory> list = categoryMapper.getCategoryPage(queryData);
        return new PageResult<>(list);
    }

    /**
     * 根据ID查询分类详情
     */
    public ZgCategory getCategory(Long categoryId) {
        return categoryMapper.selectByPrimaryKey(categoryId);
    }
}