package com.jmt.model.profit.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LklWechatPayDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String merchantNo; // 商户号

    private String termNo; // 终端号
    private String outTradeNo; //商户交易流水号

    private Integer totalAmount; // 单位：分

    private String notifyUrl; //商户通知地址

    private String remark; //备注

    private String ipAddress; //请求方IP地址

    private String subAppId; //子商户公众账号ID 微信小程序支付-71、公众号支付-51、微信app支付-61

    private String userId; //用户标识

    private String goodsTag; //订单优惠标记
    private LklSceneInfo sceneInfo; //场景信息

    @Valid
    private List<LklGoodsDetailDTO> goodsDetails;
}

