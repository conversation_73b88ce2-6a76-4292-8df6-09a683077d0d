package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-cm",path ="/cm/business/v1",configuration = FeignConfig.class)
public interface CmBusinessInfoFeignClient {
    @GetMapping("/{id}")
    public String getById(@PathVariable("id") Long id);
    @GetMapping("/getByBusinessNo")
    public String getByBusinessNo(@RequestParam("businessNo") String businessNo);
}
