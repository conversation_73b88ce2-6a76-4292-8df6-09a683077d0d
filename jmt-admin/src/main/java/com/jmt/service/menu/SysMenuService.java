package com.jmt.service.menu;

import com.jmt.base.BaseService;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.SysMenuDao;
import com.jmt.model.admin.menu.AppRouteRecord;
import com.jmt.model.admin.menu.RouteMeta;
import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.admin.permission.SysPermissionVO;
import com.jmt.model.admin.user.SysUser;
import com.jmt.model.admin.user.SysUserDetail;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.service.user.SysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysMenuService extends BaseService {

    @Resource
    private SysMenuDao sysMenuDao;
    @Resource
    private SysUserService sysUserService;
    public List<AppRouteRecord> getUserMenuList(LoginUaaUser loginUaaUser) {
        if (loginUaaUser.getIsRoot() == 1) {
            // 管理员：直接返回构建的路由树
            return buildRouteTree(JmtConstant.PARENT_ID);
        } else {
            SysUser sysUser = sysUserService.getInfoByUaaId(loginUaaUser.getUaaId());
            SysUserDetail userDetail = sysUserService.getUserDetail(sysUser);
            List<SysPermissionVO> permissions = userDetail.getPermissions();

            // 初始化总列表，用于合并所有路由
            List<AppRouteRecord> allRouteRecords = new ArrayList<>();

            if (permissions != null && !permissions.isEmpty()) {
                // 循环遍历每个权限ID，构建路由并合并
                for (SysPermissionVO permission : permissions) {
                    List<AppRouteRecord> routeRecords = buildRouteTree(permission.getParentId());
                    if (routeRecords != null && !routeRecords.isEmpty()) {
                        allRouteRecords.addAll(routeRecords);
                    }
                }
            }

            // 返回合并后的列表（去重可选，根据需求添加）
            return allRouteRecords;
        }
    }

    /**
     * 递归构建路由树形结构（分离页面路由和操作权限，不含角色信息）
     * @param parentId 父节点ID
     * @return 路由列表
     */
    private List<AppRouteRecord> buildRouteTree(Long parentId) {
        List<SysPermission> allPermissions = sysMenuDao.getSysMenuListByParentId(parentId);
        if (allPermissions == null || allPermissions.isEmpty()) {
            return Collections.emptyList();
        }

        // 分离：页面路由（permissType 1-3）和操作权限（permissType 4-5，如按钮）
        List<SysPermission> pagePermissions = allPermissions.stream()
                .filter(p -> p.getPermissType() != null && p.getPermissType() <= 3)
                .collect(Collectors.toList());

        return pagePermissions.stream()
                .map(pagePerm -> {
                    AppRouteRecord route = new AppRouteRecord();

                    route.setId(pagePerm.getId());
                    route.setName(pagePerm.getRouterName());
                    route.setComponent(pagePerm.getRouterComponent());
                    route.setPath(pagePerm.getRouterPath());
                    List<RouteMeta.AuthItem> authList = Collections.emptyList();
                    route.setMeta(buildRouteMeta(pagePerm, parentId, authList));
                    route.setChildren(buildRouteTree(pagePerm.getId()));

                    return route;
                })
                .collect(Collectors.toList());
    }


    /**
     * 构建完整的路由元数据（补充前端所需字段，不含角色信息）
     */
    private RouteMeta buildRouteMeta(SysPermission perm, Long parentId, List<RouteMeta.AuthItem> authList) {
        RouteMeta meta = new RouteMeta();
        meta.setTitle(perm.getPermissName());
        meta.setIcon(perm.getRouterIcon());
        meta.setIsHide(perm.getPermissType() >= 4);
        meta.setIsHideTab(perm.getPermissType() >= 4);
        meta.setKeepAlive(perm.getPermissType() == 2);
        meta.setAuthList(authList);
        meta.setIsFirstLevel(JmtConstant.PARENT_ID.equals(parentId));
        meta.setActivePath(perm.getPermissKey());
        return meta;
    }

}
