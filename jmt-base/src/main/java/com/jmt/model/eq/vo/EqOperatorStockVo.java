package com.jmt.model.eq.vo;

import lombok.Data;

import java.util.Date;

/**
 * 运营商库存表实体类
 * 对应表：eq_operator_stock
 */
@Data
public class EqOperatorStockVo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 运营商编号，关联 jhh_operator_info 的 operatorNo
     */
    private String operatorNo;
    /**
     * 库存总数
     */
    private Integer eqStockTotal;
    /**
     * A类数量
     */
    private Integer eqAnum;
    /**
     * B类数量
     */
    private Integer eqBnum;
    /**
     * C类数量
     */
    private Integer eqCnum;
    /**
     * 在线数量
     */
    private Integer onlineNum;
    /**
     * 离线数量
     */
    private Integer offlineNum;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除，0-否 1-是
     */
    private Integer isDelete;
}