package com.jmt.dao;

import com.jmt.model.profit.ProfitBalanceCashApply;

/**
* <AUTHOR>
* @description 针对表【profit_balance_cash _apply(余额提现申请表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitBalanceCashApply
*/
public interface ProfitBalanceCashApplyMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitBalanceCashApply record);

    int insertSelective(ProfitBalanceCashApply record);

    ProfitBalanceCashApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitBalanceCashApply record);

    int updateByPrimaryKey(ProfitBalanceCashApply record);

}
