package com.jmt.service.menu;

import com.jmt.base.BaseService;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.*;
import com.jmt.model.admin.menu.AppRouteRecord;
import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.auth.LoginUaaUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


@Service
public class SysMenuService extends BaseService {

    @Resource
    private SysMenuDao sysMenuDao;

    public List<AppRouteRecord> getUserMenuList(LoginUaaUser loginUaaUser) {
        if (loginUaaUser.getIsRoot() == 1) {
            List<SysPermission> permissions = sysMenuDao.getSysMenuListByParentId(JmtConstant.PARENT_ID);
            return Collections.emptyList();
        } else {
            return Collections.emptyList();
        }
    }
}
