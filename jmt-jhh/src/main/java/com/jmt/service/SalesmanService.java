package com.jmt.service;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.exception.BusinessException;
import com.jmt.dao.SalesmanDao;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.jhh.dto.SalesmanInfoDto;
import com.jmt.model.jhh.vo.SalesmanSelectVo;
import com.jmt.model.jhh.vo.SalesmanVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JhhUserNoUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Service
public class SalesmanService {
    @Resource
    private SalesmanDao salesmanDao;
    @Resource
    private UaaFeignClient uaaFeignClient;
    /**
     * 获取分页数据
     *
     * @param pageQuery
     * @return
     */
    public PageResult<SalesmanVo> getPage(PageQuery<SalesmanInfoDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<SalesmanVo> page = salesmanDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 运营商添加业务员
     *
     * @param salesmanInfoDto
     */
    public void add(SalesmanInfoDto salesmanInfoDto) {
        if (salesmanInfoDto.getSalesmanType() !=1 && salesmanInfoDto.getSalesmanType() !=0){
            throw new BusinessException("请选择业务员类型");
        }
        String prefix = "SA";
        String salesmanNo = JhhUserNoUtil.generate(prefix);
        SalesmanInfo salesmanInfo = new SalesmanInfo();
        BeanUtils.copyProperties(salesmanInfoDto,salesmanInfo);
        LocalDateTime now = LocalDateTime.now();
        salesmanInfo.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        salesmanInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        salesmanInfo.setIsDelete(0);
        salesmanInfo.setIsFirstLogin(1);
        salesmanInfo.setFStatus(0);
        salesmanInfo.setOperatorNo(salesmanInfoDto.getOperatorNo());
        salesmanInfo.setSalesmanNo(salesmanNo);
        UaaUserDTO uaaUserDTO = new UaaUserDTO();
        uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
        uaaUserDTO.setLoginName(salesmanInfo.getTelPhone());
        uaaUserDTO.setTelPhone(salesmanInfo.getTelPhone());
        String response = uaaFeignClient.registerUser(uaaUserDTO);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUser user = gson.fromJson(dataElement, UaaUser.class);
        Long uaaId = user.getUaaId();
        salesmanInfo.setUaaId(uaaId);
        salesmanDao.add(salesmanInfo);
    }
    /**
     * 修改业务员信息
     *
     * @param salesmanInfoDto
     */
    public void update(SalesmanInfoDto salesmanInfoDto) {
        SalesmanInfo info = salesmanDao.getInfo(salesmanInfoDto.getId());
        if (info == null || info.getIsDelete() == 1){
            throw new BusinessException("业务员不存在");
        }
        SalesmanInfo salesmanInfo = new SalesmanInfo();
        BeanUtils.copyProperties(salesmanInfoDto,salesmanInfo);
        LocalDateTime now = LocalDateTime.now();
        salesmanInfo.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        salesmanInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        salesmanInfo.setIsDelete(0);
        salesmanInfo.setIsFirstLogin(0);
        salesmanInfo.setOperatorNo(info.getOperatorNo());
        salesmanDao.update(salesmanInfo);
    }
    /**
     * 删除业务员
     *
     * @param id
     */
    public void delete(Long id) {
        SalesmanInfo info = salesmanDao.getInfo(id);
        if(info.getIsDelete() == 1){
            throw new BusinessException("已删除");
        }
        salesmanDao.delete(id);
    }
    /**
     * 获取业务员下拉框
     *
     * @return
     */
    public List<SalesmanSelectVo> getSalesmanDropdown() {
        return salesmanDao.getSalesmanDropdown();
    }
    /**
     * 根据uaaId获取业务员信息
     *
     * @param uaaId
     * @return
     */
    public SalesmanInfo getSalesmanInfoByUaaId(Long uaaId) {
        return salesmanDao.getByUaaId(uaaId);
    }
    /**
     * 根据id获取业务员信息
     *
     * @param id
     * @return
     */
    public SalesmanInfo getInfo(Long id) {
        return salesmanDao.getInfo(id);
    }
}
