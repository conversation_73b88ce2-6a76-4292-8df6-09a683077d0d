package com.jmt.dao;

import com.jmt.model.cm.dto.CmSupplyReqInfoDTO;
import com.jmt.model.cm.entity.CmSupplyReqInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmSupplyReqInfoDao {
    Long insert(CmSupplyReqInfo record);

    Integer batchInsert(List<CmSupplyReqInfo> list);

    Integer update(CmSupplyReqInfo record);


    Integer delete(@Param("id") Long id);


    CmSupplyReqInfo selectById(Long id);


    List<CmSupplyReqInfoDTO> selectByCondition(CmSupplyReqInfoDTO condition);
}
