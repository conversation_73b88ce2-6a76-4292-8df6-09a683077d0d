package com.jmt.model.cm.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 筷圣餐谋商家信息表
* @TableName cm_business_info
*/
@Data
public class CmBusinessInfo implements Serializable {

    /**
    * 
    */
    @NotNull(message="[]不能为空")
    private Long id;
    /**
    * cms_user的id
    */
    private Long userId;
    /**
    * 商家编号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busNo;
    /**
    * 商家名称
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busName;
    /**
    * 联系人
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String linkman;
    /**
    * 联系人手机号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String telPhone;
    /**
    * 国家
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String country;
    /**
    * 省份
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String province;
    /**
    * 城市
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String city;
    /**
    * 区县
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String area;
    /**
    * 详细地址
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String address;
    /**
    * 经度
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String longitude;
    /**
    * 维度
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String latitude;
    /**
    * 运营商编号
jhh_operator_info的operatorNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String operatorNo;
    /**
    * 推荐业务编号
jhh_salesman_info的salesmanNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String refereeNo;
    /**
    * 部署业务员编号
jhh_salesman_info的salesmanNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String devloperNo;
    /**
    * 商家照片
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busPhoto;
    /**
    * 商家营业执照
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busLicense;
    /**
    * 身份证正面照片
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String identityCardFront;
    /**
    * 身份证反面照片
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String identityCardBack;
    /**
    * 经营类别
    */
    private Integer categoryId;
    /**
    * 店铺面积
    */
    private Double measure;
    /**
    * 圆桌数量
    */
    private Integer ydeskNum;
    /**
    * 方桌数量
    */
    private Integer fdeskNum;
    /**
    * 人均消费
    */
    private Integer comsumeAvg;
    /**
    * 营业时间
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String openTime;
    /**
    * 人流量
    */
    private Integer visitorAvg;
    /**
    * 星级 1-5 标识1-5个星
    */
    private Integer busStar;
    /**
    * 备注说明
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String remark;
    /**
    * 0-待审核
1-同意
2-驳回
3-冻结
    */
    private Integer auditStatus;
    /**
    * 审核意见
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String reason;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;


}
