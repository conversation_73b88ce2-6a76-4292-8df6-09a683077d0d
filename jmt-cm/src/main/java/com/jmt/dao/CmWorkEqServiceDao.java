package com.jmt.dao;

import com.jmt.model.cm.dto.CmWorkEqServiceDTO;
import com.jmt.model.cm.entity.CmWorkEqService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface CmWorkEqServiceDao {
    Integer insert(CmWorkEqService record);
    Integer update(CmWorkEqService record);

    Integer delete(Long id);

    CmWorkEqService selectById(@Param("id") Long id);

    List<CmWorkEqServiceDTO> selectByCondition(CmWorkEqServiceDTO cmWorkEqServiceDTO);
}
