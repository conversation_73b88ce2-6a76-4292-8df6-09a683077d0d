<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitShareProjectMapper">

    <sql id="Base_Column_List">
        id,parentId,projectName,orderNoPrefix,useStauts,createTime,
        updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitShareProject">
        select
        <include refid="Base_Column_List" />
        from profit_share_project
        where  id = #{id}
    </select>

    <select id="selectByOrderPrefix" resultType="com.jmt.model.profit.ProfitShareProject">
        SELECT * FROM profit_share_project
        WHERE orderNoPrefix = #{orderPrefix}
          AND useStauts = 0  -- 仅查询启用的项目
    </select>

    <select id="selectByProjectName" resultType="com.jmt.model.profit.ProfitShareProject">
        SELECT * FROM profit_share_project
        WHERE projectName = #{projectName}
          AND isDelete = 0
        LIMIT 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_share_project
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitShareProject" useGeneratedKeys="true">
        insert into profit_share_project
        ( id,parentId,projectName,orderNoPrefix,useStauts,createTime,
          updateTime,isDelete)
        values (#{id},#{parentId},#{projectName},#{orderNoPrefix},#{useStauts},#{createTime},
                #{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitShareProject" useGeneratedKeys="true">
        insert into profit_share_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parentId,</if>
            <if test="projectName != null">projectName,</if>
            <if test="orderNoPrefix != null">orderNoPrefix,</if>
            <if test="useStauts != null">useStauts,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="orderNoPrefix != null">#{orderNoPrefix},</if>
            <if test="useStauts != null">#{useStauts},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitShareProject">
        update profit_share_project
        <set>
            <if test="parentId != null">
                parentId = #{parentId},
            </if>
            <if test="projectName != null">
                projectName = #{projectName},
            </if>
            <if test="orderNoPrefix != null">
                orderNoPrefix = #{orderNoPrefix},
            </if>
            <if test="useStauts != null">
                useStauts = #{useStauts},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitShareProject">
        update profit_share_project
        set
            parentId =  #{parentId},
            projectName =  #{projectName},
            orderNoPrefix =  #{orderNoPrefix},
            useStauts =  #{useStauts},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>