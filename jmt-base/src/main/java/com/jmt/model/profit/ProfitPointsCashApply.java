package com.jmt.model.profit;

import lombok.Data;

import java.util.Date;

/**
 * 积分兑现申请表
 * @TableName profit_points_cash_apply
 */
@Data
public class ProfitPointsCashApply {
    /**
     * 主键
     */
    private Long id;

    /**
     * 申请人，uaa_user的主键uaaId
     */
    private Long applyUser;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 申请积分数
     */
    private Integer points;

    /**
     * 是否提供发票
     0-否
     1-是
     */
    private Integer invoiceProvide;

    /**
     * 发票文件
     */
    private String invoiceFile;

    /**
     * 审核状态
     0-待审核
     1-审核通过
     2-审核不通过
     3-已兑现
     */
    private String auditStatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProfitPointsCashApply other = (ProfitPointsCashApply) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getApplyUser() == null ? other.getApplyUser() == null : this.getApplyUser().equals(other.getApplyUser()))
                && (this.getApplyNo() == null ? other.getApplyNo() == null : this.getApplyNo().equals(other.getApplyNo()))
                && (this.getPoints() == null ? other.getPoints() == null : this.getPoints().equals(other.getPoints()))
                && (this.getInvoiceProvide() == null ? other.getInvoiceProvide() == null : this.getInvoiceProvide().equals(other.getInvoiceProvide()))
                && (this.getInvoiceFile() == null ? other.getInvoiceFile() == null : this.getInvoiceFile().equals(other.getInvoiceFile()))
                && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
                && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getApplyUser() == null) ? 0 : getApplyUser().hashCode());
        result = prime * result + ((getApplyNo() == null) ? 0 : getApplyNo().hashCode());
        result = prime * result + ((getPoints() == null) ? 0 : getPoints().hashCode());
        result = prime * result + ((getInvoiceProvide() == null) ? 0 : getInvoiceProvide().hashCode());
        result = prime * result + ((getInvoiceFile() == null) ? 0 : getInvoiceFile().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyUser=").append(applyUser);
        sb.append(", applyNo=").append(applyNo);
        sb.append(", points=").append(points);
        sb.append(", invoiceProvide=").append(invoiceProvide);
        sb.append(", invoiceFile=").append(invoiceFile);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", reason=").append(reason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}