<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.PointApplyAuditDao">
	<insert id="add">
		INSERT INTO profit_points_cash_apply_audit
		    (auditUser,applyNo,auditStatus,reason,createTime,updateTime,isDelete)
		VALUES
		    (#{auditUser},#{applyNo},#{auditStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
	</insert>
	<update id="update">
		UPDATE profit_points_cash_apply_audit
		<set>
			<if test="auditStatus!=null">
				auditStatus = #{auditStatus},
			</if>
		    <if test="applyNo != null">
				applyNo = #{applyNo},
			</if>
		    <if test="auditUser!=null">
				auditUser = #{auditUser},
			</if>
			<if test="reason!=null">
				reason = #{reason},
			</if>
			<if test="updateTime!=null">
				updateTime = #{updateTime},
			</if>
		</set>
		WHERE
		    id = #{id}
		AND isDelete = 0
	</update>
	<select id="getInfoByApplyNo" resultType="com.jmt.model.profit.dto.PointsCashApplyAudit">
		SELECT
		    id,
			auditUser,
		    applyNo,
		    auditStatus,
		    reason,
		    createTime,
		    updateTime,
		    isDelete
		FROM profit_points_cash_apply_audit
		WHERE applyNo = #{applyNo}
		AND isDelete = 0
	</select>
</mapper>