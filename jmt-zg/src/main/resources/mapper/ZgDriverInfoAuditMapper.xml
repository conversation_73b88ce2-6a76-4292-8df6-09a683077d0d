<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgDriverInfoAuditMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgDriverInfoAudit">
        <id property="id" column="id" />
        <result property="auditUser" column="auditUser" />
        <result property="driverNo" column="driverNo" />
        <result property="auditStatus" column="auditStatus" />
        <result property="reason" column="reason" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, auditUser, driverNo, auditStatus, reason, createTime,
        updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_driver_info_audit
        where id = #{id}
    </select>
    <select id="selectByDriverNo" resultType="com.jmt.model.zg.ZgDriverInfoAudit"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from zg_driver_info_audit
        where driverNo = #{driverNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_driver_info_audit
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgDriverInfoAudit" useGeneratedKeys="true">
        insert into zg_driver_info_audit
        (id, auditUser, driverNo, auditStatus, reason, createTime,
         updateTime, isDelete)
        values (#{id}, #{auditUser}, #{driverNo}, #{auditStatus}, #{reason}, #{createTime},
                #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgDriverInfoAudit" useGeneratedKeys="true">
        insert into zg_driver_info_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="auditUser != null">auditUser,</if>
            <if test="driverNo != null">driverNo,</if>
            <if test="auditStatus != null">auditStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="driverNo != null">#{driverNo},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgDriverInfoAudit">
        update zg_driver_info_audit
        <set>
            <if test="auditUser != null">
                auditUser = #{auditUser},
            </if>
            <if test="driverNo != null">
                driverNo = #{driverNo},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgDriverInfoAudit">
        update zg_driver_info_audit
        set
            auditUser = #{auditUser},
            driverNo = #{driverNo},
            auditStatus = #{auditStatus},
            reason = #{reason},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>