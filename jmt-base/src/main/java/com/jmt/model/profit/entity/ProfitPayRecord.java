package com.jmt.model.profit.entity;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* 支付流水表
* @TableName profit_pay_record
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfitPayRecord implements Serializable {


    private Long id;
    /**
    * 订单编号
    */
    private String seqNo;
    /**
    * 
    */
    private String orderNo;
    /**
    * 支付人，uaa_user的uaaId
    */
    private Long payUser;
    /**
    * 支付方式
0-积分支付
1-余额支付
2-微信支付
3-支付宝
    */
    private Integer payType;
    /**
    * 充值金额
    */
    private BigDecimal payAmount;
    /**
    * 支付状态
0-未支付 
1-支付成功 
2-支付失败
3-取消支付
    */
    private Integer payStatus;
    /**
    * 支付失败必填
    */
    private String reason;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;



}
