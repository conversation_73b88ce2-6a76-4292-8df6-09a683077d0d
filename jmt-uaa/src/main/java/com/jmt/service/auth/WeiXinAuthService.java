package com.jmt.service.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jmt.base.BaseService;
import com.jmt.constant.JmtConstant;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.dao.UaaAppletConfigDao;
import com.jmt.model.auth.*;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.service.auth.UaaAuthService;
import com.jmt.util.*;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class WeiXinAuthService extends BaseService {

    private static final String AUTH_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String CREATE_QR_CODE = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";
    private static final String AUTH_USER_INFO_URL = "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN";
    private static final String GET_USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN";
    private static final String TINY_APP_LOGIN = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    @Resource
    private UaaAuthService uaaAuthService;

    @Resource
    private UaaAppletConfigDao uaaAppletConfigDao;

    @Resource
    private RedisUtil redisUtil;


    public String weiXinAuthLogin(WeiXinAuthLoginDto loginDto) {
        String token = null;
        String clientId = loginDto.getClientId();
        WeiXinAppletConfig weiXinAppletConfig = uaaAppletConfigDao.loadConfigByClientId(clientId);
        if (ObjectUtil.isEmpty(weiXinAppletConfig)) {
            return super.responseFailAuth("客户端未授权微信小程序登录");
        }
        Map<String, Object> loginResult;
        String telPhone = null;
        try {
            loginResult = this.appletLogin(weiXinAppletConfig,loginDto.getCode());
        } catch (Exception e) {
            logger.error("微信小程序登录失败:",e);
            return super.responseFailAuth("微信小程序登录失败");
        }
        if (ObjectUtil.isNotEmpty(loginResult)) {
            logger.info("微信小程序登录结果："+ GsonUtil.toJson(loginResult));
            String errCode = Convert.toStr(loginResult.get("errcode"),null);
            if(StrUtil.isNotEmpty(errCode)){
                logger.error("微信小程序登录失败:" + Convert.toStr(loginResult.get("errcode"),"") +"->"+ Convert.toStr(loginResult.get("errmsg")));
                return super.responseFailAuth("微信小程序登录失败");
            }
            String sessionKey = Convert.toStr(loginResult.get("session_key"));
            //解密手机号
            String phoneInfo= AesCbcUtil.decrypt(loginDto.getEncryptedData(),sessionKey,loginDto.getIv(),"utf-8");
            Map<String, Object> phoneInfoMap = GsonUtil.fromJsonMap(phoneInfo);
            telPhone = Convert.toStr(phoneInfoMap.get("phoneNumber"));
        }
        String openId = Convert.toStr(loginResult.get("openid"));
        if (StrUtil.isEmpty(telPhone) || StrUtil.isEmpty(openId)){
            logger.error("微信小程序登录失败:手机号或openId为空");
            return super.responseFailAuth("微信小程序登录失败");
        }
        try {
            LoginUaaUser userDetails = uaaAuthService.loadUserByTelPhone(telPhone);
            if (ObjectUtil.isEmpty(userDetails.getUaaId())) {
                //如果账号不存在就注册一个账号
                try {
                    String accessTokenJson = this.getAccessToken(clientId,openId,telPhone);
                    WeiXinAppletUserInfo weiXinAppletUserInfo = this.getAppletUserInfo(accessTokenJson,openId);
                    UaaUserDTO uaaUserDTO = new UaaUserDTO();
                    uaaUserDTO.setLoginName(telPhone);
                    uaaUserDTO.setTelPhone(telPhone);
                    uaaUserDTO.setNickname(weiXinAppletUserInfo.getNickname());
                    uaaUserDTO.setUnionId(weiXinAppletUserInfo.getUnionId());
                    uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
                    userDetails = uaaAuthService.registerUaaUser(uaaUserDTO);
                } catch (Exception e) {
                    logger.error("微信小程序登录失败",e);
                    return super.responseFailAuth("微信小程序登录失败");
                }
            }
            if(!userDetails.isEnabled()){
                return super.responseFailAuth("账号已禁用,请联系管理员");
            }
            userDetails.setUserType(loginDto.getUserType());//添加登录者的身份
            userDetails.setClientId(loginDto.getClientId());//添加登录客户端ID
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            token = JwtTokenUtil.createToken(BeanUtil.beanToMap(userDetails),userDetails.getLoginName());
            redisUtil.set(RedisKeyConstant.TOKEN_CACHE_KEY + userDetails.getUaaId(),token,RedisKeyConstant.TOKEN_CACHE_EXPIRE);
        } catch (AuthenticationException e) {
            logger.error("登录异常:", e);
        }
        return responseSuccessAuth(token);
    }

    private Map<String, Object> appletLogin(WeiXinAppletConfig weiXinAppletConfig, String code) throws Exception {
        String url = String.format(TINY_APP_LOGIN, weiXinAppletConfig.getAppId(), weiXinAppletConfig.getAppSecret(),code);
        return HttpsUtil.httpsMap(url,"POST", null, 6000);
    }

    private String getAccessToken(String clientId, String appId, String appSecret) throws Exception {
        Object accessTokenJson = redisUtil.get(RedisKeyConstant.WX_TOKEN_CACHE_KEY + clientId);
        if (ObjectUtil.isNotEmpty(accessTokenJson)) {
            return accessTokenJson.toString();
        }
        String url = String.format(ACCESS_TOKEN_URL,appId,appSecret);
        Map<String, Object> result = HttpsUtil.httpsMap(url, "POST", null, 6000);
        logger.info("获取token返回数据:" + GsonUtil.toJson(result));
        accessTokenJson = Convert.toStr(result.get("access_token"));
        redisUtil.set(RedisKeyConstant.WX_TOKEN_CACHE_KEY + clientId, accessTokenJson.toString(), RedisKeyConstant.WX_TOKEN_CACHE_EXPIRE);
        return accessTokenJson.toString();
    }

    private WeiXinAppletUserInfo getAppletUserInfo(String accessToken, String openId) throws Exception {
        String url = String.format(GET_USER_INFO_URL,accessToken,openId);
        Map<String, Object> result = HttpsUtil.httpsMap(url, "POST", null, 6000);
        WeiXinAppletUserInfo weiXinAppletUserInfo = new WeiXinAppletUserInfo();
        weiXinAppletUserInfo.setOpenId(openId);
        weiXinAppletUserInfo.setUnionId(Convert.toStr(result.get("unionid")));
        weiXinAppletUserInfo.setNickname(Convert.toStr(result.get("nickname")));
        return weiXinAppletUserInfo;
    }
}
