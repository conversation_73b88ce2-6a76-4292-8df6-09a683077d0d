package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.jhh.dto.OperatorInfoDto;
import com.jmt.model.jhh.vo.OperatorSelectVo;
import com.jmt.model.jhh.vo.OperatorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperatorDao {
    /**
     * 获取运营商信息
     * @param id
     * @return
     */
    OperatorInfo getInfo(Long id);
    /**
     * 获取运营商分页列表
     * @param queryData
     * @return
     */
    List<OperatorVo> getPage(@Param("queryData") OperatorInfoDto queryData);
    /**
     * 添加运营商信息
     * @param operatorInfo
     */
    void add(OperatorInfo operatorInfo);
    /**
     * 修改运营商信息
     * @param operatorInfo
     */
    void update(OperatorInfo operatorInfo);
    /**
     * 删除运营商信息
     * @param id
     */
    void delete(Long id);
    /**
     * 获取运营商下拉列表
     * @param
     * @return
     */
    List<OperatorSelectVo> getOperatorSelect();
    /**
     * 根据运营商编号查找运营商信息
     * @param
     * @return
     */
    OperatorInfo getByOperatorNo(String sourceOperatorNo);
    /**
     * 根据uaaId查找运营商信息
     * @param
     * @return
     */
    OperatorInfo getByUaaId(Long uaaId);
}
