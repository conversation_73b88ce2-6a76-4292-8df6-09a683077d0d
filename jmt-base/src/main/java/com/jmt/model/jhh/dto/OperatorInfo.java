package com.jmt.model.jhh.dto;

import lombok.Data;

import java.util.Date;

@Data
public class OperatorInfo {
    private Long id;

    private Long uaaId;

    private String operatorName;

    private String operatorNo;

    private String headImg;

    private String contractNo;

    private String dutyName;

    private String telPhone;

    private Integer isProfitLimit;

    private Integer limitAmount;

    private String contractPicUrl;

    private String licensePicUrl;

    private Integer auditStatus;

    private String reason;

    private Integer isFirstLogin;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;
}
