package com.jmt.auth.filter;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.jmt.auth.config.IgnoreUrlsConfig;
import com.jmt.constant.JmtConstant;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.util.JwtTokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * JWT登录授权过滤器
 */
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationTokenFilter.class);

    @Resource
    private IgnoreUrlsConfig ignoreUrlsConfig;

    @Override
    protected void doFilterInternal(HttpServletRequest request,HttpServletResponse response,FilterChain chain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        if (!ignoreUrlsConfig.getUrls().contains(uri)) {
            Map<String, Object> tokenMap = JwtTokenUtil.getJwtUser(request);
            String loginName = Convert.toStr(tokenMap.get(JmtConstant.LOGIN_NAME),"");
            logger.info("checking username:{}", loginName);
            if (StrUtil.isNotEmpty(loginName)) {
                if (SecurityContextHolder.getContext().getAuthentication() == null) {
                    LoginUaaUser userDetails = BeanUtil.toBean(tokenMap, LoginUaaUser.class);
                    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    logger.info("authenticated user:{}", loginName);
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        }
        chain.doFilter(request, response);
    }
}
