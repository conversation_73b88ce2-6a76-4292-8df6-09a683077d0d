package com.jmt.dao;

import com.jmt.model.profit.ProfitPointsRecord;

/**
* <AUTHOR>
* @description 针对表【profit_points_record(积分收支记录表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitPointsRecord
*/
public interface ProfitPointsRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitPointsRecord record);

    int insertSelective(ProfitPointsRecord record);

    ProfitPointsRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitPointsRecord record);

    int updateByPrimaryKey(ProfitPointsRecord record);

}
