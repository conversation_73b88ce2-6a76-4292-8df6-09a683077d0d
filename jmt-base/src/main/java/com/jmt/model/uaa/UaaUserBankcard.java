package com.jmt.model.uaa;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 用户账号绑定银行卡表
* @TableName uaa_user_bankcard
*/
@Data
public class UaaUserBankcard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @NotNull(message="[主键ID]不能为空")
    private Long id;
    /**
    * uaaId
    */
    @NotNull(message="[uaaId]不能为空")
    private Long uaaId;
    /**
    * 开户银行
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String bankName;
    /**
    * 卡号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String cardNo;
    /**
    * 开户姓名
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String accountName;
    /**
    * 0-储蓄卡 1-信用卡
    */
    private Integer cardType;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;

    /**
     * 是否默认 是否默认银行卡 0-否 1- 是；通过账号只能一个默认地址
     */
    private Integer isDefault;


}
