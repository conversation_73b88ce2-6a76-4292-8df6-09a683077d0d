package com.jmt.controller;


import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.*;
import com.jmt.service.PointCashService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/profit/pointCash/v1")
public class PointCashController extends BaseController {
    @Resource
    private PointCashService pointCashService;
    /**
     * 积分兑现申请接口
     * @param
     * @return
     */
    @ResponseBody
    @PostMapping("/apply")
    public String apply(@RequestBody PointApplyDto pointApplyDto, HttpServletRequest request){
        LoginUaaUser loginUser = LoginUserUtil.get(request);
        Long applyUser = loginUser.getUaaId();
        pointCashService.apply(pointApplyDto, applyUser);
        return super.responseSuccess("申请成功");
    }
    /**
     * 积分兑现申请分页查询接口
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<PointApplyPageDto> pageQuery) {
        PageResult<PointApply> page = pointCashService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 积分兑现申请审核（通过、不通过）接口
     * @param pointApplyAuditDto
     * @return
     */
    @ResponseBody
    @PostMapping("/audit")
    public String audit(@RequestBody PointApplyAuditDto pointApplyAuditDto, HttpServletRequest request) {
        LoginUaaUser loginUser = LoginUserUtil.get(request);
        Long auditUser = loginUser.getUaaId();
        pointCashService.audit(pointApplyAuditDto,auditUser);
        return super.responseSuccess("审核成功");
    }
}
