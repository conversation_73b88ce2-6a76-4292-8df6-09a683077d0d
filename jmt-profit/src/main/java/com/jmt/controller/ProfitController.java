package com.jmt.controller;


import com.jmt.base.BaseController;
import com.jmt.service.ProfitService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@RequestMapping("/profit/v1")
@RestController
public class ProfitController extends BaseController {

    @Resource
    private ProfitService profitService;

//    @PostMapping("/calculate")
//    public String calculate(@RequestParam String seqNo) {
//        profitService.calculatePreProfit(seqNo);
//        return super.responseSuccess("预分润计算完成");
//    }
    // 查询未分润数据并批量计算
    @PostMapping("/calculate/batch")
    public String calculateBatch() {
        int processedCount = profitService.calculateBatchPreProfit();
        return super.responseSuccess("批量预分润计算完成，处理条数：" + processedCount);
    }
    @PostMapping("/processPredivides")
    public String processPredivides() {
        profitService.processAllPredivides();
        return super.responseSuccess("分润处理完成");
    }
}
