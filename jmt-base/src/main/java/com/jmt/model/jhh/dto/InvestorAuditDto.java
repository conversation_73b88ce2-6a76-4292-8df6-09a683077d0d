package com.jmt.model.jhh.dto;

import lombok.Data;

/**
 * @TableName jhh_investor_info_audit
 */
@Data
public class InvestorAuditDto {

    private Long id;
//    private Long uaaId;
//    // 收益人编号
//    private String investorNo;
//    // 审核状态
//    private Integer auditStatus;
//    // 审核原因
//    private String reason;
//    private Date createTime;
//    //更新时间
//    private Date updateTime;
//    // 是否删除
//    private Integer isDelete;
    private Integer auditStatus;
    private String reason;
}