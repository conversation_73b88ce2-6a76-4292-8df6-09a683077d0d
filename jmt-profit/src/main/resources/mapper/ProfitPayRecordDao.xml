<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitPayRecordDao">

    <sql id="Base_Column_List">
        id, seqNo, orderNo, payUser,payType, payAmount, payStatus, reason, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.profit.entity.ProfitPayRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO profit_pay_record (
        seqNo,orderNo,payUser, payType, payAmount, payStatus, reason, createTime, updateTime, isDelete
        ) VALUES (
        #{seqNo},#{orderNo},#{payUser}, #{payType}, #{payAmount}, #{payStatus}, #{reason}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <update id="updateById">
        UPDATE profit_pay_record
        SET
        pay_status = #{payStatus},
        reason = #{reason},
        update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="selectByCondition" parameterType="com.jmt.model.profit.dto.ProfitPayRecordDTO" resultType="com.jmt.model.profit.dto.ProfitPayRecordDTO">
        SELECT <include refid="Base_Column_List"/>
        FROM profit_pay_record
        <where>
            isDelete = 0
            <if test="seqNo != null and seqNo != ''">AND seqNo = #{seqNo}</if>
            <if test="orderNo != null and orderNo != ''">AND orderNo = #{orderNo}</if>
            <if test="payUser != null">AND payUser = #{payUser}</if>
            <if test="payType != null">AND payType = #{payType}</if>
            <if test="payAmount != null">AND payAmount = #{payAmount,jdbcType=DECIMAL}</if>
            <if test="payStatus != null">AND payStatus = #{payStatus}</if>
            <if test="reason != null and reason != ''">AND reason LIKE CONCAT('%', #{reason}, '%')</if>
        </where>
        ORDER BY createTime
    </select>

    <select id="selectByOrderNo" parameterType="java.lang.String" resultType="com.jmt.model.profit.entity.ProfitPayRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM profit_pay_record
        where  orderNo = #{orderNo} and isDelete = 0
    </select>

</mapper>