package com.jmt.model.profit.dto;

import lombok.Data;

import java.util.Date;
@Data
public class PointsCashApplyAudit {
    /**
     * 主键
     */
    private Long id;

    /**
     * 审核人，uaa_user的主键uaald
     */
    private Long auditUser;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 审核状态0-待审核1-审核通过2-审核不通过3-已兑现
     */
    private String auditStatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
