package com.jmt.model.profit.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BalanceApply {
    /**
     * 主键
     */
    private Long id;

    /**
     * 申请人，uaa_user的主键uaald
     */
    private Long applyUser;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 提现金额
     */
    @NotNull(message = "提现金额不能为空")
    @DecimalMin(value = "0.01", message = "提现金额需大于等于0.01")
    @Digits(integer = 10, fraction = 2, message = "提现金额格式错误，整数部分最多10位，小数部分最多2位")
    private BigDecimal balance;

    /**
     * uaa_user_bankcard的主键ID
     */
    @NotNull(message = "银行卡主键ID不能为空")
    @Min(value = 1, message = "银行卡主键ID需大于0")
    private Long bankId;

    /**
     * 审核状态:0-待审核,1-审核通过,2-审核不通过,3-已提现
     */
    @NotBlank(message = "审核状态不能为空")
    @Pattern(regexp = "^[0123]$", message = "审核状态只能是0、1、2、3")
    private String auditStatus;

    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除:0-否,1-是
     */
    @Pattern(regexp = "^[01]$", message = "删除状态只能是0或1")
    private Integer isDelete;
}
