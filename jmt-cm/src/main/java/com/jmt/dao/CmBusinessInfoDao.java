package com.jmt.dao;

import com.jmt.model.cm.entity.CmBusinessInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmBusinessInfoDao {
    Integer insert(CmBusinessInfo cmBusinessInfo);
    Integer updateById(CmBusinessInfo cmBusinessInfo);
    Integer delete(@Param("id") Long id);
    CmBusinessInfo selectById(@Param("id") Long id);
    CmBusinessInfo selectByUserId(@Param("userId") Long userId);

    List<CmBusinessInfo> selectByCondition(CmBusinessInfo condition);


    CmBusinessInfo selectByBusinessNo(String businessNo);
}
