package com.jmt.dao;

import com.jmt.model.admin.permission.SysPermissionResource;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_permission_resource(系统权限资源关系表)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.permission.SysPermissionResource
*/
public interface SysPermissionResourceDao {

    int deleteByPrimaryKey(Long id);

    int insert(SysPermissionResource record);

    int insertSelective(SysPermissionResource record);

    SysPermissionResource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysPermissionResource record);

    int updateByPrimaryKey(SysPermissionResource record);

    List<Long> findByPermissionIds(@Param("permissionIds") List<Long> permissionIds);

    // 新增：根据权限ID批量删除资源关联
    int batchDeleteByPermissId(@Param("permissId") Long permissId);

    // 新增：批量插入权限资源关联
    int batchInsert(@Param("list") List<SysPermissionResource> list);
}
