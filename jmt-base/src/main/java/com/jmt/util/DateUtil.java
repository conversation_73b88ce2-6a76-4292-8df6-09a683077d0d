package com.jmt.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 日期工具类
 */
public class DateUtil {

    /**
     * 获取某天的开始时间（00:00:00）
     */
    public static Date getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取某天的结束时间（23:59:59）
     */
    public static Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取本周的开始时间（周一 00:00:00）
     */
    public static Date getWeekStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 设置为周一
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return getDayStart(calendar.getTime());
    }

    /**
     * 获取本周的结束时间（周日 23:59:59）
     */
    public static Date getWeekEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 设置为周日
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        // 日期加7天，避免当前日期就是周日导致计算错误
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        return getDayEnd(calendar.getTime());
    }

    /**
     * 获取本月的开始时间（1号 00:00:00）
     */
    public static Date getMonthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return getDayStart(calendar.getTime());
    }

    /**
     * 获取本月的结束时间（最后一天 23:59:59）
     */
    public static Date getMonthEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 设置为下个月第一天
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 日期减1天，得到当前月的最后一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return getDayEnd(calendar.getTime());
    }
}