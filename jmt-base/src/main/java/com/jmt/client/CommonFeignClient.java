package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.auth.SmsCodeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name="jmt-common",path ="/common",configuration = FeignConfig.class)
public interface CommonFeignClient {

    @PostMapping("/oss/upload")
    String upload(@RequestParam("file") MultipartFile multipartFile);

    @PostMapping("/sms/send")
    String sendSms(@RequestBody SmsCodeDto smsCodeDto);
}