package com.jmt.model.jhh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class InvestorInfoDto {
    //收益人id
    private Long id;
    //收益人名称
    private String investorName;
    //收益人编号
    private String investorNo;
    //头像
    private String headImg;
    //合约编号
    private String contractNo;
    //职位名称
    private String dutyName;
    //手机号码
    private String telPhone;
    //是否限制收益
    private Integer isProfitLimit;
    //限额
    private Integer limitAmount;
    //合约图片
    private String contractPicUrl;
    //证件图片
    private String licensePicUrl;
    //审核状态
    private Integer auditStatus;
    //创建时间
    private String reason;
    //是否首次登录
    private Integer isFirstLogin;
    //删除状态
    private Integer isDelete;

}
