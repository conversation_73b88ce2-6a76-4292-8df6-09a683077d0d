package com.jmt.service.role;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.SysRoleDao;
import com.jmt.dao.SysRolePermissionDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.role.SysRole;
import com.jmt.model.admin.role.SysRolePermission;
import com.jmt.model.admin.role.SysRolePermissionDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysRoleService extends BaseService {

    @Resource
    private SysRoleDao sysRoleDao;
    @Resource
    private SysRolePermissionDao sysRolePermissionDao;

    /**
     * 更新角色信息（含通用字段维护）
     */
    public Integer updateRole(SysRole sysRole) {
        setCommonRoleFields(sysRole, false);
        return sysRoleDao.updateByPrimaryKeySelective(sysRole);
    }

    /**
     * 角色下拉框数据（仅返回id和name）
     */
    public List<Map<String, Object>> getRoleSelectOptions() {
        List<SysRole> roles = sysRoleDao.selectAllAvailable();
        return roles.stream()
                .map(role -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("id", role.getId());
                    option.put("name", role.getRoleName());
                    return option;
                })
                .collect(Collectors.toList());
    }

    /**
     * 逻辑删除角色（更新isDelete=1）
     */
    public Integer deleteRole(SysRole sysRole) {
        setCommonRoleFields(sysRole, true);
        return sysRoleDao.updateByPrimaryKeySelective(sysRole);
    }

    /**
     * 添加角色（自动生成创建/更新时间）
     */
    public Long addRole(SysRole sysRole) {
        setCommonRoleFields(sysRole, false);
        sysRole.setId(null); // 确保ID自增
        sysRoleDao.insertSelective(sysRole);

        return sysRole.getId();
    }

    /**
     * 新增角色-权限关联关系
     */
    public Integer addRolePermission(SysRolePermissionDto dto) {
        SysRolePermission rolePermission = convertToRolePermission(dto);
        setCommonRolePermissionFields(rolePermission);
        return sysRolePermissionDao.insertSelective(rolePermission);
    }

    /**
     * 逻辑删除角色-权限关联（更新isDelete=1）
     */
    public Integer deleteRolePermission(SysRolePermissionDto dto) {
        SysRolePermission rolePermission = convertToRolePermission(dto);
        setCommonRolePermissionFieldsForDeletion(rolePermission);
        return sysRolePermissionDao.updateByPrimaryKeySelective(rolePermission);
    }

    /**
     * 更新角色-权限关联关系
     */
    public Integer updateRolePermission(SysRolePermissionDto dto) {
        SysRolePermission rolePermission = convertToRolePermission(dto);
        setCommonRolePermissionFields(rolePermission);
        return sysRolePermissionDao.updateByPrimaryKeySelective(rolePermission);
    }

    /**
     * 设置角色通用字段（创建/更新时间、删除状态）
     * @param isDelete 是否为删除操作
     */
    private void setCommonRoleFields(SysRole sysRole, boolean isDelete) {
        Date now = new Date();
        sysRole.setUpdateTime(now);
        sysRole.setIsDelete(isDelete ? 1 : 0);
        if (!isDelete) { // 新增/更新操作补充创建时间
            sysRole.setCreateTime(now);
        }
    }

    /**
     * DTO转换为角色-权限实体
     */
    private SysRolePermission convertToRolePermission(SysRolePermissionDto dto) {
        SysRolePermission rolePermission = new SysRolePermission();
        rolePermission.setId(dto.getId());
        rolePermission.setRoleId(dto.getRoleId());
        rolePermission.setPermissId(dto.getPermissId());
        return rolePermission;
    }

    /**
     * 设置角色-权限关联通用字段（新增/更新）
     */
    private void setCommonRolePermissionFields(SysRolePermission rolePermission) {
        Date now = new Date();
        rolePermission.setCreateTime(now);
        rolePermission.setUpdateTime(now);
        rolePermission.setIsDelete(0);
    }

    /**
     * 设置角色-权限关联删除字段
     */
    private void setCommonRolePermissionFieldsForDeletion(SysRolePermission rolePermission) {
        rolePermission.setUpdateTime(new Date());
        rolePermission.setIsDelete(1);
    }

    /**
     * 角色分页查询
     */
    public PageResult<SysRole> getPage(PageQuery<SysRole> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysRole query = pageQuery.getQueryData();
        List<SysRole> list = sysRoleDao.getPage(query);
        return new PageResult<>(list);
    }

    /**
     * 查询角色详情（校验角色有效性）
     */
    public SysRole getRoleInfo(Long roleId) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        SysRole role = sysRoleDao.selectByPrimaryKey(roleId);
        if (role == null || role.getIsDelete() == 1) {
            throw new RuntimeException("角色不存在或已被删除");
        }
        return role;
    }

    /**
     * 角色启用/禁用（0-禁用，1-启用）
     */
    public Integer enableRole(Long roleId, Integer status) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new IllegalArgumentException("状态值必须为0（禁用）或1（启用）");
        }
        SysRole role = new SysRole();
        role.setId(roleId);
        role.setIsDelete(status == 1 ? 0 : 1); // 启用→isDelete=0；禁用→isDelete=1
        setCommonRoleFields(role, status == 0);
        return sysRoleDao.updateByPrimaryKeySelective(role);
    }

    /**
     * 批量逻辑删除角色（事务保证）
     */
    @Transactional
    public Integer batchDeleteRoles(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            throw new IllegalArgumentException("角色ID列表不能为空");
        }
        int count = 0;
        Date now = new Date();
        for (Long roleId : roleIds) {
            SysRole role = new SysRole();
            role.setId(roleId);
            role.setIsDelete(1);
            role.setUpdateTime(now);
            count += sysRoleDao.updateByPrimaryKeySelective(role);
        }
        return count;
    }

    /**
     * 角色分配权限（先清除旧关联，再添加新关联）
     */
    @Transactional
    public void assignPermissions(Long roleId, List<Long> permissionIds) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        // 清除旧权限关联（逻辑删除）
        sysRolePermissionDao.updateIsDeleteByRoleId(roleId, 1);

        // 批量添加新权限关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            List<SysRolePermission> rolePermissions = permissionIds.stream()
                    .map(permissId -> {
                        SysRolePermission rp = new SysRolePermission();
                        rp.setRoleId(roleId);
                        rp.setPermissId(permissId);
                        setCommonRolePermissionFields(rp);
                        return rp;
                    })
                    .collect(Collectors.toList());
            sysRolePermissionDao.batchInsert(rolePermissions);
        }
    }
}