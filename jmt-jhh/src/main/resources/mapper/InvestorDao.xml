<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.InvestorDao">
    <resultMap id="InvestorSelectResultMap" type="com.jmt.model.jhh.vo.InvestorVo">
        <id column="id" property="id" jdbcType="BIGINT"/> <!-- 数据库字段id → 实体类id -->
        <result column="investorName" property="investorName" jdbcType="VARCHAR"/> <!-- 数据库operator_no → 实体类operatorNo -->
        <result column="investorNo" property="investorNo" jdbcType="VARCHAR"/> <!-- 数据库operator_name → 实体类operatorName -->
    </resultMap>
    <insert id="add">
        INSERT INTO jhh_investor_info(uaaId,investorName,investorNo,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete,headImg,contractPicUrl,licensePicUrl)
        VALUES
            (#{uaaId},#{investorName},#{investorNo},#{contractNo},#{dutyName},#{telPhone},#{isProfitLimit},#{limitAmount},#{auditStatus},#{reason},#{isFirstLogin},#{createTime},#{updateTime},#{isDelete},#{headImg},#{contractPicUrl},#{licensePicUrl})
    </insert>
    <update id="delete">
        UPDATE jhh_investor_info
        SET isDelete = 1
        WHERE id = #{id}
    </update>
    <select id="getPage" resultType="com.jmt.model.jhh.vo.InvestorVo">
        SELECT uaaId,investorName,investorNo,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_investor_info
        <where>
            <if test="queryData != null and queryData.investorNo!=null">
                AND investorNo LIKE CONCAT('%',#{queryData.investorNo},'%')
            </if>
            <if test="queryData != null and queryData.investorName!=null">
                AND investorName LIKE CONCAT('%',#{queryData.investorName},'%')
            </if>
            <if test="queryData != null and queryData.dutyName!=null">
                AND dutyName LIKE CONCAT('%',#{queryData.dutyName},'%')
            </if>
            <if test="queryData != null and queryData.telPhone!=null">
                AND telPhone LIKE CONCAT('%',#{queryData.telPhone},'%')
            </if>
            <if test="queryData != null and queryData.uaaId!=null">
                AND uaaId = #{queryData.uaaId}
            </if>
            <if test="queryData != null and queryData.auditStatus!=null">
                AND auditStatus = #{queryData.auditStatus}
            </if>
            <if test="true">
                AND isDelete = 0
            </if>
        </where>
    </select>

    <select id="getInfo" resultType="com.jmt.model.jhh.dto.InvestorInfo">
        SELECT uaaId,investorName,investorNo,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_investor_info
        WHERE id = #{id}
        AND isDelete = 0
    </select>

    <select id="getByInvestorNo" resultType="com.jmt.model.jhh.dto.InvestorInfo">
        SELECT uaaId,investorName,investorNo,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_investor_info
        WHERE investorNo = #{investorNo}
        AND isDelete = 0
    </select>
    <select id="getInvestorSelect" resultMap="InvestorSelectResultMap">
        SELECT id,investorNo,investorName
        FROM jhh_investor_info
        WHERE isDelete = 0
        AND auditStatus = 1
        ORDER BY investorName ASC
    </select>
    <select id="getByUaaId" resultType="com.jmt.model.jhh.dto.InvestorInfo">
        SELECT uaaId,investorName,investorNo,headImg,contractNo,dutyName,telPhone,isProfitLimit,limitAmount,contractPicUrl,licensePicUrl,auditStatus,reason,isFirstLogin,createTime,updateTime,isDelete
        FROM jhh_investor_info
        WHERE uaaId = #{uaaId}
        AND isDelete = 0
    </select>
    <update id="update">
        UPDATE jhh_investor_info
        <set>
            <if test="uaaId!=null">
                uaaId = #{uaaId},
            </if>
            <if test="investorName!=null">
                investorName = #{investorName},
            </if>
            <if test="investorNo!=null">
                investorNo = #{investorNo},
            </if>
            <if test="contractNo!=null">
                contractNo = #{contractNo},
            </if>
            <if test="dutyName!=null">
                dutyName = #{dutyName},
            </if>
            <if test="telPhone!=null">
                telPhone = #{telPhone},
            </if>
            <if test="isProfitLimit!=null">
                isProfitLimit = #{isProfitLimit},
            </if>
            <if test="limitAmount!=null">
                limitAmount = #{limitAmount},
            </if>
            <if test="contractPicUrl!=null">
                contractPicUrl = #{contractPicUrl},
            </if>
            <if test="licensePicUrl!=null">
                licensePicUrl = #{licensePicUrl},
            </if>
            <if test="auditStatus!=null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason!=null">
                reason = #{reason},
            </if>
            <if test="isFirstLogin!=null">
                isFirstLogin = #{isFirstLogin},
            </if>
            <if test="headImg!=null">
                headImg = #{headImg},
            </if>
            <if test="updateTime!=null">
                updateTime = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
        AND isDelete = 0
    </update>
</mapper>