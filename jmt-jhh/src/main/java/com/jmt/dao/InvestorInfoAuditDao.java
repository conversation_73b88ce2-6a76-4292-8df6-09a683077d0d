package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.jhh.dto.InvestorAuditDto;
import com.jmt.model.jhh.dto.InvestorInfoAudit;
import com.jmt.model.jhh.vo.InvestorInfoAuditVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvestorInfoAuditDao {
    /**
     * 添加相关的审核信息
     * @param investorInfoAudit
     */
    void add(InvestorInfoAudit investorInfoAudit);
    /**
     * 删除相关的审核信息
     * @param investorNo
     */
    void delete(String investorNo);
    /**
     * 查询审核信息
     * @param queryData
     */
    List<InvestorInfoAuditVo> getPage(@Param("queryData") InvestorAuditDto queryData);
    /**
     * 修改的审核信息
     * @param investorInfoAudit
     */
    void update(InvestorInfoAudit investorInfoAudit);
    /**
     * 查询相关的审核信息
     * @param id
     */
    InvestorInfoAudit getInfo(Long id);
    // List<DishFlavor> getbyDishId(Long dishId);
    /**
     * 删除相关的审核信息
     * @param id
     */
    void deleteById(Long id);
    /**
     * 根据收益人编号查询相关的审核信息
     * @param investorNo
     */
    List<InvestorInfoAudit> getInfoByInvestorNo(String investorNo);
    /**
     * 删除相关的审核信息通过Long
     * @param id
     */
    void deleteByIdWithLong(Long id);
    /**
     * 批量添加
     * @param investorInfoAudits
     */
    void addBatch(List<InvestorInfoAudit> investorInfoAudits);
}
