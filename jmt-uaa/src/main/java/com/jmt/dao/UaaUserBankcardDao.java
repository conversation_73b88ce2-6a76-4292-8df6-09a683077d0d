package com.jmt.dao;

import com.jmt.model.uaa.UaaUserBankcard;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UaaUserBankcardDao {
    Integer insert(UaaUserBankcard uaaUserBankcard);

    Integer updateById(UaaUserBankcard uaaUserBankcard);

    Integer deleteById(@Param("id") Long id);

    UaaUserBankcard selectById(@Param("id") Long id);

    List<UaaUserBankcard> selectByUaaId(@Param("uaaId") Long uaaId);

    UaaUserBankcard findDefaultByUaaId(Long uaaId);

    Integer countByUaaId(Long uaaId);

}
