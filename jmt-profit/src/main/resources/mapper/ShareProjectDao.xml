<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.ShareProjectDao">
	<select id="getShareProjectSelect" resultType="com.jmt.model.profit.vo.ShareProjectVo">
		SELECT id,parentId,projectName,orderNoPrefix
		FROM profit_share_project
		WHERE isDelete = 0
	</select>
	<select id="getInfo" resultType="com.jmt.model.profit.dto.ShareProject">
		SELECT id,parentId,projectName,orderNoPrefix,useStauts,createTime,updateTime,isDelete
		FROM profit_share_project
		WHERE id = #{projectId}
		AND isDelete = 0
	</select>
	<select id="getInfoByParentId" resultType="com.jmt.model.profit.dto.ShareProject">
		SELECT id,parentId,projectName,orderNoPrefix,useStauts,createTime,updateTime,isDelete
		FROM profit_share_project
		WHERE parentId = #{projectId}
		AND isDelete = 0
	</select>
</mapper>