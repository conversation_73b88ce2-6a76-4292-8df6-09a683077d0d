package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.cm.dto.CmWorkEqApplyDTO;
import com.jmt.model.cm.entity.CmWorkEqApply;
import com.jmt.model.cm.entity.CmWorkEqApplyDetail;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmWorkEqApplyVO;
import com.jmt.service.CmWorkEqApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cm/workEqApply")
public class CmWorkEqApplyController extends BaseController {
    @Resource
    private CmWorkEqApplyService cmWorkEqApplyService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<CmWorkEqApply> pageQuery){
        PageResult<CmWorkEqApply> pageResult = cmWorkEqApplyService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 新增
     * @param dto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmWorkEqApplyDTO dto) {
        try {
            cmWorkEqApplyService.create(dto);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增工单失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmWorkEqApplyDTO dto) {
        try {
            cmWorkEqApplyService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmWorkEqApplyService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmWorkEqApplyVO cmWorkEqApply = cmWorkEqApplyService.getById(id);
        if(cmWorkEqApply == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmWorkEqApply,"查询成功");
    }

    /**
     * 根据id查询工单详情
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/detail/{id}")
    public String getDetailById(@PathVariable Long id) {
        try {
            List<CmWorkEqApplyDetail> cmWorkEqApplyDetails = cmWorkEqApplyService.getDetailById(id);
            return super.responseSuccess(cmWorkEqApplyDetails,"查询成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 审核申请工单
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                @RequestParam Integer auditStatus,
                                @RequestParam(required = false) String reason) {
        try {
            cmWorkEqApplyService.auditBusiness(id, auditStatus, reason);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

}
