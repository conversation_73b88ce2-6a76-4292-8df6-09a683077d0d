package com.jmt.dao;

import com.jmt.model.cm.entity.CmWorkEqApply;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface CmWorkEqApplyDao {
    Integer insert(CmWorkEqApply record);
    Integer update(CmWorkEqApply record);

    CmWorkEqApply selectById(@Param("id") Long id);
    List<CmWorkEqApply> selectAll();
    List<CmWorkEqApply> selectByWorkStatus(@Param("workStatus") Integer workStatus);

    Integer updateWorkStatus(Long id, Integer workStatus, String reason);
    Integer delete(Long id);

    List<CmWorkEqApply> selectByCondition(CmWorkEqApply cmWorkEqApply);
}
