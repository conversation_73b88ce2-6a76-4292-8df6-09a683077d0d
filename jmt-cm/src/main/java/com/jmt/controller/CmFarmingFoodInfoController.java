package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmFarmingFoodInfoDTO;
import com.jmt.model.cm.entity.CmFarmingFoodOrder;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmFarmingFoodInfoVO;
import com.jmt.service.CmFarmingFoodInfoService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/farmingFoodInfo")
public class CmFarmingFoodInfoController extends BaseController {
    @Resource
    private CmFarmingFoodInfoService cmFarmingFoodInfoService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<CmFarmingFoodInfoDTO> pageQuery){
        PageResult<CmFarmingFoodInfoDTO> pageResult = cmFarmingFoodInfoService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 新增
     * @param cmFarmingFoodInfoDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmFarmingFoodInfoDTO cmFarmingFoodInfoDTO) {
        try {
            cmFarmingFoodInfoService.create(cmFarmingFoodInfoDTO);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增工单失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmFarmingFoodInfoDTO dto) {
        try {
            cmFarmingFoodInfoService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmFarmingFoodInfoService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmFarmingFoodInfoVO cmFarmingFoodInfoVO = cmFarmingFoodInfoService.getById(id);
        if(cmFarmingFoodInfoVO == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmFarmingFoodInfoVO,"查询成功");
    }


    /**
     * 审核申请工单
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                @RequestParam Integer auditStatus, @RequestParam String reason, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        try {
            cmFarmingFoodInfoService.auditBusiness(id, auditStatus,reason, loginUser.getUaaId());
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 生成订单
     * @param cmFarmingFoodOrder
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/addOrder")
    public String addOrder(@RequestBody CmFarmingFoodOrder cmFarmingFoodOrder) {
        try {
            cmFarmingFoodInfoService.createOrder(cmFarmingFoodOrder);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/cancelOrder/{id}")
    public String cancelOrder(@PathVariable Long id) {
        try {
            cmFarmingFoodInfoService.cancelOrder(id);
            return super.responseSuccess("取消成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/auditOrder/{id}")
    public String auditOrder(@PathVariable Long id,
                             @RequestParam Integer auditStatus, @RequestParam String reason) {
        try {
            cmFarmingFoodInfoService.auditOrder(id, auditStatus,reason);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

}
