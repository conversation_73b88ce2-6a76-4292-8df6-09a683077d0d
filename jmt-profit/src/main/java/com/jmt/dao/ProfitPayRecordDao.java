package com.jmt.dao;

import com.jmt.model.profit.dto.ProfitPayRecordDTO;
import com.jmt.model.profit.entity.ProfitPayRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProfitPayRecordDao {

    Integer insert(ProfitPayRecord record);

    Integer updateById(ProfitPayRecord record);

    List<ProfitPayRecordDTO> selectByCondition(ProfitPayRecordDTO demoDto);
    ProfitPayRecord selectByOrderNo(String orderNo);
}
