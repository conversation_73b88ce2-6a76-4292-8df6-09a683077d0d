package com.jmt.dao;

import com.jmt.model.profit.dto.ShareConfig;
import com.jmt.model.profit.dto.ShareConfigDto;
import com.jmt.model.profit.dto.ShareConfigPageDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareConfigDao {
    /**
     * 分页查询
     * @param queryData
     * @return
     */
    List<ShareConfig> getPage(@Param("queryData") ShareConfigPageDto queryData);
    /**
     * 新增配置
     * @param shareConfig
     */
    void add(ShareConfig shareConfig);
    /**
     * 删除配置
     * @param id
     */
    void delete(Long id);
    /**
     * 获取配置信息
     * @param operatorNo
     * @return
     */
    List<ShareConfigDto> getInfo(String operatorNo);
    /**
     * 删除配置
     * @param operatorNo
     */
    void deleteByNo(String operatorNo);
}
