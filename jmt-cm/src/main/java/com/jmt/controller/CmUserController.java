package com.jmt.controller;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseController;
import com.jmt.client.UaaFeignClient;
import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.service.CmUserService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/user")
public class CmUserController extends BaseController {
    @Resource
    private CmUserService cmUserService;

    @Resource
    private UaaFeignClient uaaFeignClient;

    /**
     * 新增
     * @param userDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody UaaUserDTO userDTO ) {
        try {
            String response = uaaFeignClient.registerUser(userDTO);
            Gson gson = new Gson();
            JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();

            String code = jsonObject.get("code").getAsString();
            if (!"200".equals(code)) {
                String errorMsg = jsonObject.get("msg").getAsString();
                return super.responseFail("UAA服务错误: " + errorMsg);
            }

            JsonElement dataElement = jsonObject.get("data");
            if (dataElement == null || dataElement.isJsonNull()) {
                return super.responseFail("UAA服务返回数据为空");
            }

            UaaUser user = gson.fromJson(dataElement, UaaUser.class);
            if (user == null || user.getUaaId() == null) {
                return super.responseFail("用户注册失败: 无效的用户数据");
            }

            CmUserDTO cmUserDTO = new CmUserDTO();
            cmUserDTO.setUaaId(user.getUaaId());
            cmUserDTO.setUserName(user.getLoginName());
            cmUserDTO.setTelPhone(userDTO.getTelPhone());
            cmUserDTO.setRefereeNo(userDTO.getRefereeNo());

            CmUser cmUser= cmUserService.createUser(cmUserDTO);
            return super.responseSuccess(cmUser, "用户新增成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 修改
     * @param cmUser
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmUser cmUser,HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        cmUser.setUaaId(loginUser.getUaaId());
        cmUserService.updateByUaaId(cmUser);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmUserService.deleteUser(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmUser cmUser = cmUserService.getUserById(id);
        if(cmUser == null){
            return super.responseFail("不存在该用户");
        }
        return super.responseSuccess(cmUser,"查询成功");
    }

    /**
     * 根据uaaId查询
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/uaa")
    public String getByUaaId( HttpServletRequest request) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(request);
            CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
            if (cmUser == null) {
                return super.responseFail("用户不存在");
            }
            return super.responseSuccess(cmUser, "查询成功");
        } catch (Exception e) {
            return super.responseFail("查询失败: " + e.getMessage());
        }
    }

}
