package com.jmt.model.profit.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.Date;

@Data
public class BalanceCashApplyAudit {
    /**
     * 主键
     */
    private Long id;

    /**
     * 审核人，uaa_user的主键uaald
     */
    @NotNull(message = "审核人ID不能为空")
    @Min(value = 1, message = "审核人ID需大于0")
    private Long auditUser;

    /**
     * 申请编号
     */
    @NotBlank(message = "申请编号不能为空")
    @Size(max = 255, message = "申请编号长度不能超过255个字符")
    private String applyNo;

    /**
     * 审核状态0-待审核1-审核通过2-审核不通过3-已提现
     */
    @NotBlank(message = "审核状态不能为空")
    @Pattern(regexp = "^[0123]$", message = "审核状态只能是0、1、2、3")
    private String auditStatus;

    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String reason;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date createTime;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    @NotNull(message = "删除状态不能为空")
    @Pattern(regexp = "^[01]$", message = "删除状态只能是0或1")
    private Integer isDelete;
}
