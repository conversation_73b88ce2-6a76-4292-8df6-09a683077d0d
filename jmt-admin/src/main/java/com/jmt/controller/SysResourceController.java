package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.resource.SysResource;
import com.jmt.service.resource.SysResourceService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/resource/v1")

public class SysResourceController extends BaseController {


    @Resource
    private SysResourceService resourceService;


    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<SysResource> pageQuery, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            PageResult<SysResource> page = resourceService.getPage(pageQuery);
            return super.responseSuccess(page, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询资源信息时出现异常", e);
            return super.responseFail("分页查询资源信息时出现异常");
        }
    }

    /**
     * 添加资源
     *
     * @param sysResource 资源实体
     * @return 操作结果信息
     */
    @PostMapping("/addResource")
    public String addResource(@RequestBody SysResource sysResource) {
        try {
            Long id = resourceService.addResource(sysResource);
            return super.responseSuccess(id,"增加成功");
        } catch (Exception e) {
            logger.error("添加资源时出现异常，资源信息: {}", sysResource, e);
            return super.responseFail("增加资源失败");
        }
    }

    /**
     * 更新资源
     *
     * @param sysResource 资源实体
     * @return 操作结果信息
     */
    @PostMapping("/updateResource")
    public String updateResource(@RequestBody SysResource sysResource) {
        try {
            resourceService.updateResource(sysResource);
            return super.responseSuccess("更新成功");
        } catch (Exception e) {
            logger.error("更新资源时出现异常，资源信息: {}", sysResource, e);
            return super.responseFail("更新资源失败");
        }
    }

    /**
     * 删除资源
     *
     * @param sysResource 资源实体
     * @return 操作结果信息
     */
    @PostMapping("/deleteResource")
    public String deleteResource(@RequestBody SysResource sysResource) {
        try {
            resourceService.deleteResource(sysResource);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            logger.error("删除资源时出现异常，资源信息: {}", sysResource, e);
            return super.responseFail("删除资源失败");
        }
    }
    /**
     * 资源查看接口
     * @param resourceId 资源ID
     * @return 资源详情
     */
    @GetMapping("/getResourceInfo")
    public String getResourceInfo(@RequestParam Long resourceId) {
        try {
            SysResource resource = resourceService.getResourceInfo(resourceId);
            return super.responseSuccess(resource, "查询成功");
        } catch (Exception e) {
            logger.error("查询资源详情失败，资源ID: {}", resourceId, e);
            return super.responseFail("查询资源详情失败");
        }
    }
    /**
     * 资源下拉框数据接口
     * @return 资源下拉框数据列表
     */
    @GetMapping("/getSelectOptions")
    public String getSelectOptions() {
        try {
            List<Map<String, Object>> options = resourceService.getResourceSelectOptions();
            return super.responseSuccess(options, "查询成功");
        } catch (Exception e) {
            logger.error("获取资源下拉框数据失败", e);
            return super.responseFail("获取资源下拉框数据失败");
        }
    }
}