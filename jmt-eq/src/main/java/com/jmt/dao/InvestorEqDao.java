package com.jmt.dao;

import com.github.pagehelper.Page;

import com.jmt.model.eq.dto.EqBusinessSetupDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;

import java.util.List;


public interface InvestorEqDao {
    /**
     * 获取分页数据
     * @param queryData 查询条件
     * @return
     */
    /**
     * 按id获取详情
     * @param id
     * @return
     */
    EqOperatorStockVo getInfo(Integer id);
    /**
     * 解除商家对设备的控制
     *
     */
    void clearInvestorEq(String operatorNo);
}
