<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.InvestorInfoAuditDao">
    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO jhh_investor_info_audit(id,auditUser,investorNo,auditStatus,reason,createTime,updateTime,isDelete)
        VALUES
            (#{id},#{auditUser},#{investorNo},#{auditStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
    </insert>
    <insert id="addBatch">
        INSERT INTO jhh_investor_info_audit(auditUser,investorNo,auditStatus,reason,createTime,updateTime,isDelete)
        VALUES
        <foreach collection="investorInfoAudits" item="item" separator=",">
            (#{item.auditUser},#{item.investorNo},#{item.auditStatus},#{item.reason},#{item.createTime},#{item.updateTime},#{item.isDelete})
        </foreach>
    </insert>
    <update id="update">
        UPDATE jhh_investor_info_audit
        <set>
            <if test="auditUser!=null">
                auditUser = #{auditUser},
            </if>
            <if test="investorNo!=null">
                investorNo = #{investorNo},
            </if>
            <if test="auditStatus!=null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason!=null">
                reason = #{reason},
            </if>
            <if test="updateTime!=null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete!=null">
                isDelete = #{isDelete},
            </if>
        </set>
        WHERE id = #{id}
        AND isDelete = 0
    </update>

    <update id="deleteById">
        UPDATE jhh_investor_info
        SET isDelete = 1
        WHERE
            id = #{id}
        AND isDelete = 0
    </update>

    <update id="deleteByIdWithLong">
        UPDATE jhh_investor_info_audit
        SET isDelete = 1
        WHERE id = #{id}
        AND isDelete = 0
    </update>
    <update id="delete">
        UPDATE jhh_investor_info_audit
        SET isDelete = 1
        WHERE investorNo = #{investorNo}
        AND isDelete = 0
    </update>

    <select id="getPage" resultType="com.jmt.model.jhh.vo.InvestorInfoAuditVo">
        SELECT
        auditUser,
        investorNo,
        auditStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM jhh_investor_info_audit
        <where>
            <if test="queryData != null and queryData.investorNo!=null">
                and investorNo = #{queryData.investorNo}
            </if>
            <if test="queryData != null and queryData.auditUser!=null">
                and auditUser = #{queryData.auditUser}
            </if>
            <if test="queryData != null and queryData.auditStatus!=null">
                and auditStatus = #{queryData.auditStatus}
            </if>
            <if test="queryData != null and queryData.reason!=null">
                and reason LIKE CONCAT('%',#{queryData.reason},'%')
            </if>
            <if test="queryData != null and queryData.createTime!=null">
                and createTime >= #{queryData.createTime}
            </if>
            <if test="queryData != null and queryData.updateTime!=null">
                and updateTime >= #{queryData.updateTime}
            </if>
            <if test="queryData != null and queryData.isDelete!=null">
                and isDelete = #{queryData.isDelete}
            </if>
            <if test="true">
                AND isDelete = 0
            </if>
        </where>
        ORDER BY createTime DESC
    </select>
    <select id="getInfo" resultType="com.jmt.model.jhh.dto.InvestorInfoAudit">
        SELECT id,auditUser,investorNo,auditStatus,reason,createTime,updateTime,isDelete
        FROM jhh_investor_info_audit WHERE id = #{id}
        AND isDelete = 0
    </select>
    <select id="getInfoByInvestorNo" resultType="com.jmt.model.jhh.dto.InvestorInfoAudit">
        SELECT id,auditUser,investorNo,auditStatus,reason,createTime,updateTime,isDelete
        FROM jhh_investor_info_audit WHERE investorNo = #{investorNo}
        AND isDelete = 0
    </select>
</mapper>