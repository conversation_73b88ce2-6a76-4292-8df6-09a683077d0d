package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.ZgCategory;
import com.jmt.service.CategoryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 业态分类管理控制器
 * 提供业态分类的增删改查以及树形结构查询等功能
 */
@RestController
@RequestMapping("/zg/category/v1")
public class CategoryController extends BaseController {

    @Resource
    private CategoryService categoryService;

    /**
     * 添加业态分类
     * @param zgCategory 业态分类信息，包含分类名称、父级ID等基础信息
     * @return 操作结果
     */
    @PostMapping("/addCategory")
    public String addCategory(@RequestBody ZgCategory zgCategory) {
        categoryService.addCategory(zgCategory);
        return super.responseSuccess("增加成功");
    }

    /**
     * 删除业态分类
     * @param zgCategory 业态分类信息，必须包含要删除的分类ID
     * @return 操作结果
     */
    @PostMapping("/deleteCategory")
    public String deleteCategory(@RequestBody ZgCategory zgCategory) {
        categoryService.deleteCategory(zgCategory);
        return super.responseSuccess("删除成功");
    }

    /**
     * 更新业态分类信息
     * @param zgCategory 业态分类信息，包含要更新的字段和分类ID
     * @return 操作结果
     */
    @PostMapping("/updateCategory")
    public String updateCategory(@RequestBody ZgCategory zgCategory) {
        categoryService.updateCategory(zgCategory);
        return super.responseSuccess("更新成功");
    }

    /**
     * 分页查询业态分类信息
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 业态分类分页数据
     */
    @GetMapping("/page")
    public String getPage(@RequestBody PageQuery<ZgCategory> pageQuery) {
        PageResult<ZgCategory> page = categoryService.getCategoryPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 根据ID获取业态分类详情
     * @param id 业态分类ID
     * @return 业态分类详细信息
     */
    @GetMapping("/categoryInfo")
    public String getCategoryInfo(@RequestParam Long id) {
        ZgCategory category = categoryService.getCategory(id);
        return super.responseSuccess(category, "查询成功");
    }
    /**
     * 供应商业态树形下拉框数据接口
     * 返回带层级结构的业态分类，用于下拉框树形展示
     * @return 业态树形分类详细信息
     */
    @GetMapping("/categoryTreeSelect")
    public String getCategoryTreeSelect() {
            // 调用service获取树形结构（默认不包含已删除数据）
        List<Map<String, Object>> treeData = categoryService.buildCategoryTreeForSelect();
        // 若需要简化下拉框数据结构（仅保留id、名称、子节点），可在此处转换
            return super.responseSuccess(treeData, "树形下拉框数据查询成功");

    }
}