package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmSelloutInfoConvertor;
import com.jmt.dao.*;
import com.jmt.model.cm.dto.CmSelloutInfoDTO;
import com.jmt.model.cm.entity.CmSelloutAudit;
import com.jmt.model.cm.entity.CmSelloutInfo;
import com.jmt.model.cm.entity.CmSelloutOrder;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmSelloutInfoVO;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmSelloutInfoService extends BaseService {
    @Resource
    private CmSelloutInfoDao cmSelloutInfoDao;

    @Resource
    private CmSelloutAuditDao cmSelloutAuditDao;

    @Resource
    private CmSelloutOrderDao cmSelloutOrderDao;

    @Resource
    private CmSelloutInfoConvertor cmSelloutInfoConvertor;

    @Transactional
    public Long create(CmSelloutInfoDTO dto) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoConvertor.toEntity(dto);

        cmSelloutInfo.setAuditStatus(0);
        cmSelloutInfo.setCreateTime(new Date());
        cmSelloutInfo.setUpdateTime(new Date());
        cmSelloutInfo.setIsDelete(0);
        Long result = cmSelloutInfoDao.insert(cmSelloutInfo);
        Long generatedId = cmSelloutInfo.getId();

        CmSelloutAudit audit = new CmSelloutAudit();
        audit.setInfoId(generatedId);
        audit.setAuditStatus(0);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setIsDelete(0);
        cmSelloutAuditDao.insert(audit);
        return result;
    }

    @Transactional
    public int update(CmSelloutInfoDTO dto) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoDao.selectById(dto.getId());
        if (cmSelloutInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        if (cmSelloutInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmSelloutInfo cmWorkEq = cmSelloutInfoConvertor.toEntity(dto);
        cmWorkEq.setUpdateTime(new Date());
        return cmSelloutInfoDao.update(cmWorkEq);
    }


    @Transactional
    public int delete(Long id) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoDao.selectById(id);
        if (cmSelloutInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmSelloutInfoDao.delete(id);
    }

    @Transactional
    public CmSelloutInfoVO getById(Long id) {
        return cmSelloutInfoConvertor.toVo(cmSelloutInfoDao.selectById(id));
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmSelloutInfoDTO> getPage(PageQuery<CmSelloutInfoDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        CmSelloutInfoDTO cmSelloutInfoDTO = pageQuery.getQueryData();
        List<CmSelloutInfoDTO> list = cmSelloutInfoDao.selectByCondition(cmSelloutInfoDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason, Long auditUserId) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoDao.selectById(id);
        if (cmSelloutInfo == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmSelloutInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单审核状态
        CmSelloutInfo update = new CmSelloutInfo();
        update.setId(id);
        update.setAuditStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());
        int updateCount = cmSelloutInfoDao.update(update);

        // 查询审核记录
        CmSelloutAudit existingAudit = cmSelloutAuditDao.selectByInfoId(id);

        // 更新审核表
        CmSelloutAudit audit = new CmSelloutAudit();
        audit.setInfoId(id);
        audit.setAuditStatus(auditStatus);
        audit.setReason(reason);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setAuditUser(auditUserId);
        audit.setIsDelete(0);
        audit.setId(existingAudit.getId());
        cmSelloutAuditDao.update(audit);

        return updateCount;
    }

    /**
     * 创建订单
     * @param order
     */
    @Transactional
    public void createOrder(CmSelloutOrder order) {
        order.setOrderStatus(0);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = WorkNoGeneratorUtil.generate("SNO");
        order.setOrderNo(orderNo);
        cmSelloutOrderDao.insert(order);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional
    public void cancelOrder(Long id) {
        CmSelloutOrder order = cmSelloutOrderDao.selectById(id);
        if (order == null || order.getIsDelete() == 1) {
            throw new RuntimeException("订单不存在或已被删除");
        }
        order.setOrderStatus(3); //取消支付
        order.setUpdateTime(new Date());
        cmSelloutOrderDao.update(order);
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditOrder(Long id, Integer auditStatus, String reason) {
        CmSelloutOrder order = cmSelloutOrderDao.selectById(id);
        if (order == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单支付状态
        CmSelloutOrder update = new CmSelloutOrder();
        update.setId(id);
        update.setOrderStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmSelloutOrderDao.update(update);
    }
}
