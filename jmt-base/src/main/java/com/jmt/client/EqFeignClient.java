package com.jmt.client;
import com.jmt.config.FeignConfig;
import com.jmt.model.eq.dto.RequestSendEq;
import com.jmt.model.eq.dto.RequestTrans;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-eq",path ="/eq/v1",configuration = FeignConfig.class)
public interface EqFeignClient {

    @PostMapping(value = "/transfer")
    String transfer(@RequestBody RequestTrans requestTrans);

    @PostMapping(value = "/assign")
    String assign(@RequestBody RequestSendEq requestSendEq);

    @PostMapping(value = "/getInfoByBusNo")
    String getInfoByBusNo(@RequestParam("busNo") String busNo);
}
