package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.ZgTicketInfo;
import com.jmt.service.TicketService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 囤货券信息管理控制器
 * 提供囤货券的查询、上架、下架、审核以及订单管理等功能
 */
@RestController
@RequestMapping("/zg/ticket")
public class TicketController extends BaseController {

    @Resource
    private TicketService ticketService;

    /**
     * 分页查询囤货券信息
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 囤货券分页数据
     */
    @GetMapping("/page")
    public String getTicketInfoPage(@RequestBody PageQuery<ZgTicketInfo> pageQuery) {
        return super.responseSuccess(ticketService.getTicketInfoPage(pageQuery),"查询成功");
    }

    /**
     * 上架囤货券
     * @param ticketInfo 囤货券信息，包含上架所需的券基本信息
     * @return 操作结果
     */
    @PostMapping("/putOnSale")
    public String putTicketOnSale(@RequestBody ZgTicketInfo ticketInfo) {
        ticketService.putTicketOnSale(ticketInfo);
        return super.responseSuccess("上架成功");
    }

    /**
     * 根据ID获取囤货券详情
     * @param ticketNo 囤货券编号
     * @return 囤货券详细信息
     */
    @GetMapping("/{ticketNo}")
    public String getTicketInfo(@PathVariable String ticketNo) {
        return super.responseSuccess(ticketService.getTicketInfo(ticketNo),"");
    }

    /**
     * 审核囤货券
     * @param id 囤货券ID
     * @param auditStatus 审核状态（0-待审核，1-审核通过，2-审核拒绝）
     * @param reason 审核意见
     * @return 操作结果
     */
    @PostMapping("/audit")
    public String auditTicket(@RequestParam Long id, @RequestParam Integer auditStatus, @RequestParam String reason) {
        ticketService.auditTicket(id, auditStatus, reason);
        return super.responseSuccess("审核操作成功");
    }

    /**
     * 下架囤货券
     * @param id 囤货券ID
     * @return 操作结果
     */
    @PostMapping("/takeOffSale")
    public String takeTicketOffSale(@RequestParam Long id) {
        ticketService.takeTicketOffSale(id);
        return super.responseSuccess("下架成功");
    }

    /**
     * 取消囤货券订单
     * @param orderId 订单ID
     * @return 操作结果（成功/失败）
     */
    @PostMapping("/cancelTicketOrder")
    public String cancelTicketOrder(@RequestParam Long orderId) {
        boolean result = ticketService.cancelTicketOrder(orderId);
        if (result) {
            return super.responseSuccess("订单取消成功");
        }
        return super.responseSuccess("订单取消失败");
    }

    /**
     * 获取囤货券订单详情
     * @param orderId 订单ID
     * @return 订单详细信息
     */
    @GetMapping("/ticketOrderInfo")
    public String getTicketOrderInfo(@RequestParam Long orderId) {
        return super.responseSuccess(ticketService.getTicketOrderInfo(orderId),"查询成功");
    }

    /**
     * 囤货券订单退款
     * @param orderId 订单ID
     * @return 操作结果（成功/失败）
     */
    @PostMapping("/refundTicketOrder")
    public String refundTicketOrder(@RequestParam Long orderId) {
        boolean result = ticketService.refundTicketOrder(orderId);
        if (result) {
            return super.responseSuccess("订单退款成功");
        }
        return super.responseSuccess("订单退款失败");
    }

    /**
     * 获取囤货券下拉框数据（用于选择框）
     * @return 囤货券简要信息列表
     */
    @GetMapping("/selectBoxData")
    public List<ZgTicketInfo> getTicketSelectBoxData() {
        return ticketService.getTicketSelectBoxData();
    }
}