<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitShareConfigMapper">

    <sql id="Base_Column_List">
        id,projectId,projectName,operatorNo,shareType,operatorRatio,
        investorRatio,platformRatio,businessRatio,operatorAmount,investorAmount,
        platformAmount,businessAmount,useStauts,createTime,updateTime,
        isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitShareConfig">
        select
        <include refid="Base_Column_List" />
        from profit_share_config
        where  parentId = #{id}
    </select>

    <select id="selectActiveByProjectId" resultType="com.jmt.model.profit.ProfitShareConfig">
        SELECT * FROM profit_share_config
        WHERE projectId = #{projectId}
        AND useStauts = 0        <!-- 仅查启用的配置 -->
        ORDER BY updateTime DESC  <!-- 取最新配置 -->
        LIMIT 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_share_config
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitShareConfig" useGeneratedKeys="true">
        insert into profit_share_config
        ( id,projectId,projectName,operatorNo,shareType,operatorRatio,
          investorRatio,platformRatio,businessRatio,operatorAmount,investorAmount,
          platformAmount,businessAmount,useStauts,createTime,updateTime,
          isDelete)
        values (#{id},#{projectId},#{projectName},#{operatorNo},#{shareType},#{operatorRatio},
                #{investorRatio},#{platformRatio},#{businessRatio},#{operatorAmount},#{investorAmount},
                #{platformAmount},#{businessAmount},#{useStauts},#{createTime},#{updateTime},
                #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitShareConfig" useGeneratedKeys="true">
        insert into profit_share_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectId != null">projectId,</if>
            <if test="projectName != null">projectName,</if>
            <if test="operatorNo != null">operatorNo,</if>
            <if test="shareType != null">shareType,</if>
            <if test="operatorRatio != null">operatorRatio,</if>
            <if test="investorRatio != null">investorRatio,</if>
            <if test="platformRatio != null">platformRatio,</if>
            <if test="businessRatio != null">businessRatio,</if>
            <if test="operatorAmount != null">operatorAmount,</if>
            <if test="investorAmount != null">investorAmount,</if>
            <if test="platformAmount != null">platformAmount,</if>
            <if test="businessAmount != null">businessAmount,</if>
            <if test="useStauts != null">useStauts,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="operatorNo != null">#{operatorNo},</if>
            <if test="shareType != null">#{shareType},</if>
            <if test="operatorRatio != null">#{operatorRatio},</if>
            <if test="investorRatio != null">#{investorRatio},</if>
            <if test="platformRatio != null">#{platformRatio},</if>
            <if test="businessRatio != null">#{businessRatio},</if>
            <if test="operatorAmount != null">#{operatorAmount},</if>
            <if test="investorAmount != null">#{investorAmount},</if>
            <if test="platformAmount != null">#{platformAmount},</if>
            <if test="businessAmount != null">#{businessAmount},</if>
            <if test="useStauts != null">#{useStauts},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitShareConfig">
        update profit_share_config
        <set>
            <if test="projectId != null">
                projectId = #{projectId},
            </if>
            <if test="projectName != null">
                projectName = #{projectName},
            </if>
            <if test="operatorNo != null">
                operatorNo = #{operatorNo},
            </if>
            <if test="shareType != null">
                shareType = #{shareType},
            </if>
            <if test="operatorRatio != null">
                operatorRatio = #{operatorRatio},
            </if>
            <if test="investorRatio != null">
                investorRatio = #{investorRatio},
            </if>
            <if test="platformRatio != null">
                platformRatio = #{platformRatio},
            </if>
            <if test="businessRatio != null">
                businessRatio = #{businessRatio},
            </if>
            <if test="operatorAmount != null">
                operatorAmount = #{operatorAmount},
            </if>
            <if test="investorAmount != null">
                investorAmount = #{investorAmount},
            </if>
            <if test="platformAmount != null">
                platformAmount = #{platformAmount},
            </if>
            <if test="businessAmount != null">
                businessAmount = #{businessAmount},
            </if>
            <if test="useStauts != null">
                useStauts = #{useStauts},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitShareConfig">
        update profit_share_config
        set
            projectId =  #{projectId},
            projectName =  #{projectName},
            operatorNo =  #{operatorNo},
            shareType =  #{shareType},
            operatorRatio =  #{operatorRatio},
            investorRatio =  #{investorRatio},
            platformRatio =  #{platformRatio},
            businessRatio =  #{businessRatio},
            operatorAmount =  #{operatorAmount},
            investorAmount =  #{investorAmount},
            platformAmount =  #{platformAmount},
            businessAmount =  #{businessAmount},
            useStauts =  #{useStauts},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>