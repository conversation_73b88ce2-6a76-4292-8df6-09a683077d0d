package com.jmt.service.resource;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.SysResourceDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.resource.SysResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SysResourceService extends BaseService {

    @Resource
    private SysResourceDao sysResourceDao;

    /**
     * 新增资源（自动填充创建/更新时间，默认未删除）
     */
    public Long addResource(SysResource sysResource) {
        setCommonResourceFields(sysResource, false);
        sysResourceDao.insert(sysResource);
        return sysResource.getId();
    }

    /**
     * 更新资源（自动填充更新时间）
     */
    public Integer updateResource(SysResource sysResource) {
        setCommonResourceFields(sysResource, false);
        return sysResourceDao.updateByPrimaryKeySelective(sysResource);
    }

    /**
     * 逻辑删除资源（标记isDelete=1，填充更新时间）
     */
    public Integer deleteResource(SysResource sysResource) {
        setCommonResourceFields(sysResource, true);
        return sysResourceDao.updateByPrimaryKeySelective(sysResource);
    }

    /**
     * 填充资源通用字段（时间戳、删除状态）
     * @param isDelete true=删除操作（仅更新时间+标记删除），false=新增/更新（填充创建+更新时间）
     */
    private void setCommonResourceFields(SysResource sysResource, boolean isDelete) {
        Date now = new Date();
        sysResource.setUpdateTime(now);
        sysResource.setIsDelete(isDelete ? 1 : 0);
        if (!isDelete) {
            sysResource.setCreateTime(now);
        }
    }

    /**
     * 资源分页查询
     */
    public PageResult<SysResource> getPage(PageQuery<SysResource> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysResource query = pageQuery.getQueryData();
        List<SysResource> list = sysResourceDao.getPage(query);
        return new PageResult<>(list);
    }

    /**
     * 查询资源详情（校验有效性：存在且未删除）
     */
    public SysResource getResourceInfo(Long resourceId) {
        if (resourceId == null) {
            throw new IllegalArgumentException("资源ID不能为空");
        }
        SysResource resource = sysResourceDao.selectByPrimaryKey(resourceId);
        if (resource == null || resource.getIsDelete() == 1) {
            throw new RuntimeException("资源不存在或已被删除");
        }
        return resource;
    }

    /**
     * 资源下拉框数据（仅返回有效资源的ID和名称）
     */
    public List<Map<String, Object>> getResourceSelectOptions() {
        List<SysResource> resources = sysResourceDao.selectAllAvailable();
        return resources.stream()
                .map(resource -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("id", resource.getId());
                    option.put("name", resource.getResName());
                    return option;
                })
                .collect(Collectors.toList());
    }
}