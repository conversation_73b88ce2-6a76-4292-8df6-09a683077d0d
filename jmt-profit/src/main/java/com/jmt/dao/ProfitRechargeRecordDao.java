package com.jmt.dao;

import com.jmt.model.profit.dto.ProfitPayRecordDTO;
import com.jmt.model.profit.dto.ProfitRechargeRecordDTO;
import com.jmt.model.profit.entity.ProfitRechargeRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProfitRechargeRecordDao {
    Integer insert(ProfitRechargeRecord record);

    List<ProfitRechargeRecordDTO> selectByCondition(ProfitRechargeRecordDTO demoDto);
}
