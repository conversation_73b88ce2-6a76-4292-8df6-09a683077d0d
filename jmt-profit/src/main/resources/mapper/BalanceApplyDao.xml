<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.BalanceApplyDao">
	<insert id="add">
		INSERT INTO profit_balance_cash_apply
		    (applyUser,applyNo,balance,bankId,auditStatus,reason,createTime,updateTime,isDelete)
		VALUES
		    (#{applyUser},#{applyNo},#{balance},#{bankId},#{auditStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
	</insert>
	<update id="update">
		UPDATE profit_balance_cash_apply
		<set>
			<if test="applyUser != null">
				applyUser = #{applyUser},
			</if>
			<if test="applyNo != null">
				 applyNo = #{applyNo},
			</if>
			<if test="balance != null">
				balance = #{balance},
			</if>
			<if test="bankId != null">
				 bankId = #{bankId},
			</if>
			<if test="auditStatus != null">
				 auditStatus = #{auditStatus},
			</if>
			<if test="reason != null">
				reason = #{reason},
			</if>
			<if test="updateTime != null">
				updateTime = #{updateTime},
			</if>
		</set>
		WHERE id = #{id}
		AND isDelete = 0
	</update>
	<select id="getCountByUaaIdAndCreateTime" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM profit_balance_cash_apply
		WHERE applyUser = #{applyUser}
		  AND createTime BETWEEN #{startDay} AND #{endDay}
		  AND isDelete = 0
	</select>
	<select id="getPage" resultType="com.jmt.model.profit.dto.BalanceApply">
		SELECT
		    id,
		    applyUser,
		    applyNo,
		    balance,
		    bankId,
		    auditStatus,
		    reason,
		    createTime,
		    updateTime,
		    isDelete
		FROM profit_balance_cash_apply
		<where>
			<if test="queryData != null and queryData.applyUser != null">
				applyUser = #{queryData.applyUser}
			</if>
			<if test="queryData != null and queryData.auditStatus != null">
				auditStatus = #{queryData.auditStatus}
			</if>
			<if test="true">
				AND isDelete = 0
			</if>
		</where>
	</select>
	<select id="getInfo" resultType="com.jmt.model.profit.dto.BalanceApply">
		SELECT
		    id,
		    applyUser,
		    applyNo,
		    balance,
		    bankId,
		    auditStatus,
		    reason,
		    createTime,
		    updateTime,
		    isDelete
		FROM profit_balance_cash_apply
		WHERE id = #{id}
		  AND isDelete = 0
	</select>

</mapper>