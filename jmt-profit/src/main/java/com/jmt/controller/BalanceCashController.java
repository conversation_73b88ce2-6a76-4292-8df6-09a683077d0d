package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.vo.BalanceRechargeOrderVo;
import com.jmt.service.BalanceCashService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

@RestController
@RequestMapping("/profit/balanceCash/v1")
public class BalanceCashController extends BaseController {
   @Resource
   private BalanceCashService balanceCashService;
   /**
    * 余额提现申请接口
    * @param
    * @param request
    * @return
    */
   @ResponseBody
   @PostMapping("/apply")
   public String apply(@RequestBody BalanceApplyDto balanceApplyDto, HttpServletRequest request){
      LoginUaaUser loginUser = LoginUserUtil.get(request);
      Long applyUser = loginUser.getUaaId();
      balanceCashService.apply(balanceApplyDto, applyUser);
      return super.responseSuccess("申请成功");
   }
   /**
    * 余额提现申请分页查询接口
    * @param pageQuery
    * @return
    */
   @ResponseBody
   @GetMapping("/getPage")
   public String getPage(@RequestBody PageQuery<BalanceApplyDto> pageQuery) {
      PageResult<BalanceApply> page = balanceCashService.getPage(pageQuery);
      return super.responseSuccess(page,"查询成功");
   }
   /**
    * 余额提现申请审核（通过、不通过）接口
    * @param balanceApplyAuditDto
    * @return
    */
   @ResponseBody
   @PostMapping("/audit")
   public String audit(@RequestBody BalanceApplyAuditDto balanceApplyAuditDto, HttpServletRequest request) {
      LoginUaaUser loginUser = LoginUserUtil.get(request);
      Long auditUser = loginUser.getUaaId();
      balanceCashService.audit(balanceApplyAuditDto,auditUser);
      return super.responseSuccess("申请成功");
   }
   /**
    * 余额充值订单生成接口
    * @return
    */
   @ResponseBody
   @PostMapping("/createOrder")
   public String createOrder(@RequestBody CreateOrderDto createOrderDto, HttpServletRequest request) {
      LoginUaaUser loginUser = LoginUserUtil.get(request);
      Long rechargeUser = loginUser.getUaaId();
      balanceCashService.createOrder(createOrderDto,rechargeUser);
      return super.responseSuccess("创建成功");
   }
   /**
    * 获取余额充值订单列表
    * @param pageQuery
    * @return
    */
   @ResponseBody
   @GetMapping(value = "/getBalancePage")
   public String getBalancePage(@RequestBody PageQuery<BalanceRechargeOrderDto> pageQuery) {
      PageResult<BalanceRechargeOrderVo> page = balanceCashService.getBalancePage(pageQuery);
      return super.responseSuccess(page,"查询成功");
   }
   /**
    * 异步调用支付结果处理
    * @param
    * @return
    */
   @ResponseBody
   @PostMapping(value = "/handlePayCallback")
    public String handlePayCallback(@RequestParam String orderNo,@RequestParam Long rechargeUser) {
      balanceCashService.handlePayCallback(orderNo,rechargeUser);
      return super.responseSuccess("处理成功");
   }
}
