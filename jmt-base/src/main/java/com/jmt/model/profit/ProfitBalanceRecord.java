package com.jmt.model.profit;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 余额收支记录表
 * @TableName profit_balance_record
 */
@Data
public class ProfitBalanceRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 余额归属人，uaa_user的主键uaaId
     */
    private Long uaaId;

    /**
     * 变动余额
     */
    private BigDecimal balance;

    /**
     * 记录类型
     0-收入
     1-支出
     */
    private Integer recordType;

    /**
     * 余额来源
     recordType=0时填写
     0-充值
     1-积分兑现
     */
    private Integer balanceSource;

    /**
     * 填写充值订单号或积分兑现申请单号
     */
    private String sourceId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProfitBalanceRecord other = (ProfitBalanceRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getUaaId() == null ? other.getUaaId() == null : this.getUaaId().equals(other.getUaaId()))
                && (this.getBalance() == null ? other.getBalance() == null : this.getBalance().equals(other.getBalance()))
                && (this.getRecordType() == null ? other.getRecordType() == null : this.getRecordType().equals(other.getRecordType()))
                && (this.getBalanceSource() == null ? other.getBalanceSource() == null : this.getBalanceSource().equals(other.getBalanceSource()))
                && (this.getSourceId() == null ? other.getSourceId() == null : this.getSourceId().equals(other.getSourceId()))
                && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUaaId() == null) ? 0 : getUaaId().hashCode());
        result = prime * result + ((getBalance() == null) ? 0 : getBalance().hashCode());
        result = prime * result + ((getRecordType() == null) ? 0 : getRecordType().hashCode());
        result = prime * result + ((getBalanceSource() == null) ? 0 : getBalanceSource().hashCode());
        result = prime * result + ((getSourceId() == null) ? 0 : getSourceId().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", uaaId=").append(uaaId);
        sb.append(", balance=").append(balance);
        sb.append(", recordType=").append(recordType);
        sb.append(", balanceSource=").append(balanceSource);
        sb.append(", sourceId=").append(sourceId);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}