package com.jmt.dao;

import com.jmt.model.profit.dto.PointApply;
import com.jmt.model.profit.dto.PointApplyDto;
import com.jmt.model.profit.dto.PointApplyPageDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PointApplyDao {
    /**
     * 根据用户id和申请时间获取申请数量
     * @param applyUser
     * @param startDay
     * @param endDay
     * @return
     */
    int getCountByUaaIdAndCreateTime(@Param("applyUser") Long applyUser, @Param("startDay")Date startDay, @Param("endDay") Date endDay);
    /**
     * 添加申请
     * @param pointApply
     */
    void add(PointApply pointApply);
    /**
     * 获取申请列表
     * @param queryData
     * @return
     */
    List<PointApply> getPage(@Param("queryData") PointApplyPageDto queryData);
    /**
     * 获取申请详情
     * @param id
     */
    PointApply getInfo(Long id);
    /**
     * 修改申请
     * @param pointApply
     */
    void update(PointApply pointApply);
}
