package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;

import com.jmt.dao.*;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.BusinessException;
import com.jmt.model.profit.dto.*;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.DateUtil;
import com.jmt.util.ProfitUserNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class PointCashService extends BaseService {
    //最低提现积分
    private static final int MIN_POINT = 10;
    //最高提现积分
    private static final int MAX_POINT = 5000000;
    //每日兑现积分上限
    private static final int MAX_DAY_COUNT = 3;
    //每周兑现次数上限
    private static final int MAX_WEEK_COUNT = 10;
    //每月兑现次数上限
    private static final int MAX_MONTH_COUNT = 20;
    //积分余额兑换比例
    private static final Double POINT_RATIO = 0.01;
    //税率
    private static final Double TAX_RATIO = 0.05;
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private PointApplyDao pointApplyDao;
    @Resource
    private PointsRecordDao pointsRecordDao;
    @Resource
    private PointApplyAuditDao pointApplyAuditDao;
    @Resource
    private BalanceRecordDao balanceRecordDao;
    // 用户锁缓存（每个用户一个锁对象）
    private final ConcurrentHashMap<Long, Object> userLocks = new ConcurrentHashMap<>();
    // 锁缓存（按申请单ID加锁，防止并发审核）
    private final ConcurrentHashMap<Long, Object> applyLocks = new ConcurrentHashMap<>();
    /**
     * 申请兑现
     *
     * @param pointApplyDto
     * @param applyUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void apply(PointApplyDto pointApplyDto, Long applyUser) {
        // 获取用户专属锁对象（如果不存在则创建）
        Object userLock = userLocks.computeIfAbsent(applyUser, k -> new Object());
        // 1. 校验用户存在
        PointApply pointApply = new PointApply();
        BeanUtils.copyProperties(pointApplyDto, pointApply);
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        // 2. 获取积分配置，校验积分是否满足兑现条件
        Integer points = pointApplyDto.getPoints();
        validatePointsCashCondition(userWallet,points);
        // 3. 校验兑现频次限制（每天/每周/每月）
        Date now = new Date();
        Date startDay = DateUtil.getDayStart(now);
        Date endDay = DateUtil.getDayEnd(now);
        Date startWeek = DateUtil.getWeekStart(now);
        Date endWeek = DateUtil.getWeekEnd(now);
        Date startMonth = DateUtil.getMonthStart(now);
        Date endMonth = DateUtil.getMonthEnd(now);
        int countDay = pointApplyDao.getCountByUaaIdAndCreateTime(applyUser, startDay, endDay);
        int countWeek = pointApplyDao.getCountByUaaIdAndCreateTime(applyUser, startWeek, endWeek);
        int countMonth = pointApplyDao.getCountByUaaIdAndCreateTime(applyUser, startMonth, endMonth);
        if (countDay >= MAX_DAY_COUNT){
            throw new RuntimeException("今日已超过最大提现次数限制");
        }
        if (countWeek >= MAX_WEEK_COUNT){
            throw new RuntimeException("本周已超过最大提现次数限制");
        }
        if (countMonth >= MAX_MONTH_COUNT){
            throw new RuntimeException("本月已超过最大提现次数限制");
        }
        synchronized (userLock){
            try {
                // 4. 扣减用户积分
                Integer sourPoints = getPoints(applyUser);
                uaaFeignClient.decreasePoints(applyUser, points);
                Integer targetPoints = getPoints(applyUser);
                if (!validatePointCashCondition(points,sourPoints,targetPoints)){
                    throw new BusinessException("积分扣除异常");
                }
            }catch (Exception e) {
                // 标记申请为结算失败
                pointApply.setAuditStatus(AuditStatusEnum.PENDING.getDescription()); // 处理失败
                pointApplyDao.update(pointApply);
                throw e;
            }
            // 5.根据是否上传发票决定是否需要审核
            try {
                if (pointApplyDto.getInvoiceProvide() == 1) {
                    pointApply.setAuditStatus(AuditStatusEnum.PENDING.getDescription());// 待审核
                }else{
                    pointApply.setAuditStatus(AuditStatusEnum.APPROVED.getDescription());// 无需审核
                    // 积分转余额基准比例
                    BigDecimal baseRate = new BigDecimal(POINT_RATIO);
                    // 计算原始金额
                    BigDecimal originalAmount = new BigDecimal(points).multiply(baseRate);
                    //计算可兑现余额(扣税)
                    BigDecimal taxRate = new BigDecimal(TAX_RATIO); // 税率
                    BigDecimal taxAmount = originalAmount.multiply(taxRate);  // 税费 = 原始金额 * 税率
                    BigDecimal finalAmount = originalAmount.subtract(taxAmount);  // 最终可兑现金额 = 原始金额 - 扣税
                    try {
                        BigDecimal sourceBalance = getBalance(applyUser);
                        userWallet.setBalance(userWallet.getBalance().add(finalAmount));
                        BigDecimal targetBalance = getBalance(applyUser);
                        if (!validateBalanceCashCondition(finalAmount,sourceBalance,targetBalance)){
                            throw new BusinessException("提现异常");
                        }
                    }catch (Exception e){
                        // 余额增加失败
                        throw new BusinessException("余额增加失败");
                    }
                    // 记录余额变动
                    recordBalanceChange(applyUser, originalAmount, pointApply.getApplyNo(), pointApplyDto.getReason(), now);
                }
                // 7.记录积分变动
                Integer recordType = 1;
                recordPointsChange(applyUser, pointApply, now, recordType);
                // 6.创建积分兑现申请表
                pointApply.setApplyUser(applyUser);
                pointApply.setCreateTime(now);
                pointApply.setUpdateTime(now);
                pointApply.setIsDelete(0);
                String applyNo = ProfitUserNoUtil.generate("PA");
                pointApply.setApplyNo(applyNo);
                pointApplyDao.add(pointApply);

            } catch (Exception e) {
                try {
                    // 出现异常，回滚预扣积分
                    uaaFeignClient.increasePoints(applyUser, points);
                    throw e;
                }catch (Exception e1){
                    // 回滚操作本身失败，记录严重错误
                    log.error("审核回滚失败，申请单ID：{}，错误：{}", pointApply.getId(), e.getMessage(), e);
                }

            }

        }
    }
    /**
     * 获取积分申请列表
     *
     * @param pageQuery
     * @return
     */
    public PageResult<PointApply> getPage(PageQuery<PointApplyPageDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<PointApply> page = pointApplyDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 审核
     */
    @Transactional(rollbackFor = Exception.class)
    public void audit(PointApplyAuditDto pointApplyAuditDto, Long auditUser) {
        Long id = pointApplyAuditDto.getId();
        Date now = new Date();
        String auditStatus = pointApplyAuditDto.getAuditStatus();
        String reason = pointApplyAuditDto.getReason();
        //查询积分兑现申请
        PointApply pointApply = pointApplyDao.getInfo(id);
        // 获取申请单专属锁，防止并发审核
        Object applyLock = applyLocks.computeIfAbsent(pointApply.getId(), k -> new Object());
        Long applyUser = pointApply.getApplyUser();
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        // 保存原始状态（用于回滚）
        String originalAuditStatus = pointApply.getAuditStatus();
        PointApply originalApply = new PointApply();
        BeanUtils.copyProperties(pointApply, originalApply); // 深拷贝原始状态
        if (pointApply == null){
            throw new BusinessException("申请不存在");
        }
        //校验申请状态
        if (!pointApply.getAuditStatus().equals(AuditStatusEnum.PENDING.getDescription())){
            throw new BusinessException("申请已处理");
        }
        //校验审核状态
        if (!(auditStatus.equals(AuditStatusEnum.APPROVED.getDescription())||!auditStatus.equals(AuditStatusEnum.REJECTED.getDescription()))){
            throw new BusinessException("审核状态有误");
        }
        //更新申请表申请状态
        pointApply.setUpdateTime(now);
        pointApply.setReason(reason);
        pointApply.setAuditStatus(auditStatus);
        pointApplyDao.update(pointApply);
        //更新审核表申请状态
        PointsCashApplyAudit applyAudit = new PointsCashApplyAudit();
        applyAudit.setAuditUser(auditUser);
        applyAudit.setApplyNo(pointApply.getApplyNo());
        applyAudit.setReason(reason);
        applyAudit.setIsDelete(0);
        applyAudit.setAuditStatus(auditStatus);
        applyAudit.setUpdateTime(now);
        List<PointsCashApplyAudit> applyAudits = pointApplyAuditDao.getInfoByApplyNo(pointApply.getApplyNo());
        if (applyAudits != null && applyAudits.size() > 0) {
            log.info("有多个相同编号的审核信息");
            for (PointsCashApplyAudit applyAudit1 : applyAudits){
                if (applyAudit1.getAuditUser().equals(auditUser)){
                    log.info("有i相同uuid的记录");
                    applyAudit.setId(applyAudit1.getId());
                    pointApplyAuditDao.update(applyAudit);
                }else {
                    log.info("无i相同uuid的记录");
                    applyAudit.setCreateTime(now);
                    pointApplyAuditDao.add(applyAudit);
                }
            }
        }else{
            log.info("首次审核");
            applyAudit.setCreateTime(now);
            pointApplyAuditDao.add(applyAudit);
        }
        synchronized (applyLock){
            try {
                //计算兑现金额(审核通过)
                if (pointApplyAuditDto.getAuditStatus().equals(AuditStatusEnum.APPROVED.getDescription())){
                    // 积分转余额基准比例
                    BigDecimal baseRate = new BigDecimal(POINT_RATIO);
                    // 计算原始金额
                    BigDecimal originalAmount = new BigDecimal(pointApply.getPoints()).multiply(baseRate);
                    BigDecimal sourBalance = userWallet.getBalance();
                    uaaFeignClient.increaseBalance(applyUser, originalAmount);
                    BigDecimal targetBalance = userWallet.getBalance();
                    if (!targetBalance.subtract(sourBalance).equals(originalAmount)){
                        //退还积分
                        Integer sourPoints = getPoints(applyUser);
                        uaaFeignClient.increasePoints(pointApply.getApplyUser(), pointApply.getPoints());
                        Integer targetPoints = getPoints(applyUser);
                        if (!validatePointCashCondition(pointApply.getPoints(),sourPoints,targetPoints)){
                            throw new BusinessException("积分扣除异常");
                        }
                        Integer recordType1 = 0;
                        //记录积分变动
                        recordPointsChange(applyUser, pointApply, now,recordType1);
                        throw new BusinessException("积分转余额失败");
                    }
                    //记录余额变动
                    recordBalanceChange(applyUser, originalAmount, pointApply.getApplyNo(),reason,now);
                }else{
                    Integer recordType = 0;
                    //退还积分
                    uaaFeignClient.increasePoints(pointApply.getApplyUser(), pointApply.getPoints());
                    //记录积分变动
                    recordPointsChange(applyUser, pointApply, now,recordType);
                }
            }catch (Exception e){
                rollbackAudit(originalApply, originalAuditStatus, now, e.getMessage(), auditUser);
                throw new BusinessException("审核处理失败：" + e.getMessage());
            }
        }
    }
    /**
     * 记录积分变动
     */
    private void recordPointsChange(Long applyUser, PointApply pointApply,Date now, Integer recordType) {
        PointsRecord pointsRecord = new PointsRecord();
        pointsRecord.setUaaId(applyUser);
        pointsRecord.setPoints(pointApply.getPoints());
        pointsRecord.setRecordType(recordType);//支出
        pointsRecord.setRemark(pointApply.getReason());
        pointsRecord.setCreateTime(now);
        pointsRecord.setUpdateTime(now);
        pointsRecordDao.insert(pointsRecord);
    }
    /**
     * 校验积分兑现条件
     */
    private void validatePointsCashCondition(UaaUserWallet userWallet, Integer points) {
        // 校验最低兑现积分
        if (points <= MIN_POINT){
            throw new BusinessException("积分低于最低提现限制");
        }
        // 校验用户积分是否足够
        if (userWallet.getPoints() < points) {
            throw new BusinessException("用户积分不足");
        }
        // 校验最高兑现积分
        if (points > MAX_POINT) {
            throw new BusinessException("积分高于最高提现限制");
        }
    }
    // 回滚机制：恢复申请单和审核表状态
    private void rollbackAudit(PointApply originalApply, String originalStatus, Date now, String errorMsg,Long auditUser) {
        try {
            // 回滚申请表状态为原始状态（待审核）
            originalApply.setAuditStatus(originalStatus);
            originalApply.setReason("审核失败：" + errorMsg);
            originalApply.setUpdateTime(now);
            pointApplyDao.update(originalApply);

            // 回滚审核表：新增一条失败记录
            PointsCashApplyAudit rollbackAudit = new PointsCashApplyAudit();
            rollbackAudit.setApplyNo(originalApply.getApplyNo());
            rollbackAudit.setAuditUser(auditUser); // 系统标识
            rollbackAudit.setAuditStatus(AuditStatusEnum.REJECTED.getDescription()); // 审核失败
            rollbackAudit.setReason("审核回滚：" + errorMsg);
            rollbackAudit.setCreateTime(now);
            rollbackAudit.setUpdateTime(now);
            rollbackAudit.setIsDelete(0);
            pointApplyAuditDao.add(rollbackAudit);

        } catch (Exception e) {
            // 回滚操作本身失败时，记录严重错误日志（人工介入）
            log.error("审核回滚失败，申请单ID：{}，错误：{}", originalApply.getId(), e.getMessage(), e);
        }
    }
    //记录余额变动(只能收入)
    private void recordBalanceChange(Long applyUser,BigDecimal Amount,String ApplyNo,String reason,Date now) {
        BalanceRecord balanceRecord = new BalanceRecord();
        balanceRecord.setUaaId(applyUser);
        balanceRecord.setBalance(Amount);
        balanceRecord.setRecordType(0);
        balanceRecord.setBalanceSource(1);
        balanceRecord.setSourceId(ApplyNo);
        balanceRecord.setRemark(reason);
        balanceRecord.setCreateTime(now);
        balanceRecord.setUpdateTime(now);
        balanceRecord.setIsDelete(0);
        balanceRecordDao.add(balanceRecord);
    }
    //反序列化
    private UaaUserWallet getWallet(String wallet) {
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(wallet).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        return gson.fromJson(dataElement, UaaUserWallet.class);
    }
    //验证流程
    private Boolean validateBalanceCashCondition(BigDecimal balance, BigDecimal sourceBalance,BigDecimal targetBalance) {
        if (sourceBalance.compareTo(targetBalance)>0){
            return sourceBalance.subtract(targetBalance).compareTo(balance) == 0;
        }else {
            return targetBalance.subtract(sourceBalance).compareTo(balance) == 0;
        }

    }
    //获取金额
    private BigDecimal getBalance(Long applyUser) {
        String sourceUaaId = uaaFeignClient.getByUaaId(applyUser);
        UaaUserWallet sourceWallet = getWallet(sourceUaaId);
        BigDecimal sourceBalance = sourceWallet.getBalance();
        return sourceBalance;
    }
    //获取积分
    private Integer getPoints(Long applyUser) {
        String sourceUaaId = uaaFeignClient.getByUaaId(applyUser);
        UaaUserWallet sourceWallet = getWallet(sourceUaaId);
        Integer sourcePoints = sourceWallet.getPoints();
        return sourcePoints;
    }
    private Boolean validatePointCashCondition(Integer points, Integer sourPoints,Integer targetPoints) {
        if (sourPoints>targetPoints){
            return sourPoints-targetPoints == points;
        }else {
            return targetPoints-sourPoints == points;
        }

    }
}
