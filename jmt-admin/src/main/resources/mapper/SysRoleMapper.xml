<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysRoleDao">
    <resultMap id="BaseResultMap" type="com.jmt.model.admin.role.SysRole">
        <id property="id" column="id" />
        <result property="roleKey" column="roleKey" />
        <result property="roleName" column="roleName" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>
    <sql id="Base_Column_List">
        id, roleKey, roleName, createTime, updateTime, isDelete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from sys_role where id = #{id}
    </select>
    <select id="findByRoleIds" resultType="com.jmt.model.admin.role.SysRole">
        SELECT <include refid="Base_Column_List" /> FROM sys_role WHERE isDelete = 0 and id IN <foreach collection="roleIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>
    <select id="getPage" parameterType="com.jmt.model.admin.role.SysRole" resultType="com.jmt.model.admin.role.SysRole">
        SELECT <include refid="Base_Column_List" /> FROM sys_role t
        <where>
            <if test="id != null">AND t.id = #{id}</if>
            <if test="roleKey != null and roleKey != ''">AND t.roleKey = #{roleKey}</if>
            <if test="roleName != null and roleName != ''">AND t.roleName = #{roleName}</if>
            <if test="createTime != null">AND t.createTime &gt;= #{createTime}</if>
            <if test="updateTime != null">AND t.updateTime &lt;= #{updateTime}</if>
            <if test="isDelete != null">AND t.isDelete = #{isDelete}</if>
        </where>
        ORDER BY t.createTime DESC
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update sys_role set isDelete = 1, updateTime = NOW() where id = #{id} and isDelete = 0
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.role.SysRole" useGeneratedKeys="true">
        insert into sys_role (id, roleKey, roleName, createTime, updateTime, isDelete) values (#{id}, #{roleKey}, #{roleName}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.role.SysRole" useGeneratedKeys="true">
        insert into sys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="roleKey != null">roleKey,</if>
            <if test="roleName != null">roleName,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="roleKey != null">#{roleKey},</if>
            <if test="roleName != null">#{roleName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.role.SysRole">
        update sys_role
        <set>
            <if test="roleKey != null">roleKey = #{roleKey},</if>
            <if test="roleName != null">roleName = #{roleName},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.role.SysRole">
        update sys_role set roleKey = #{roleKey}, roleName = #{roleName}, createTime = #{createTime}, updateTime = #{updateTime}, isDelete = #{isDelete} where id = #{id}
    </update>
    <select id="selectAllAvailable" resultMap="BaseResultMap">
        SELECT id, roleName,  createTime, updateTime, isDelete FROM sys_role WHERE isDelete = 0 ORDER BY createTime DESC
    </select>
</mapper>