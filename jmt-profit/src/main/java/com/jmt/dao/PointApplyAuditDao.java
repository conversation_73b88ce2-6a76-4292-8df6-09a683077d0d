package com.jmt.dao;

import com.jmt.model.profit.dto.PointApply;
import com.jmt.model.profit.dto.PointApplyPageDto;
import com.jmt.model.profit.dto.PointsCashApplyAudit;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PointApplyAuditDao {
    /**
     * 根据申请编号查询审核信息
     * @param applyNo
     * @return
     */
    List<PointsCashApplyAudit> getInfoByApplyNo(String applyNo);
    /**
     * 修改审核信息
     * @param applyAudit
     */
    void update(PointsCashApplyAudit applyAudit);
    /**
     * 新增审核信息
     * @param applyAudit
     */
    void add(PointsCashApplyAudit applyAudit);
}
