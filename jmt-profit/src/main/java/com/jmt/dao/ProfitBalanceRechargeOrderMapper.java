package com.jmt.dao;

import com.jmt.model.profit.ProfitBalanceRechargeOrder;

/**
* <AUTHOR>
* @description 针对表【profit_balance_recharge _order(余额充值订单表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitBalanceRechargeOrder
*/
public interface ProfitBalanceRechargeOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitBalanceRechargeOrder record);

    int insertSelective(ProfitBalanceRechargeOrder record);

    ProfitBalanceRechargeOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitBalanceRechargeOrder record);

    int updateByPrimaryKey(ProfitBalanceRechargeOrder record);

}
