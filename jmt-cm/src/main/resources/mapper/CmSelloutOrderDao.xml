<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmSelloutOrderDao">
    <sql id="Base_Column_List">
        id, infoId, orderNo, orderName, busNo, payType, payAmount, orderStatus, reason, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmSelloutOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_sellout_order (
        infoId,
        orderNo,
        orderName,
        busNo,
        payType,
        payAmount,
        orderStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        ) VALUES (
        #{infoId},
        #{orderNo},
        #{orderName},
        #{busNo},
        #{payType},
        #{payAmount},
        #{orderStatus},
        #{reason},
        #{createTime},
        #{updateTime},
        #{isDelete}
        )
    </insert>

    <!-- 根据订单 ID 查询订单 -->
    <select id="selectById" parameterType="long" resultType="com.jmt.model.cm.entity.CmSelloutOrder">
        SELECT  <include refid="Base_Column_List"/>
        FROM cm_sellout_order WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 更新订单状态 -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmSelloutOrder">
        UPDATE cm_sellout_order
        SET
        orderStatus = #{orderStatus},
        updateTime = #{updateTime},
        reason = #{reason}
        WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="delete">
        UPDATE cm_sellout_order
        SET isDelete = 1,updateTime = NOW()
        WHERE id = #{id}
    </update>
</mapper>