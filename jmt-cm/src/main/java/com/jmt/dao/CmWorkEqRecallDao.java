package com.jmt.dao;

import com.jmt.model.cm.dto.CmWorkEqRecallDTO;
import com.jmt.model.cm.entity.CmWorkEqRecall;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmWorkEqRecallDao {
    Integer insert(CmWorkEqRecall record);
    Integer update(CmWorkEqRecall record);

    Integer delete(Long id);

    CmWorkEqRecall selectById(@Param("id") Long id);

    List<CmWorkEqRecallDTO> selectByCondition(CmWorkEqRecallDTO cmWorkEqRecallDTO);
}