<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.BalanceRechargeOrderDao">
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO profit_balance_recharge_order
		(orderNo,orderName,rechargeUser,payType,payAmount,orderStatus,reason,createTime,updateTime,isDelete)
		VALUES
		(#{orderNo},#{orderName},#{rechargeUser},#{payType},#{payAmount},#{orderStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
	</insert>
	<update id="update">
		UPDATE profit_balance_recharge_order
		<set>
			<if test="orderStatus != null">
				orderStatus = #{orderStatus},
			</if>
		</set>
		WHERE id = #{id}
		AND isDelete = 0
	</update>
	<select id="getBalancePage" resultType="com.jmt.model.profit.vo.BalanceRechargeOrderVo">
		SELECT
		    id,
		    orderNo,
		    orderName,
		    rechargeUser,
		    payType,
		    payAmount,
		    orderStatus,
		    reason,
		    createTime,
		    updateTime,
		    isDelete
		FROM profit_balance_recharge_order
		<where>
			<if test="queryData != null and queryData.orderName != null">
				orderName = #{queryData.orderName}
			</if>
			<if test="queryData != null and queryData.payType != null">
				payType = #{queryData.payType}
			</if>
			<if test="queryData != null and queryData.orderStatus != null">
				orderStatus = #{queryData.orderStatus}
			</if>
		</where>
	</select>
	<select id="getInfoByOrderNo" resultType="com.jmt.model.profit.dto.BalanceRechargeOrder">
		SELECT
		    id,
		    orderNo,
		    orderName,
		    rechargeUser,
		    payType,
		    payAmount,
		    orderStatus,
		    reason,
		    createTime,
		    updateTime,
		    isDelete
		FROM profit_balance_recharge_order
		WHERE orderNo = #{orderNo}
		AND isDelete = 0
	</select>
</mapper>