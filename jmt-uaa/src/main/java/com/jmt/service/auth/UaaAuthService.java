package com.jmt.service.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseService;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.dao.UaaAuthDao;
import com.jmt.model.auth.*;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.service.UaaUserService;
import com.jmt.util.CaptchaUtil;
import com.jmt.util.IpUtil;
import com.jmt.util.JwtTokenUtil;
import com.jmt.util.RedisUtil;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Service
public class UaaAuthService extends BaseService {

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private UaaAuthDao uaaAuthDao;

    @Resource
    private UaaUserService uaaUserService;

    @Resource
    private RedisUtil redisUtil;

    public String passwordAuthLogin(PasswordAuthLoginDto loginDto) {
        String token = null;
        //密码需要客户端加密后传递
        try {
            LoginUaaUser userDetails = this.loadUserByUsername(loginDto.getLoginName());
            if (ObjectUtil.isEmpty(userDetails.getUaaId())) {
                logger.error("账号密码登录失败：账号不存在");
                return super.responseFailAuth("账号或密码错误");
            }
            if(!passwordEncoder.matches(loginDto.getLoginName() + loginDto.getPassword(),userDetails.getPassword())){
                logger.error("账号密码登录失败：密码错误");
                return super.responseFailAuth("账号或密码错误");
            }
            if(!userDetails.isEnabled()){
                logger.error("账号密码登录失败：账号已禁用,请联系管理员");
                return super.responseFailAuth("账号已禁用,请联系管理员");
            }
            userDetails.setUserType(loginDto.getUserType());//添加登录者的身份
            userDetails.setClientId(loginDto.getClientId());//添加登录客户端ID
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            token = JwtTokenUtil.createToken(BeanUtil.beanToMap(userDetails),userDetails.getLoginName());
            redisUtil.set(RedisKeyConstant.TOKEN_CACHE_KEY + userDetails.getUaaId(),token,RedisKeyConstant.TOKEN_CACHE_EXPIRE);
        } catch (AuthenticationException e) {
            logger.error("账号密码登录异常:", e);
        }
        return responseSuccessAuth(token);
    }

    private LoginUaaUser loadUserByUsername(String loginName) {
        UaaUser uaaUser = uaaAuthDao.loadUserByUsername(loginName);
        LoginUaaUser loginUaaUser =  new LoginUaaUser();
        BeanUtil.copyProperties(uaaUser,loginUaaUser,false);
        loginUaaUser.setRoleKeys(new ArrayList<>());
        loginUaaUser.setResUrls(new ArrayList<>());
        return loginUaaUser;
    }

    LoginUaaUser loadUserByTelPhone(String telPhone) {
        UaaUser uaaUser = uaaAuthDao.loadUserByTelPhone(telPhone);
        LoginUaaUser loginUaaUser =  new LoginUaaUser();
        BeanUtil.copyProperties(uaaUser,loginUaaUser,false);
        loginUaaUser.setRoleKeys(new ArrayList<>());
        loginUaaUser.setResUrls(new ArrayList<>());
        return loginUaaUser;
    }


    LoginUaaUser registerUaaUser(UaaUserDTO uaaUserDTO) {
        UaaUser uaaUser = uaaUserService.registerUser(uaaUserDTO);
        LoginUaaUser loginUaaUser =  new LoginUaaUser();
        BeanUtil.copyProperties(uaaUser,loginUaaUser,false);
        loginUaaUser.setRoleKeys(new ArrayList<>());
        loginUaaUser.setResUrls(new ArrayList<>());
        return loginUaaUser;
    }


    public String createBase64Image(){

        try {
            String clientIp = IpUtil.getRemoteHost();
            Map<String, Object> map;
            String code;
            while (true) {
                map = CaptchaUtil.initCode();
                code = Convert.toStr(map.get("code"), "");
                boolean doesItExist = redisUtil.hasKey(RedisKeyConstant.CAPTCHA_CACHE_KEY + code);
                if (!doesItExist) {
                    redisUtil.set(RedisKeyConstant.CAPTCHA_CACHE_KEY + clientIp,code,RedisKeyConstant.CAPTCHA_CACHE_EXPIRE);
                    break;
                }
            }
            // 将图像输出。
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                ImageIO.write((BufferedImage) map.get("image"), "jpeg", outputStream);
                byte[] base64Img = Base64Utils.encode(outputStream.toByteArray());
                return "data:image/jpeg;base64," + new String(base64Img).replaceAll("\n", "");
            } catch (Exception e) {
                logger.error("获取验证码异常:", e);
            }
        } catch (Exception e) {
            logger.error("获取验证码异常:", e);
        }
        return null;
    }
}
