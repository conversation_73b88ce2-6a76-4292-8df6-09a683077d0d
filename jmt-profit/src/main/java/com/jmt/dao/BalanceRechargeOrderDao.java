package com.jmt.dao;

import com.jmt.model.profit.dto.BalanceRechargeOrder;
import com.jmt.model.profit.dto.BalanceRechargeOrderDto;
import com.jmt.model.profit.dto.BalanceRecord;
import com.jmt.model.profit.vo.BalanceRechargeOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BalanceRechargeOrderDao {
    /**
     * 添加充值订单
     *
     * @param balanceRechargeOrder
     */
    void add(BalanceRechargeOrder balanceRechargeOrder);
    /**
     * 修改充值订单
     *
     * @param balanceRechargeOrder
     */
    void update(BalanceRechargeOrder balanceRechargeOrder);
    /**
     * 获取充值订单信息
     *
     * @param queryData
     * @return
     */
    List<BalanceRechargeOrderVo> getBalancePage(@Param("queryData") BalanceRechargeOrderDto queryData);
    /**
     * 获取充值订单信息
     *
     * @param orderNo
     * @return
     */
    BalanceRechargeOrder getInfoByOrderNo(String orderNo);
}
