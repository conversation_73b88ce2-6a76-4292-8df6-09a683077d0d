package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;

import com.jmt.dao.*;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.BusinessException;
import com.jmt.model.profit.dto.*;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.DateUtil;
import com.jmt.util.ProfitUserNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class PointCashService extends BaseService {
    //最低提现积分
    private static final int MIN_POINT = 10;
    //最高提现积分
    private static final int MAX_POINT = 5000000;
    //每日兑现积分上限
    private static final int MAX_DAY_COUNT = 3;
    //每周兑现次数上限
    private static final int MAX_WEEK_COUNT = 10;
    //每月兑现次数上限
    private static final int MAX_MONTH_COUNT = 20;
    //积分余额兑换比例
//    private static final BigDecimal POINT_RATIO = new BigDecimal("0.01");
    private static final Double POINT_RATIO = 0.01;
    //税率
    private static final Double TAX_RATIO = 0.05;
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private PointApplyDao pointApplyDao;
    @Resource
    private PointsRecordDao pointsRecordDao;
    @Resource
    private PointApplyAuditDao pointApplyAuditDao;
    @Resource
    private BalanceRecordDao balanceRecordDao;
    /**
     * 申请兑现
     *
     * @param pointApplyDto
     * @param applyUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void apply(PointApplyDto pointApplyDto, Long applyUser) {
        // 1. 校验用户存在
        PointApply pointApply = new PointApply();
        BeanUtils.copyProperties(pointApplyDto, pointApply);
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        // 2. 获取积分配置，校验积分是否满足兑现条件
        Integer points = pointApplyDto.getPoints();
        validatePointsCashCondition(userWallet,points);
        // 3. 校验兑现频次限制（每天/每周/每月）
        Date now = new Date();
        Date startDay = DateUtil.getDayStart(now);
        Date endDay = DateUtil.getDayEnd(now);
        Date startWeek = DateUtil.getWeekStart(now);
        Date endWeek = DateUtil.getWeekEnd(now);
        Date startMonth = DateUtil.getMonthStart(now);
        Date endMonth = DateUtil.getMonthEnd(now);
        int countDay = pointApplyDao.getCountByUaaIdAndCreateTime(applyUser, startDay, endDay);
        int countWeek = pointApplyDao.getCountByUaaIdAndCreateTime(applyUser, startWeek, endWeek);
        int countMonth = pointApplyDao.getCountByUaaIdAndCreateTime(applyUser, startMonth, endMonth);
        if (countDay >= MAX_DAY_COUNT){
            throw new RuntimeException("今日已超过最大提现次数限制");
        }
        if (countWeek >= MAX_WEEK_COUNT){
            throw new RuntimeException("本周已超过最大提现次数限制");
        }
        if (countMonth >= MAX_MONTH_COUNT){
            throw new RuntimeException("本月已超过最大提现次数限制");
        }
        // 4. 扣减用户积分
        uaaFeignClient.decreasePoints(applyUser, points);
        // 5.根据是否上传发票决定是否需要审核
        if (pointApplyDto.getInvoiceProvide() == 1) {
            pointApply.setAuditStatus("0");// 待审核
        }else{
            pointApply.setAuditStatus("1");// 无需审核
            // 积分转余额基准比例
            BigDecimal baseRate = new BigDecimal(POINT_RATIO);
            // 计算原始金额
            BigDecimal originalAmount = new BigDecimal(points).multiply(baseRate);
            //计算可兑现余额(扣税)
            BigDecimal taxRate = new BigDecimal(TAX_RATIO); // 税率
            BigDecimal taxAmount = originalAmount.multiply(taxRate);  // 税费 = 原始金额 * 税率
            BigDecimal finalAmount = originalAmount.subtract(taxAmount);  // 最终可兑现金额 = 原始金额 - 扣税
            userWallet.setBalance(userWallet.getBalance().add(finalAmount));
            //记录余额变动
            BalanceRecord balanceRecord = new BalanceRecord();
            balanceRecord.setUaaId(applyUser);
            balanceRecord.setBalance(originalAmount);
            balanceRecord.setRecordType(0);
            balanceRecord.setBalanceSource(1);
            balanceRecord.setSourceId(pointApply.getApplyNo());
            balanceRecord.setCreateTime(now);
            balanceRecord.setUpdateTime(now);
            balanceRecord.setIsDelete(0);
            balanceRecord.setRemark(pointApplyDto.getReason());
            balanceRecordDao.add(balanceRecord);
        }
        // 7.记录积分变动
        Integer recordType = 1;
        recordPointsChange(applyUser, pointApply, now, recordType);
        // 6.创建积分兑现申请表
        pointApply.setApplyUser(applyUser);
        pointApply.setCreateTime(now);
        pointApply.setUpdateTime(now);
        pointApply.setIsDelete(0);
        String applyNo = ProfitUserNoUtil.generate("PA");
        pointApply.setApplyNo(applyNo);
        pointApplyDao.add(pointApply);

        // 8.需要审核 or 不需要审核
        // 9.计算兑现金额（未上传发票需要扣税）or 如果不需要审核，直接处理后续流程
    }
    /**
     * 获取积分申请列表
     *
     * @param pageQuery
     * @return
     */
    public PageResult<PointApply> getPage(PageQuery<PointApplyPageDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<PointApply> page = pointApplyDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 审核
     */
    @Transactional(rollbackFor = Exception.class)
    public void audit(PointApplyAuditDto pointApplyAuditDto, Long auditUser) {
        Long id = pointApplyAuditDto.getId();
        Date now = new Date();
        String auditStatus = pointApplyAuditDto.getAuditStatus();
        String reason = pointApplyAuditDto.getReason();
        //查询积分兑现申请
        PointApply pointApply = pointApplyDao.getInfo(id);
        Long applyUser = pointApply.getApplyUser();
        String byUaaId = uaaFeignClient.getByUaaId(applyUser);
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(byUaaId).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        UaaUserWallet userWallet = gson.fromJson(dataElement, UaaUserWallet.class);
        if (pointApply == null){
            throw new BusinessException("申请不存在");
        }
        //校验申请状态
        if (!pointApply.getAuditStatus().equals("0")){
            throw new BusinessException("申请已处理");
        }
        //校验审核状态
        if (!(auditStatus.equals("1")||!auditStatus.equals("2"))){
            throw new BusinessException("审核状态有误");
        }

        //更新申请表申请状态
//        String applyStatus = pointApply.getAuditStatus();
        pointApply.setUpdateTime(now);
        pointApply.setReason(reason);
        pointApply.setAuditStatus(auditStatus);
        pointApplyDao.update(pointApply);
        //更新审核表申请状态
        PointsCashApplyAudit applyAudit = new PointsCashApplyAudit();
        applyAudit.setAuditUser(auditUser);
        applyAudit.setApplyNo(pointApply.getApplyNo());
        applyAudit.setReason(reason);
        applyAudit.setIsDelete(0);
        applyAudit.setAuditStatus(auditStatus);
        applyAudit.setUpdateTime(now);
        List<PointsCashApplyAudit> applyAudits = pointApplyAuditDao.getInfoByApplyNo(pointApply.getApplyNo());
        if (applyAudits != null && applyAudits.size() > 0) {
            log.info("有多个相同编号的审核信息");
            for (PointsCashApplyAudit applyAudit1 : applyAudits){
                if (applyAudit1.getAuditUser().equals(auditUser)){
                    log.info("有i相同uuid的记录");
                    applyAudit.setId(applyAudit1.getId());
                    pointApplyAuditDao.update(applyAudit);
                }else {
                    log.info("无i相同uuid的记录");
                    applyAudit.setCreateTime(now);
                    pointApplyAuditDao.add(applyAudit);
                }
            }
        }else{
            log.info("首次审核");
            applyAudit.setCreateTime(now);
            pointApplyAuditDao.add(applyAudit);
        }
        //计算兑现金额(审核通过)
        if (pointApplyAuditDto.getAuditStatus().equals("1")){
            // 积分转余额基准比例
            BigDecimal baseRate = new BigDecimal(POINT_RATIO);
            // 计算原始金额
            BigDecimal originalAmount = new BigDecimal(pointApply.getPoints()).multiply(baseRate);
            //支出
            Integer recordType = 1;
            //增加余额
//            userWallet.setBalance(userWallet.getBalance().add(originalAmount));
            uaaFeignClient.increaseBalance(applyUser, originalAmount);
            //记录余额变动
            BalanceRecord balanceRecord = new BalanceRecord();
            balanceRecord.setUaaId(applyUser);
            balanceRecord.setBalance(originalAmount);
            balanceRecord.setRecordType(0);
            balanceRecord.setBalanceSource(1);
            balanceRecord.setSourceId(pointApply.getApplyNo());
            balanceRecord.setRemark(reason);
            balanceRecord.setCreateTime(now);
            balanceRecord.setUpdateTime(now);
            balanceRecord.setIsDelete(0);
            balanceRecordDao.add(balanceRecord);

        }else{
            Integer recordType = 0;
            //退还积分
//            userWallet.setPoints(userWallet.getPoints() + pointApply.getPoints());
            uaaFeignClient.increasePoints(pointApply.getApplyUser(), pointApply.getPoints());
            //记录积分变动
            recordPointsChange(applyUser, pointApply, now,recordType);
        }
    }
    /**
     * 记录积分变动
     */
    private void recordPointsChange(Long applyUser, PointApply pointApply,Date now, Integer recordType) {
        PointsRecord pointsRecord = new PointsRecord();
        pointsRecord.setUaaId(applyUser);
        pointsRecord.setPoints(pointApply.getPoints());
        pointsRecord.setRecordType(recordType);//支出
        pointsRecord.setRemark(pointApply.getReason());
        pointsRecord.setCreateTime(now);
        pointsRecord.setUpdateTime(now);
        pointsRecordDao.insert(pointsRecord);
    }
    /**
     * 校验积分兑现条件
     */
    private void validatePointsCashCondition(UaaUserWallet userWallet, Integer points) {
        // 校验最低兑现积分
        if (points <= MIN_POINT){
            throw new BusinessException("积分低于最低提现限制");
        }
        // 校验用户积分是否足够
        if (userWallet.getPoints() < points) {
            throw new BusinessException("用户积分不足");
        }
        // 校验最高兑现积分
        if (points > MAX_POINT) {
            throw new BusinessException("积分高于最高提现限制");
        }
    }
}
