<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmWorkEqDeployDao">

    <sql id="Base_Column_List">
        id, workNo, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, deployerNo, applyWorkNo,
        workStatus, reason,createTime, updateTime, isDelete
    </sql>

    <!-- Insert -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmWorkEqDeploy" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_work_eq_deploy (
        workNo, busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, operatorNo, refereeNo, deployerNo,applyWorkNo, workStatus,reason,
        createTime, updateTime, isDelete
        )
        VALUES (
        #{workNo,jdbcType=VARCHAR},
        #{busNo,jdbcType=VARCHAR},
        #{busName,jdbcType=VARCHAR},
        #{linkman,jdbcType=VARCHAR},
        #{telPhone,jdbcType=VARCHAR},
        #{country,jdbcType=VARCHAR},
        #{province,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{address,jdbcType=VARCHAR},
        #{longitude,jdbcType=VARCHAR},
        #{latitude,jdbcType=VARCHAR},
        #{operatorNo,jdbcType=VARCHAR},
        #{refereeNo,jdbcType=VARCHAR},
        #{deployerNo,jdbcType=VARCHAR},
        #{applyWorkNo,jdbcType=VARCHAR},
        #{workStatus,jdbcType=INTEGER},
        #{reason,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=INTEGER}
        )
    </insert>

    <!-- Select by primary key -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        SELECT
        <include refid="Base_Column_List" />
        FROM cm_work_eq_deploy
        WHERE id = #{id,jdbcType=BIGINT}
        AND isDelete = 0
    </select>

    <!-- Update -->
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        UPDATE cm_work_eq_deploy
        SET
        workNo = #{workNo,jdbcType=VARCHAR},
        busNo = #{busNo,jdbcType=VARCHAR},
        busName = #{busName,jdbcType=VARCHAR},
        linkman = #{linkman,jdbcType=VARCHAR},
        telPhone = #{telPhone,jdbcType=VARCHAR},
        country = #{country,jdbcType=VARCHAR},
        province = #{province,jdbcType=VARCHAR},
        city = #{city,jdbcType=VARCHAR},
        area = #{area,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        longitude = #{longitude,jdbcType=VARCHAR},
        latitude = #{latitude,jdbcType=VARCHAR},
        operatorNo = #{operatorNo,jdbcType=VARCHAR},
        refereeNo = #{refereeNo,jdbcType=VARCHAR},
        deployerNo = #{deployerNo,jdbcType=VARCHAR},
        applyWorkNo = #{applyWorkNo,jdbcType=VARCHAR},
        workStatus = #{workStatus,jdbcType=INTEGER},
        reason = #{reason,jdbcType=VARCHAR},
        updateTime = #{updateTime,jdbcType=TIMESTAMP},
        isDelete = #{isDelete,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <update id="update" parameterType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        UPDATE cm_work_eq_deploy
        <set>
            <if test="workNo != null">workNo = #{workNo,jdbcType=VARCHAR},</if>
            <if test="busNo != null">busNo = #{busNo,jdbcType=VARCHAR},</if>
            <if test="busName != null">busName = #{busName,jdbcType=VARCHAR},</if>
            <if test="linkman != null">linkman = #{linkman,jdbcType=VARCHAR},</if>
            <if test="telPhone != null">telPhone = #{telPhone,jdbcType=VARCHAR},</if>
            <if test="country != null">country = #{country,jdbcType=VARCHAR},</if>
            <if test="province != null">province = #{province,jdbcType=VARCHAR},</if>
            <if test="city != null">city = #{city,jdbcType=VARCHAR},</if>
            <if test="area != null">area = #{area,jdbcType=VARCHAR},</if>
            <if test="address != null">address = #{address,jdbcType=VARCHAR},</if>
            <if test="longitude != null">longitude = #{longitude,jdbcType=VARCHAR},</if>
            <if test="latitude != null">latitude = #{latitude,jdbcType=VARCHAR},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo,jdbcType=VARCHAR},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo,jdbcType=VARCHAR},</if>
            <if test="applyWorkNo != null"> applyWorkNo = #{applyWorkNo,jdbcType=VARCHAR},</if>
            <if test="deployerNo != null"> deployerNo = #{deployerNo,jdbcType=VARCHAR},</if>
            <if test="workStatus != null">workStatus = #{workStatus,jdbcType=INTEGER},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createTime != null">createTime = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">updateTime = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">isDelete = #{isDelete,jdbcType=INTEGER},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- Delete (logical delete) -->
    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_work_eq_deploy
        SET isDelete = 1,
        updateTime = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAll" resultType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        SELECT
        <include refid="Base_Column_List" />
        FROM cm_work_eq_deploy
        WHERE isDelete = 0
    </select>

    <!-- Select by work status -->
    <select id="selectByWorkStatus" parameterType="java.lang.Integer" resultType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        SELECT
        <include refid="Base_Column_List" />
        FROM cm_work_eq_deploy
        WHERE work_status = #{workStatus,jdbcType=INTEGER}
        AND isDelete = 0
    </select>

    <!-- Select by work no -->
    <select id="selectByWorkNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        SELECT
        <include refid="Base_Column_List" />
        FROM cm_work_eq_deploy
        WHERE workNo = #{workNo,jdbcType=VARCHAR}
        AND isDelete = 0
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmWorkEqDeploy">
        SELECT
        <include refid="Base_Column_List" />
        FROM cm_work_eq_deploy
        WHERE id = #{id,jdbcType=BIGINT} and isDelete = 0
    </select>

    <select id="selectByCondition" parameterType="com.jmt.model.cm.dto.CmWorkEqDeployDTO" resultType="com.jmt.model.cm.dto.CmWorkEqDeployDTO">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_deploy
        <where>
            isDelete = 0
            <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
            <if test="workNo != null and workNo != ''">AND workNo = #{workNo,jdbcType=VARCHAR}</if>
            <if test="busNo != null and busNo != ''">AND busNo = #{busNo,jdbcType=VARCHAR}</if>
            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName,jdbcType=VARCHAR}, '%')</if>
            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman,jdbcType=VARCHAR}, '%')</if>
            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone,jdbcType=VARCHAR}</if>
            <if test="country != null and country != ''">AND country = #{country,jdbcType=VARCHAR}</if>
            <if test="province != null and province != ''">AND province = #{province,jdbcType=VARCHAR}</if>
            <if test="city != null and city != ''">AND city = #{city,jdbcType=VARCHAR}</if>
            <if test="area != null and area != ''">AND area = #{area,jdbcType=VARCHAR}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address,jdbcType=VARCHAR}, '%')</if>
            <if test="longitude != null and longitude != ''">AND longitude = #{longitude,jdbcType=VARCHAR}</if>
            <if test="latitude != null and latitude != ''">AND latitude = #{latitude,jdbcType=VARCHAR}</if>
            <if test="operatorNo != null and operatorNo != ''">AND operatorNo = #{operatorNo,jdbcType=VARCHAR}</if>
            <if test="refereeNo != null and refereeNo != ''">AND refereeNo = #{refereeNo,jdbcType=VARCHAR}</if>
            <if test="applyWorkNo != null and applyWorkNo != ''">AND applyWorkNo = #{applyWorkNo,jdbcType=VARCHAR}</if>
            <if test="deployerNo != null and deployerNo != ''">AND deployerNo = #{deployerNo,jdbcType=VARCHAR}</if>
            <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>
            <if test="reason != null and reason != ''">AND reason LIKE CONCAT('%', #{reason,jdbcType=VARCHAR}, '%')</if>
        </where>
        ORDER BY createTime
    </select>
</mapper>