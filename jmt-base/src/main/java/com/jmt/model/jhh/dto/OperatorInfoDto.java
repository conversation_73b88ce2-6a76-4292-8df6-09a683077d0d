package com.jmt.model.jhh.dto;

import com.jmt.model.profit.dto.ShareConfigDto;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

@Data
public class OperatorInfoDto {

    private Long id;

    private String operatorName;

    private String operatorNo;

    private String headImg;

    private String contractNo;

    private String dutyName;

    private String telPhone;

    private Integer isProfitLimit;

    private Integer limitAmount;

    private String contractPicUrl;

    private String licensePicUrl;

    private Integer auditStatus;

    private String reason;

    private Integer isFirstLogin;

    private Integer isDelete;

    private List<ShareConfigDto> shareConfigs = new ArrayList<>();
}