package com.jmt.dao;

import com.jmt.model.profit.ProfitShareConfig;

/**
* <AUTHOR>
* @description 针对表【profit_share_config(收益分润配置表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitShareConfig
*/
public interface ProfitShareConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitShareConfig record);

    int insertSelective(ProfitShareConfig record);

    ProfitShareConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitShareConfig record);

    int updateByPrimaryKey(ProfitShareConfig record);

    ProfitShareConfig selectActiveByProjectId(Long projectId);
}
