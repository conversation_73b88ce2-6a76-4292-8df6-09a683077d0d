package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmWorkEqApplyConvertor;
import com.jmt.convertor.CmWorkEqApplyDetailConvertor;
import com.jmt.dao.CmWorkEqApplyDao;
import com.jmt.dao.CmWorkEqApplyDetailDao;
import com.jmt.model.cm.dto.CmWorkEqApplyDTO;
import com.jmt.model.cm.entity.CmWorkEqApply;
import com.jmt.model.cm.entity.CmWorkEqApplyDetail;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmWorkEqApplyVO;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CmWorkEqApplyService extends BaseService {

    @Resource
    private CmWorkEqApplyDao cmWorkEqApplyDao;

    @Resource
    private CmWorkEqApplyDetailDao cmWorkEqApplyDetailDao;

    @Resource
    private CmWorkEqApplyConvertor cmWorkEqApplyConvertor;

    @Resource
    private CmWorkEqApplyDetailConvertor cmWorkEqApplyDetailConvertor;


    @Transactional
    public int create(CmWorkEqApplyDTO dto) {
        // 生成工单编号（使用日期+随机数）
        String workNo = WorkNoGeneratorUtil.generate("AEQ");
        // 计算设备总数
        Integer eqTotalNum = 0;
        if (dto.getDetails() != null) {
            eqTotalNum = dto.getDetails().stream()
                    .mapToInt(detailDto -> detailDto.getEqNum() != null ? detailDto.getEqNum() : 0)
                    .sum();
        }

        // 准备主表数据
        CmWorkEqApply apply = cmWorkEqApplyConvertor.toEntity(dto);
        apply.setWorkNo(workNo);
        apply.setWorkStatus(0);
        apply.setEqTotalNum(eqTotalNum);
        apply.setCreateTime(new Date());
        apply.setUpdateTime(new Date());
        apply.setIsDelete(0);
        // 先插入主表
        int result = cmWorkEqApplyDao.insert(apply);

        // 处理明细表
        if (result > 0 && dto.getDetails() != null && !dto.getDetails().isEmpty()) {
            List<CmWorkEqApplyDetail> details = dto.getDetails().stream()
                    .map(detailDto -> {
                        CmWorkEqApplyDetail detail = cmWorkEqApplyDetailConvertor.toEntity(detailDto);
                        detail.setWorkNo(workNo);
                        detail.setBusNo(dto.getBusNo());
                        detail.setBusName(dto.getBusName());
                        detail.setCreateTime(new Date());
                        detail.setUpdateTime(new Date());
                        detail.setIsDelete(0);
                        return detail;
                    })
                    .collect(Collectors.toList());

            cmWorkEqApplyDetailDao.batchInsert(details);
        }

        return result;
    }

    @Transactional
    public int update(CmWorkEqApplyDTO dto) {
        // 1. 验证工单是否存在
        CmWorkEqApply cmWorkEqApply = cmWorkEqApplyDao.selectById(dto.getId());
        if (cmWorkEqApply == null) {
            throw new RuntimeException("工单不存在");
        }

        // 2. 只能修改待审核状态的工单
        if (cmWorkEqApply.getWorkStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        // 3. 更新主表
        CmWorkEqApply apply = cmWorkEqApplyConvertor.toEntity(dto);
        apply.setUpdateTime(new Date());
        int result = cmWorkEqApplyDao.update(apply);

        // 4. 更新明细表（先删除旧明细，再插入新明细）
        if (result > 0 && dto.getDetails() != null) {
            // 删除原有明细
            cmWorkEqApplyDetailDao.deleteByWorkNo(cmWorkEqApply.getWorkNo());

            // 插入新明细
            List<CmWorkEqApplyDetail> details = dto.getDetails().stream()
                    .map(detailDto -> {
                        CmWorkEqApplyDetail detail = cmWorkEqApplyDetailConvertor.toEntity(detailDto);
                        detail.setWorkNo(cmWorkEqApply.getWorkNo()); // 使用原有的工单编号
                        detail.setBusNo(dto.getBusNo());
                        detail.setBusName(dto.getBusName());
                        detail.setCreateTime(new Date());
                        detail.setUpdateTime(new Date());
                        detail.setIsDelete(0);
                        return detail;
                    })
                    .collect(Collectors.toList());

            if (!details.isEmpty()) {
                cmWorkEqApplyDetailDao.batchInsert(details);
            }
        }

        return result;
    }


    @Transactional
    public int delete(Long id) {
        CmWorkEqApply cmWorkEqApply = cmWorkEqApplyDao.selectById(id);
        if (cmWorkEqApply == null) {
            throw new RuntimeException("工单不存在");
        }
        cmWorkEqApplyDetailDao.deleteByWorkNo(cmWorkEqApply.getWorkNo());
        return cmWorkEqApplyDao.delete(id);
    }

    @Transactional
    public CmWorkEqApplyVO getById(Long id) {
        return cmWorkEqApplyConvertor.toVo(cmWorkEqApplyDao.selectById(id));

    }


    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmWorkEqApply> getPage(PageQuery<CmWorkEqApply> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<CmWorkEqApply> list = cmWorkEqApplyDao.selectByCondition(pageQuery.getQueryData());
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }


    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason) {
        CmWorkEqApply cmWorkEqApply = cmWorkEqApplyDao.selectById(id);
        if (cmWorkEqApply == null) {
            throw new RuntimeException("工单不存在");
        }
        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmWorkEqApply.getWorkStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        CmWorkEqApply update = new CmWorkEqApply();
        update.setId(id);
        update.setWorkStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmWorkEqApplyDao.update(update);
    }

    public List<CmWorkEqApplyDetail> getDetailById(Long id) {
        CmWorkEqApply apply = cmWorkEqApplyDao.selectById(id);
        if (apply == null || apply.getIsDelete() == 1) {
            throw new RuntimeException("工单不存在或已被删除");
        }
        List<CmWorkEqApplyDetail> details = cmWorkEqApplyDetailDao.selectByWorkNo(apply.getWorkNo());
        return details;
    }



}
