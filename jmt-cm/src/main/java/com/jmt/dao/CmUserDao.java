package com.jmt.dao;

import com.jmt.model.cm.entity.CmUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface CmUserDao {

    CmUser selectById(@Param("id") Long id);

    Integer insert(CmUser user);

    Integer updateByUaaId(CmUser user);
    Integer updateById(CmUser user);

    Integer deleteById(@Param("id") Long id);
    CmUser selectByUaaId(@Param("uaaId") Long uaaId);

}
