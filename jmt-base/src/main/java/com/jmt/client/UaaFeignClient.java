package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.uaa.dto.UaaUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@FeignClient(name="jmt-uaa", path ="/uaa", configuration = FeignConfig.class)
public interface UaaFeignClient {

    @PostMapping(value ="/v1/addUaaUser", produces = "application/json")
    String registerUser(@RequestBody UaaUserDTO userDTO);

    @PostMapping(value ="/v1/unlock", produces = "application/json")
    String unlockUser();

    @PostMapping("/wallet/v1/decreaseBalance")
    String decreaseBalance(@RequestParam("uaaId") Long uaaId, @RequestParam("amount") BigDecimal amount);

    @PostMapping("/wallet/v1/validatePayPassword")
    Boolean validatePayPassword(@RequestParam("uaaId") Long uaaId,
                                @RequestParam("payPassword") String payPassword);

    @PostMapping("/wallet/v1/decreasePoints")
    String decreasePoints(@RequestParam("uaaId") Long uaaId, @RequestParam("points") Integer points);

    @GetMapping("/wallet/v1/{uaaId}")
    String getByUaaId(@PathVariable("uaaId") Long uaaId);

    @GetMapping("/bankcard/v1/{id}")
    String getById(@PathVariable("id") Long id);

    @PostMapping("/wallet/v1/increasePoints")
    String increasePoints(@RequestParam("uaaId") Long uaaId, @RequestParam("points") Integer points);

    @PostMapping("/wallet/v1/increaseBalance")
    String increaseBalance(@RequestParam("uaaId") Long uaaId, @RequestParam("amount") BigDecimal amount);
}
