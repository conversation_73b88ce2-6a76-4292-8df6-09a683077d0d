<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitSharePredivideMapper">

    <sql id="Base_Column_List">
        id,orderNo,projectName,profitTotalPoints,operatorNo,investorNo,
        businessNo,operatorPoints,investorPoints,platformPoints,businessPoints,
        shareStauts,createTime,updateTime,isDelete  <!-- 修正为shareStauts -->
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitSharePredivide">
        select
        <include refid="Base_Column_List" />
        from profit_share_predivide
        where  id = #{id}
    </select>

    <select id="selectByShareStatus" resultType="com.jmt.model.profit.ProfitSharePredivide">
        SELECT * FROM profit_share_predivide
        WHERE shareStauts = #{shareStauts}  <!-- 字段名和参数名统一为shareStauts -->
        AND isDelete = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_share_predivide
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitSharePredivide" useGeneratedKeys="true">
        insert into profit_share_predivide
        ( id,orderNo,projectName,profitTotalPoints,operatorNo,investorNo,
        businessNo,operatorPoints,investorPoints,platformPoints,businessPoints,
        shareStauts,createTime,updateTime,isDelete)  <!-- 字段名是shareStauts -->
        values (#{id},#{orderNo},#{projectName},#{profitTotalPoints},#{operatorNo},#{investorNo},
        #{businessNo},#{operatorPoints},#{investorPoints},#{platformPoints},#{businessPoints},
        #{shareStauts},#{createTime},#{updateTime},#{isDelete})  <!-- 参数名统一为shareStauts -->
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitSharePredivide" useGeneratedKeys="true">
        insert into profit_share_predivide
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="projectName != null">projectName,</if>
            <if test="profitTotalPoints != null">profitTotalPoints,</if>
            <if test="operatorNo != null">operatorNo,</if>
            <if test="investorNo != null">investorNo,</if>
            <if test="businessNo != null">businessNo,</if>
            <if test="operatorPoints != null">operatorPoints,</if>
            <if test="investorPoints != null">investorPoints,</if>
            <if test="platformPoints != null">platformPoints,</if>
            <if test="businessPoints != null">businessPoints,</if>
            <if test="shareStauts != null">shareStauts,</if>  <!-- 字段名和test条件统一 -->
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="profitTotalPoints != null">#{profitTotalPoints},</if>
            <if test="operatorNo != null">#{operatorNo},</if>
            <if test="investorNo != null">#{investorNo},</if>
            <if test="businessNo != null">#{businessNo},</if>
            <if test="operatorPoints != null">#{operatorPoints},</if>
            <if test="investorPoints != null">#{investorPoints},</if>
            <if test="platformPoints != null">#{platformPoints},</if>
            <if test="businessPoints != null">#{businessPoints},</if>
            <if test="shareStauts != null">#{shareStauts},</if>  <!-- 参数名统一 -->
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitSharePredivide">
        update profit_share_predivide
        <set>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="projectName != null">
                projectName = #{projectName},
            </if>
            <if test="profitTotalPoints != null">
                profitTotalPoints = #{profitTotalPoints},
            </if>
            <if test="operatorNo != null">
                operatorNo = #{operatorNo},
            </if>
            <if test="investorNo != null">
                investorNo = #{investorNo},
            </if>
            <if test="businessNo != null">
                businessNo = #{businessNo},
            </if>
            <if test="operatorPoints != null">
                operatorPoints = #{operatorPoints},
            </if>
            <if test="investorPoints != null">
                investorPoints = #{investorPoints},
            </if>
            <if test="platformPoints != null">
                platformPoints = #{platformPoints},
            </if>
            <if test="businessPoints != null">
                businessPoints = #{businessPoints},
            </if>
            <if test="shareStauts != null">  <!-- test条件统一 -->
                shareStauts = #{shareStauts},  <!-- 字段名和参数名统一 -->
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitSharePredivide">
        update profit_share_predivide
        set
        orderNo =  #{orderNo},
        projectName =  #{projectName},
        profitTotalPoints =  #{profitTotalPoints},
        operatorNo =  #{operatorNo},
        investorNo =  #{investorNo},
        businessNo =  #{businessNo},
        operatorPoints =  #{operatorPoints},
        investorPoints =  #{investorPoints},
        platformPoints =  #{platformPoints},
        businessPoints =  #{businessPoints},
        shareStauts =  #{shareStauts},  <!-- 参数名统一 -->
        createTime =  #{createTime},
        updateTime =  #{updateTime},
        isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>