package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.EqBusinessSetupDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.InvestorEqService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eq/investorEq/v1")
public class InvestorEqController extends BaseController {
    @Resource
    private InvestorEqService investorEqService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Integer id) {
        EqOperatorStockVo eqOperatorStockVo = investorEqService.getInfo(id);
        return super.responseSuccess(eqOperatorStockVo,"查询成功");
    }

}
