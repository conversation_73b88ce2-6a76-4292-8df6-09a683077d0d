package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-jhh",path ="/jhh/salesman/v1",configuration = FeignConfig.class)
public interface JhhSalesmanFeignClient {
    @GetMapping(value = "/getSalesmanInfoByUaaId")
    String getSalesmanInfoByUaaId(@RequestParam("uaaId") Long uaaId);
}
