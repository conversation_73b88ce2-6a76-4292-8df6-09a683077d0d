<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.UaaUserBankcardDao">

	<insert id="insert" parameterType="com.jmt.model.uaa.UaaUserBankcard" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO uaa_user_bankcard
		(uaaId, bankName, cardNo, accountName, cardType, createTime, updateTime, isDelete, isDefault)
		VALUES
			(#{uaaId}, #{bankName}, #{cardNo}, #{accountName}, #{cardType}, #{createTime}, #{updateTime}, #{isDelete},#{isDefault})
	</insert>

	<update id="updateById" parameterType="com.jmt.model.uaa.UaaUserBankcard">
		UPDATE uaa_user_bankcard
		<set>
			<if test="uaaId != null">uaaId = #{uaaId},</if>
			<if test="bankName != null">bankName = #{bankName},</if>
			<if test="cardNo != null">cardNo = #{cardNo},</if>
			<if test="accountName != null">accountName = #{accountName},</if>
			<if test="cardType != null">cardType = #{cardType},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="isDelete != null">isDelete = #{isDelete},</if>
			<if test="isDefault != null">isDefault = #{isDefault},</if>
		</set>
		WHERE id = #{id}
	</update>

	<update id="deleteById" parameterType="java.lang.Long">
		UPDATE uaa_user_bankcard
		SET
			isDelete = 1,
			updateTime = NOW()
		WHERE
			id = #{id}
		  AND isDelete = 0
	</update>

	<select id="selectById" resultType="com.jmt.model.uaa.UaaUserBankcard">
		SELECT id, uaaId, bankName, cardNo, accountName, cardType, createTime, updateTime, isDefault
		FROM uaa_user_bankcard
		WHERE id = #{id} AND isDelete = 0
	</select>

	<select id="selectByUaaId" resultType="com.jmt.model.uaa.UaaUserBankcard">
		SELECT id, uaaId, bankName, cardNo, accountName, cardType, createTime, updateTime, isDefault
		FROM uaa_user_bankcard
		WHERE uaaId = #{uaaId} AND isDelete = 0
		ORDER BY createTime DESC
	</select>

	<!-- 查找用户的默认银行卡 -->
	<select id="findDefaultByUaaId" resultType="com.jmt.model.uaa.UaaUserBankcard">
		SELECT id, uaaId, bankName, cardNo, accountName, cardType, createTime, updateTime, isDefault FROM uaa_user_bankcard
		WHERE uaaId = #{uaaId} AND isDefault = 1 AND isDelete = 0
		LIMIT 1
	</select>

	<!-- 统计用户银行卡数量 -->
	<select id="countByUaaId" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM uaa_user_bankcard
		WHERE uaaId = #{uaaId} AND isDelete = 0
	</select>


</mapper>