<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysPermissionResourceDao">

    <!-- 基础表结构：column对应表中实际列名resoureId，property对应Java对象属性resourceId -->
    <resultMap id="BaseResultMap" type="com.jmt.model.admin.permission.SysPermissionResource">
        <id property="id" column="id" />
        <result property="permissId" column="permissId" />
        <result property="resourceId" column="resoureId" /> <!-- 修正：表中实际列名是resoureId -->
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, permissId, resoureId, createTime, updateTime, isDelete
    </sql>

    <!-- 批量删除权限关联的资源 -->
    <update id="batchDeleteByPermissId" parameterType="long">
        UPDATE sys_permission_resource
        SET isDelete = 1,
            updateTime = NOW()
        WHERE permissId = #{permissId}
          AND isDelete = 0
    </update>

    <!-- 批量插入权限与资源的关联 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO sys_permission_resource (permissId, resoureId, createTime, updateTime, isDelete)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.permissId}, #{item.resourceId}, #{item.createTime}, #{item.updateTime}, #{item.isDelete})
        </foreach>
    </insert>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_permission_resource
        where id = #{id}
    </select>

    <!-- 核心错误修正：根据权限ID列表查询资源ID列表 -->
    <select id="findByPermissionIds" resultType="java.lang.Long">
        SELECT resoureId  <!-- 修正：表中实际列名是resoureId（原错误为resourceId） -->
        FROM sys_permission_resource
        WHERE permissId IN
        <foreach collection="permissionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据主键逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        UPDATE sys_permission_resource
        SET isDelete = 1,
            updateTime = NOW()
        WHERE id = #{id}
          AND isDelete = 0
    </update>

    <!-- 插入记录 -->
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.permission.SysPermissionResource" useGeneratedKeys="true">
        insert into sys_permission_resource
        (id, permissId, resoureId, createTime, updateTime, isDelete)  <!-- 修正：列名resoureId -->
        values (#{id}, #{permissId}, #{resourceId}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.permission.SysPermissionResource" useGeneratedKeys="true">
        insert into sys_permission_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="permissId != null">permissId,</if>
            <if test="resourceId != null">resoureId,</if>  <!-- 修正：列名resoureId -->
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="permissId != null">#{permissId},</if>
            <if test="resourceId != null">#{resourceId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.permission.SysPermissionResource">
        update sys_permission_resource
        <set>
            <if test="permissId != null">
                permissId = #{permissId},
            </if>
            <if test="resourceId != null">
                resoureId = #{resourceId},  <!-- 修正：列名resoureId -->
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 全字段更新 -->
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.permission.SysPermissionResource">
        update sys_permission_resource
        set
        permissId = #{permissId},
        resoureId = #{resourceId},
        createTime = #{createTime},
        updateTime = #{updateTime},
        isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>