<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgCategoryMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgCategory">
        <id property="id" column="id" />
        <result property="parentId" column="parentId" />
        <result property="categoryNo" column="categoryNo" />
        <result property="categoryName" column="categoryName" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <resultMap id="CategoryTreeResultMap" type="com.jmt.model.zg.ZgCategory" extends="BaseResultMap">
        <collection property="children" column="id" select="selectChildCategories" />
    </resultMap>

    <sql id="Base_Column_List">
        id, parentId, categoryNo, categoryName, createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_category
        where id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_category
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgCategory" useGeneratedKeys="true">
        insert into zg_category
        (id, parentId, categoryNo, categoryName, createTime, updateTime, isDelete)
        values (#{id}, #{parentId}, #{categoryNo}, #{categoryName}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgCategory" useGeneratedKeys="true">
        insert into zg_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parentId,</if>
            <if test="categoryNo != null">categoryNo,</if>
            <if test="categoryName != null">categoryName,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="categoryNo != null">#{categoryNo},</if>
            <if test="categoryName != null">#{categoryName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgCategory">
        update zg_category
        <set>
            <if test="parentId != null">
                parentId = #{parentId},
            </if>
            <if test="categoryNo != null">
                categoryNo = #{categoryNo},
            </if>
            <if test="categoryName != null">
                categoryName = #{categoryName},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgCategory">
        update zg_category
        set
            parentId = #{parentId},
            categoryNo = #{categoryNo},
            categoryName = #{categoryName},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>

    <select id="selectCategoryTree" parameterType="java.lang.Boolean" resultMap="CategoryTreeResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_category
        WHERE
        parentId IS NULL
        <if test="includeDeleted != null and !includeDeleted">
            AND isDelete = 0
        </if>
        ORDER BY categoryNo ASC
    </select>

    <select id="selectChildCategories" parameterType="java.lang.Long" resultMap="CategoryTreeResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_category
        WHERE
        parentId = #{id}
        <if test="_parameter != null and !_parameter">
            AND isDelete = 0
        </if>
        ORDER BY categoryNo ASC
    </select>

    <select id="getCategoryPage" parameterType="com.jmt.model.zg.ZgCategory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_category t
        <where>
            <!-- 业态ID条件 -->
            <if test="id != null">
                AND t.id = #{id}
            </if>

            <!-- 业态编码条件 -->
            <if test="categoryNo != null and categoryNo != ''">
                AND t.categoryNo = #{categoryNo}
            </if>

            <!-- 业态名称条件 -->
            <if test="categoryName != null and categoryName != ''">
                AND t.categoryName = #{categoryName}
            </if>

            <!-- 父节点ID条件 -->
            <if test="parentId != null">
                AND t.parentId = #{parentId}
            </if>

            <!-- 创建时间范围查询（大于等于） -->
            <if test="createTime != null">
                AND t.createTime &gt;= #{createTime}
            </if>

            <!-- 更新时间范围查询（小于等于） -->
            <if test="updateTime != null">
                AND t.updateTime &lt;= #{updateTime}
            </if>

            <!-- 逻辑删除状态条件（0-未删除，1-已删除） -->
            <if test="isDelete != null">
                AND t.isDelete = #{isDelete}
            </if>
        </where>
        <!-- 按创建时间降序排列，确保分页结果稳定 -->
        ORDER BY t.createTime DESC
    </select>
</mapper>