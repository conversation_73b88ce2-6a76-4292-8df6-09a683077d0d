package com.jmt.service.permission;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.SysPermissionDao;
import com.jmt.dao.SysPermissionResourceDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.admin.permission.SysPermissionResource;
import com.jmt.model.admin.permission.SysPermissionResourceDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysPermissionService extends BaseService {

    @Resource
    private SysPermissionDao sysPermissionDao;
    @Resource
    private SysPermissionResourceDao sysPermissionResourceDao;

    /**
     * 新增权限（自动填充创建/更新时间，默认未删除）
     */
    public Integer addPermission(SysPermission sysPermission) {
        setCommonPermissionFields(sysPermission, false);
        return sysPermissionDao.insert(sysPermission);
    }

    /**
     * 更新权限（更新时间戳，保留创建时间）
     */
    public Integer updatePermission(SysPermission sysPermission) {
        setCommonPermissionFields(sysPermission, false);
        return sysPermissionDao.updateByPrimaryKeySelective(sysPermission);
    }

    /**
     * 逻辑删除权限（标记isDelete=1，更新时间戳）
     */
    public Integer deletePermission(SysPermission sysPermission) {
        setCommonPermissionFields(sysPermission, true);
        return sysPermissionDao.updateByPrimaryKeySelective(sysPermission);
    }

    /**
     * 新增权限-资源关联关系
     */
    public Integer addPermissionResource(SysPermissionResourceDto dto) {
        SysPermissionResource resource = convertToPermissionResource(dto);
        setCommonResourceFields(resource);
        return sysPermissionResourceDao.insertSelective(resource);
    }

    /**
     * 逻辑删除权限-资源关联（标记isDelete=1）
     */
    public Integer deletePermissionResource(SysPermissionResourceDto dto) {
        SysPermissionResource resource = convertToPermissionResource(dto);
        setCommonResourceFieldsForDeletion(resource);
        return sysPermissionResourceDao.updateByPrimaryKeySelective(resource);
    }

    /**
     * 更新权限-资源关联关系
     */
    public Integer updatePermissionResource(SysPermissionResourceDto dto) {
        SysPermissionResource resource = convertToPermissionResource(dto);
        setCommonResourceFields(resource);
        return sysPermissionResourceDao.updateByPrimaryKeySelective(resource);
    }

    /**
     * 填充权限通用字段（创建/更新时间、删除状态）
     * @param isDelete true-删除操作（仅更新时间+标记删除）；false-新增/更新（填充创建+更新时间）
     */
    private void setCommonPermissionFields(SysPermission permission, boolean isDelete) {
        Date now = new Date();
        permission.setUpdateTime(now);
        permission.setIsDelete(isDelete ? 1 : 0);
        if (!isDelete) {
            permission.setCreateTime(now);
        }
    }

    /**
     * DTO转换为权限-资源关联实体（修正资源ID字段拼写）
     */
    private SysPermissionResource convertToPermissionResource(SysPermissionResourceDto dto) {
        SysPermissionResource resource = new SysPermissionResource();
        resource.setId(dto.getId());
        resource.setPermissId(dto.getPermissId());
        resource.setResourceId(dto.getResoureId());
        return resource;
    }

    /**
     * 填充权限-资源关联通用字段（新增/更新）
     */
    private void setCommonResourceFields(SysPermissionResource resource) {
        Date now = new Date();
        resource.setCreateTime(now);
        resource.setUpdateTime(now);
        resource.setIsDelete(0);
    }

    /**
     * 填充权限-资源关联删除字段（仅更新时间+标记删除）
     */
    private void setCommonResourceFieldsForDeletion(SysPermissionResource resource) {
        resource.setUpdateTime(new Date());
        resource.setIsDelete(1);
    }

    /**
     * 权限分页查询
     */
    public PageResult<SysPermission> getPage(PageQuery<SysPermission> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysPermission query = pageQuery.getQueryData();
        List<SysPermission> list = sysPermissionDao.getPage(query);
        return new PageResult<>(list);
    }

    /**
     * 构建权限树形结构（递归查询子权限）
     */
    public List<Map<String, Object>> getPermissionTree(Long parentId) {
        List<SysPermission> permissions = getChildrenByParentId(parentId);
        return buildPermissionTree(permissions);
    }

    /**
     * 查询指定父节点的子权限
     */
    private List<SysPermission> getChildrenByParentId(Long parentId) {
        return (parentId == null)
                ? sysPermissionDao.findTopLevelPermissions() // 顶级节点（parentId为null/0）
                : sysPermissionDao.findChildrenByParentId(parentId);
    }

    /**
     * 递归构建权限树形结构
     */
    private List<Map<String, Object>> buildPermissionTree(List<SysPermission> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return Collections.emptyList();
        }

        return permissions.stream().map(permission -> {
            Map<String, Object> node = new LinkedHashMap<>();
            node.put("permission", permission);
            // 递归查询子节点
            node.put("children", buildPermissionTree(getChildrenByParentId(permission.getId())));
            return node;
        }).collect(Collectors.toList());
    }

    /**
     * 权限批量关联资源（先清除旧关联，再新增）
     */
    @Transactional
    public void assignResource(SysPermissionResourceDto dto) {
        if (dto.getPermissId() == null) {
            throw new IllegalArgumentException("权限ID（permissId）不能为空");
        }
        if (dto.getResourceIds() == null || dto.getResourceIds().isEmpty()) {
            throw new IllegalArgumentException("资源ID列表（resourceIds）不能为空");
        }

        // 清除旧关联（逻辑删除）
        sysPermissionResourceDao.batchDeleteByPermissId(dto.getPermissId());

        // 新增新关联
        List<SysPermissionResource> resources = dto.getResourceIds().stream()
                .map(resId -> {
                    SysPermissionResource res = new SysPermissionResource();
                    res.setPermissId(dto.getPermissId());
                    res.setResourceId(resId);
                    res.setIsDelete(0);
                    Date now = new Date();
                    res.setCreateTime(now);
                    res.setUpdateTime(now);
                    return res;
                })
                .collect(Collectors.toList());

        sysPermissionResourceDao.batchInsert(resources);
    }

    /**
     * 查询权限详情（校验有效性）
     */
    public SysPermission getPermissionInfo(Long permissionId) {
        if (permissionId == null) {
            throw new IllegalArgumentException("权限ID（permissionId）不能为空");
        }

        SysPermission permission = sysPermissionDao.selectByPrimaryKey(permissionId);
        if (permission == null) {
            throw new RuntimeException("权限不存在");
        }
        if (permission.getIsDelete() == 1) {
            throw new RuntimeException("权限已被删除");
        }

        return permission;
    }
}