<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmSupplyReqInfoDao">

    <sql id="Base_Column_List">
        id, busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, title, content, picture, video, scope, infoType, auditStatus,
        reason, createTime, updateTime, isDelete
    </sql>

    <!-- Insert -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmSupplyReqInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_supply_req_info (
        busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, title, content, picture, video, scope, infoType,
        auditStatus, reason, createTime, updateTime, isDelete
        ) VALUES (
        #{busNo}, #{busName}, #{linkman}, #{telPhone}, #{country}, #{province}, #{city}, #{area}, #{address},
        #{longitude}, #{latitude}, #{title}, #{content}, #{picture}, #{video}, #{scope}, #{infoType},
        #{auditStatus}, #{reason}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <!-- Batch Insert -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cm_supply_req_info (
        busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, title, content, picture, video, scope, infoType,
        auditStatus, reason, createTime, updateTime, isDelete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.busNo}, #{item.busName}, #{item.linkman}, #{item.telPhone}, #{item.country}, #{item.province},
            #{item.city}, #{item.area}, #{item.address}, #{item.longitude}, #{item.latitude}, #{item.title},
            #{item.content}, #{item.picture}, #{item.video}, #{item.scope}, #{item.infoType},
            #{item.auditStatus}, #{item.reason}, #{item.createTime}, #{item.updateTime}, #{item.isDelete}
            )
        </foreach>
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmSupplyReqInfo">
        UPDATE cm_supply_req_info
        <set>
            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="busName != null">busName = #{busName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="picture != null">picture = #{picture},</if>
            <if test="video != null">video = #{video},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="infoType != null">infoType = #{infoType},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- Update Audit Status -->
    <update id="updateAuditStatus">
        UPDATE cm_supply_req_info
        SET auditStatus = #{auditStatus},
        reason = #{reason},
        updateTime = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- Logical Delete -->
    <update id="delete">
        UPDATE cm_supply_req_info
        SET isDelete = 1,updateTime = NOW()
        WHERE id = #{id}
    </update>



    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmSupplyReqInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_info
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByCondition" parameterType="com.jmt.model.cm.dto.CmSupplyReqInfoDTO" resultType="com.jmt.model.cm.dto.CmSupplyReqInfoDTO">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_supply_req_info
        <where>
            isDelete = 0
            <if test="busNo != null and busNo != ''">AND busNo LIKE CONCAT('%', #{busNo}, '%')</if>
            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName}, '%')</if>
            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman}, '%')</if>
            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone}</if>
            <if test="country != null and country != ''">AND country = #{country}</if>
            <if test="province != null and province != ''">AND province = #{province}</if>
            <if test="city != null and city != ''">AND city = #{city}</if>
            <if test="area != null and area != ''">AND area = #{area}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address}, '%')</if>
            <if test="longitude != null and longitude != ''">AND longitude = #{longitude,jdbcType=VARCHAR}</if>
            <if test="latitude != null and latitude != ''">AND latitude = #{latitude,jdbcType=VARCHAR}</if>
            <if test="title != null and title != ''">AND title LIKE CONCAT('%', #{title}, '%')</if>
            <if test="scope != null">AND scope = #{scope}</if>
            <if test="infoType != null">AND infoType = #{infoType}</if>
            <if test="auditStatus != null">AND auditStatus = #{auditStatus}</if>
        </where>
        ORDER BY createTime
    </select>

</mapper>