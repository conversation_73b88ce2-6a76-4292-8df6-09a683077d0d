package com.jmt.model.eq.dto;

import lombok.Data;

import java.util.Date;

/**
 * 运营商库存变动记录表实体类
 * 对应表：eq_operator_stock_record
 */
@Data
public class EqOperatorStockRecordDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 运营商编号，关联 jhh_operator_info 的 operatorNo
     */
    private String operatorNo;
    /**
     * 记录类型，0-发货/1-退货
     */
    private Integer recordType;
    /**
     * 变动设备数量
     */
    private Integer eqRecordTotal;
    /**
     * A类数量变动
     */
    private Integer eqAnum;
    /**
     * B类数量变动
     */
    private Integer eqBnum;
    /**
     * C类数量变动
     */
    private Integer eqCnum;
    /**
     * 凭证图片
     */
    private String evidence;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除，0-否 1-是
     */
    private Integer isDelete;
}