package com.jmt.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jmt.dao.EqInfoDao;
import com.jmt.model.eq.dto.EqInfo;
import com.jmt.model.eq.dto.EqInfoImport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EqInfoImportListener extends AnalysisEventListener<EqInfoImport>{
    @Resource
    private EqInfoDao eqInfoDao;

    // 批处理阈值
    private static final int BATCH_SIZE = 1000;
    private List<EqInfoImport> dataList = new ArrayList<>();

    @Override
    public void invoke(EqInfoImport data, AnalysisContext context) {
        dataList.add(data);
        if (dataList.size() >= BATCH_SIZE) {
            saveData();
            dataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData(); // 处理剩余数据
        log.info("所有数据解析完成！");
    }
    private void saveData() {
        if (dataList.isEmpty()) return;

        // 提取设备码列表
        List<String> eqNoList = dataList.stream()
                .map(EqInfoImport::getEqNo)
                .collect(Collectors.toList());

        // 查询已存在的设备码
        List<String> existingEqNos = eqInfoDao.selectExistingEqNos(eqNoList);

        // 过滤新设备码并转换为实体
        List<EqInfo> newEqList = dataList.stream()
                .filter(item -> !existingEqNos.contains(item.getEqNo()))
                .map(item -> {
                    EqInfo eqInfo = new EqInfo();
                    eqInfo.setEqNo(item.getEqNo());
                    eqInfo.setEqState(0); // 初始状态：未激活
                    LocalDateTime now = LocalDateTime.now();
                    eqInfo.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
                    eqInfo.setIsDelete(0);
                    return eqInfo;
                })
                .collect(Collectors.toList());

        // 批量插入
        if (!newEqList.isEmpty()) {
            eqInfoDao.batchInsert(newEqList);
        }
    }
}