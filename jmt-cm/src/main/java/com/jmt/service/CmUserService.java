package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.convertor.CmUserConvertor;
import com.jmt.dao.CmUserDao;
import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.entity.CmWorkEqRecall;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class CmUserService extends BaseService {

    @Resource
    private CmUserDao cmUserDao;
    @Resource
    private CmUserConvertor cmUserConvertor;

    /**
     * 新增用户
     */
    @Transactional
    public CmUser createUser(CmUserDTO cmUserDTO) {
        CmUser cmUser = cmUserConvertor.toEntity(cmUserDTO);
        cmUser.setCreateTime(new Date());
        cmUser.setUpdateTime(new Date());
        cmUser.setIsFirstLogin(1);
        cmUser.setIsDelete(0);
        cmUser.setRoleType(0); //游客
        cmUserDao.insert(cmUser);
        return cmUser;
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public Integer updateByUaaId(CmUser cmUser) {
        cmUser.setUpdateTime(new Date());
        return cmUserDao.updateByUaaId(cmUser);
    }

    /**
     * 删除用户
     */
    @Transactional
    public int deleteUser(Long id) {
        CmUser cmUser = cmUserDao.selectById(id);
        if (cmUser == null) {
            throw new RuntimeException("用户不存在");
        }
        return cmUserDao.deleteById(id);
    }

    /**
     * 根据ID查询用户
     */
    @Transactional
    public CmUser getUserById(Long id) {
        return cmUserDao.selectById(id);
    }
    @Transactional
    public CmUser getUserByUaaId(Long uaaId) {
        return cmUserDao.selectByUaaId(uaaId);
    }

    @Transactional
    public int updateUserRoleType(Long userId, Integer roleType) {
        if (userId == null || roleType == null) {
            throw new IllegalArgumentException("用户ID和角色类型不能为空");
        }

        CmUser user = new CmUser();
        user.setId(userId);
        user.setRoleType(roleType);
        user.setUpdateTime(new Date());

        return cmUserDao.updateById(user);
    }

}
