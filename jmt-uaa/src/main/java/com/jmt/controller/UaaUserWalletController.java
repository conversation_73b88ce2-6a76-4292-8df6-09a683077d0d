package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.uaa.dto.ChangePayPasswordDTO;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.service.UaaUserWalletService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

@Slf4j
@RestController
@RequestMapping("/uaa/wallet")
public class UaaUserWalletController extends BaseController {
    @Resource
    private UaaUserWalletService uaaUserWalletService;


    /**
     * 修改
     * @param uaaUserWallet
     * @return
     */
    @PostMapping(value ="/v1/update")
    public String update(@RequestBody UaaUserWallet uaaUserWallet) {
        uaaUserWalletService.updateWallet(uaaUserWallet);
        return super.responseSuccess("修改成功");
    }

    /**
     * 更新余额接口（增加）
     * @param uaaId
     * @param amount
     * @return
     */
    @PostMapping("/v1/increaseBalance")
    public String increaseBalance(@RequestParam Long uaaId,
                                  @RequestParam BigDecimal amount) {
        try{
            uaaUserWalletService.updateBalance(uaaId, amount, true);
        }catch (Exception e){
            log.error("更新余额接口异常", e);
            return super.responseFail(e.getMessage());
        }
        return super.responseSuccess("余额增加成功");
    }

    /**
     * 更新余额接口（扣减）
     * @param uaaId
     * @param amount
     * @return
     */
    @PostMapping("/v1/decreaseBalance")
    public String decreaseBalance(@RequestParam Long uaaId,
                                  @RequestParam BigDecimal amount) {
        try{
            uaaUserWalletService.updateBalance(uaaId, amount, false);
        }catch (Exception e){
            log.error("更新余额接口异常", e);
            return super.responseFail(e.getMessage());
        }
        return super.responseSuccess("余额扣减成功");
    }

    /**
     * 更新积分接口（增加）
     * @param uaaId
     * @param points
     * @return
     */
    @PostMapping("/v1/increasePoints")
    public String increasePoints(@RequestParam Long uaaId,
                                 @RequestParam Integer points) {
        try{
            uaaUserWalletService.updatePoints(uaaId, points, true);
        }catch (Exception e){
            log.error("更新积分接口异常", e);
            return super.responseFail(e.getMessage());
        }
        return super.responseSuccess("积分增加成功");
    }

    /**
     * 更新积分接口（扣减）
     * @param uaaId
     * @param points
     * @return
     */
    @PostMapping("/v1/decreasePoints")
    public String decreasePoints(@RequestParam Long uaaId,
                                 @RequestParam Integer points) {
        try{
            uaaUserWalletService.updatePoints(uaaId, points, false);
        }catch (Exception e){
            log.error("更新积分接口异常", e);
            return super.responseFail(e.getMessage());
        }
        return super.responseSuccess("积分扣减成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        uaaUserWalletService.deleteWallet(id);
        return super.responseSuccess("删除成功");
    }

    /**
     * 根据uaaId查询
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/search")
    public String getByUaaId(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        UaaUserWallet wallet = uaaUserWalletService.getWalletById(loginUser.getUaaId());
        return super.responseSuccess(wallet,"查询成功");
    }

    @PostMapping("/v1/reset")
    public String changePayPassword(@RequestBody ChangePayPasswordDTO dto, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            uaaUserWalletService.changePayPassword(loginUser.getUaaId(),dto);
            return super.responseSuccess("密码修改成功");
        } catch (RuntimeException e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 密码校验
     * @param uaaId
     * @param payPassword
     * @return
     */
    @PostMapping("/v1/validatePayPassword")
    public Boolean validatePayPassword(@RequestParam("uaaId") Long uaaId,
                                @RequestParam("payPassword") String payPassword){
        return uaaUserWalletService.validatePayPassword(uaaId, payPassword);
    }

}
