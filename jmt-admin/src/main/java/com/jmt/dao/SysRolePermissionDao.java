package com.jmt.dao;

import com.jmt.model.admin.role.SysRolePermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_role_permission(系统角色权限关系表)】的数据库操作Mapper
 */
public interface SysRolePermissionDao {

    int deleteByPrimaryKey(Long id);

    int insert(SysRolePermission record);

    int insertSelective(SysRolePermission record);

    SysRolePermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysRolePermission record);

    int updateByPrimaryKey(SysRolePermission record);

    List<Long> findByRoleId(Long Id);

    /**
     * 逻辑删除角色的权限关联（更新isDelete=1）
     * @param roleId 角色ID
     * @param isDelete 删除标志（1-已删除）
     * @return 更新数量
     */
    int updateIsDeleteByRoleId(@Param("roleId") Long roleId, @Param("isDelete") Integer isDelete);

    /**
     * 批量插入角色权限关联
     * @param list 角色权限关联列表
     * @return 插入数量
     */
    int batchInsert(List<SysRolePermission> list);

    List<Long> findByRoleIds(@Param("roleIds") List<Long> roleIds);
}