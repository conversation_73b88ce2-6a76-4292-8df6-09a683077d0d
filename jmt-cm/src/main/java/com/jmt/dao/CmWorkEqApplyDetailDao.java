package com.jmt.dao;

import com.jmt.model.cm.entity.CmWorkEqApplyDetail;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmWorkEqApplyDetailDao {
    Integer insert(CmWorkEqApplyDetail record);
    Integer update(CmWorkEqApplyDetail record);
    Integer delete(Long id);
    Integer deleteByWorkNo(String workNo);
    CmWorkEqApplyDetail selectById(Long id);
    List<CmWorkEqApplyDetail> selectByWorkNo(String workNo);
    List<CmWorkEqApplyDetail> selectByBusNo(String busNo);
    List<CmWorkEqApplyDetail> selectByEqType(String eqType);
    List<CmWorkEqApplyDetail> selectAll();
    Integer batchInsert(List<CmWorkEqApplyDetail> list);
}
