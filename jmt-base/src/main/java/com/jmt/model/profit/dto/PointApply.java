package com.jmt.model.profit.dto;

import lombok.Data;

import java.util.Date;
@Data
public class PointApply {
    /**
     * 主键
     */
    private Long id;

    /**
     * 申请人，uaa_user的主键uaald
     */
    private Long applyUser;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 申请积分数
     */
    private Integer points;

    /**
     * 是否提供发票0-否1-是
     */
    private Integer invoiceProvide;

    /**
     * 发票文件
     */
    private String invoiceFile;

    /**
     * 审核状态0-待审核1-审核通过2-审核不通过3-已兑现
     */
    private String auditStatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除0-否1-是
     */
    private Integer isDelete;
}
