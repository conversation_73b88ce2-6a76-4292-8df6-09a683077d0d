package com.jmt.model.profit.dto;

import lombok.Data;

import java.util.Date;

@Data
public class PointApplyPageDto {

    /**
     * 申请人，uaa_user的主键uaald
     */
    private Long applyUser;

    /**
     * 申请积分数
     */
    private Integer points;

    /**
     * 是否提供发票0-否1-是
     */
    private Integer invoiceProvide;

    /**
     * 审核状态0-待审核1-审核通过2-审核不通过3-已兑现
     */
    private String auditStatus;

}
