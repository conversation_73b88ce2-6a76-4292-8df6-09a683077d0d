package com.jmt.service;
import cn.hutool.core.convert.Convert;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.client.EqFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.exception.BusinessException;
import com.jmt.dao.InvestorDao;
import com.jmt.dao.InvestorInfoAuditDao;
import com.jmt.dao.OperatorDao;
import com.jmt.dao.SalesmanDao;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.eq.dto.RequestSendEq;
import com.jmt.model.jhh.dto.*;
import com.jmt.model.jhh.vo.InvestorSelectVo;
import com.jmt.model.jhh.vo.InvestorVo;
import com.jmt.model.jhh.vo.JhhUserInfoVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JhhUserNoUtil;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class InvestorService {
    /**
     * 根据id获取投资人信息
     * @param id
     * @return
     */
    @Resource
    private InvestorDao investorDao;
    @Resource
    private InvestorInfoAuditDao investorInfoAuditDao;
    @Resource
    private EqFeignClient eqFeignClient;
    @Resource
    private UaaFeignClient uaaFeignClient;
    @Resource
    private OperatorDao operatorDao;
    @Resource
    private SalesmanDao salesmanDao;
    /******
     * 分配设备
     * @param requestSendEq
     * @return
     */
    @Transactional
    public void assignEq(RequestSendEq requestSendEq) {
        String investorNo = requestSendEq.getInvestorNo();
        InvestorInfo investorInfo = investorDao.getByInvestorNo(investorNo);
        if (investorInfo == null||investorInfo.getIsDelete() == 1){
            throw new BusinessException("设备收益人编号不存在");
        }
        if (investorInfo.getAuditStatus() != 1){
            throw new BusinessException("设备收益人不通过审核");
        }
        eqFeignClient.assign(requestSendEq);
    }

    public InvestorInfo getInfo(Long id) {
        return investorDao.getInfo(id);
    }
    /**
     * 分页查询投资人列表
     * @param
     * @return
     */
    public PageResult<InvestorVo> getPage(PageQuery<InvestorPageQueryDto> PageQuery) {
        PageHelper.startPage(PageQuery.getPageNo(), PageQuery.getPageSize());
        List<InvestorVo> page = investorDao.getPage(PageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 新增投资人
     * @param investorInfoDto
     * @return
     */
    public void add(InvestorInfoDto investorInfoDto){
        InvestorInfo byInvestorNo = investorDao.getByInvestorNo(investorInfoDto.getInvestorNo());

        if (investorInfoDto.getIsProfitLimit()!=null){
            if (investorInfoDto.getLimitAmount()==null){
                throw new BusinessException("请填写收益限制金额");
            }
            if (investorInfoDto.getLimitAmount()<=0){
                throw new BusinessException("收益限制金额必须大于0");
            }
            if (investorInfoDto.getIsProfitLimit()!=1&&investorInfoDto.getIsProfitLimit()!=0){
                throw new BusinessException("请选择是否限制收益");
            }
        }
        if (investorInfoDto.getIsProfitLimit()!=null){
            if (investorInfoDto.getLimitAmount()==null){
                throw new BusinessException("请填写收益限制金额");
            }
            if (investorInfoDto.getLimitAmount()<=0){
                throw new BusinessException("收益限制金额必须大于0");
            }
            if (investorInfoDto.getIsProfitLimit()!=1&&investorInfoDto.getIsProfitLimit()!=0){
                throw new BusinessException("请选择是否限制收益");
            }
        }
        String prefix = "IN";
        String investorNo = JhhUserNoUtil.generate(prefix);
        InvestorInfo investorInfo = new InvestorInfo();
        BeanUtils.copyProperties(investorInfoDto,investorInfo);
        LocalDateTime now = LocalDateTime.now();
        investorInfo.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        investorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        investorInfo.setIsDelete(0);
        investorInfo.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        investorInfo.setInvestorNo(investorNo);
        investorInfo.setIsFirstLogin(1);
        investorDao.add(investorInfo);
    }
    /**
     * 修改收益人
     * @param investorInfoDto
     * @return
     */
    public void update(InvestorInfoDto investorInfoDto) {
        if (investorInfoDto == null){
            throw new BusinessException("参数错误");
        }
        InvestorInfo info = investorDao.getInfo(investorInfoDto.getId());
        if (info == null||info.getIsDelete() == 1){
            throw new RuntimeException("该用户不存在");
        }
        InvestorInfo investorInfo = new InvestorInfo();
        BeanUtils.copyProperties(investorInfoDto,investorInfo);
        LocalDateTime now = LocalDateTime.now();
        investorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        investorInfo.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        investorInfo.setIsDelete(0);
        investorInfo.setIsFirstLogin(0);
        investorDao.update(investorInfo);
        investorInfoAuditDao.delete(investorInfo.getInvestorNo());
    }
    /**
     * 删除投资人
     * @param id
     * @return
     */
    public void delete(Long id) {
        InvestorInfo info = investorDao.getInfo(id);
        if (info == null){
            throw new RuntimeException("该用户不存在");
        }
        investorDao.delete(id);
        investorInfoAuditDao.delete(info.getInvestorNo());
    }
    /**
     * 审核
     * @param investorInfoAuditDto
     */
    public void audit(InvestorAuditDto investorInfoAuditDto, HttpServletRequest request) {
        Long id = investorInfoAuditDto.getId();
        Integer auditStatus = investorInfoAuditDto.getAuditStatus();
        String reason = investorInfoAuditDto.getReason();
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long auditUser = loginUaaUser.getUaaId();
        InvestorInfo investorInfo = investorDao.getInfo(id);
        if (investorInfo == null || investorInfo.getInvestorNo() == null) {
            throw new BusinessException("未找到要审批的对象信息");
        }
        if (auditStatus != AuditStatusEnum.APPROVED.getCode() && auditStatus != AuditStatusEnum.REJECTED.getCode()) {
            throw new BusinessException("审核状态错误");
        }
        investorInfo.setId(id);
        investorInfo.setAuditStatus(auditStatus);
        investorInfo.setReason(reason);
        LocalDateTime now = LocalDateTime.now();
        investorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        UaaUserDTO uaaUserDTO = new UaaUserDTO();
        uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
        uaaUserDTO.setLoginName(investorInfo.getTelPhone());
        uaaUserDTO.setTelPhone(investorInfo.getTelPhone());
        if (auditStatus == AuditStatusEnum.APPROVED.getCode()){
            String response = uaaFeignClient.registerUser(uaaUserDTO);
            Gson gson = new Gson();
            JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
            JsonElement dataElement = jsonObject.get("data");
            UaaUser user = gson.fromJson(dataElement, UaaUser.class);
            Long uaaId = user.getUaaId();
            investorInfo.setUaaId(uaaId);
            System.out.println(investorInfo);
        }
        investorDao.update(investorInfo);
        InvestorInfoAudit investorInfoAudit = new InvestorInfoAudit();
        investorInfoAudit.setAuditStatus(auditStatus);
        investorInfoAudit.setReason(reason);
        investorInfoAudit.setAuditUser(auditUser);
        investorInfoAudit.setInvestorNo(investorInfo.getInvestorNo());
        investorInfoAudit.setIsDelete(0);
        investorInfoAudit.setCreateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        investorInfoAudit.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        List<InvestorInfoAudit> investorInfoAudits = investorInfoAuditDao.getInfoByInvestorNo(investorInfo.getInvestorNo());
        if (investorInfoAudits != null && investorInfoAudits.size() > 0) {
            log.info("有多个相同编号的审核信息");
            for (InvestorInfoAudit investorInfoAudit1 : investorInfoAudits){
                if (investorInfoAudit1.getAuditUser().equals(auditUser)){
                    log.info("有i相同uuid的记录");
                    investorInfoAudit.setId(investorInfoAudit1.getId());
                    investorInfoAuditDao.update(investorInfoAudit);
                }else {
                    log.info("无i相同uuid的记录");
                    investorInfoAuditDao.add(investorInfoAudit);
                }
            }
        }else{
            log.info("首次审核");
            investorInfoAuditDao.add(investorInfoAudit);
        }
    }
    /**
     * 获取操作员下拉列表
     *
     * @return
     */
    public List<InvestorSelectVo> getOperatorSelect() {
        return investorDao.getInvestorSelect();
    }
    /**
     * 冻结
     * @param investorInfoAuditDto
     * @param request
     * @return
     */
    public void freeze(InvestorAuditDto investorInfoAuditDto, HttpServletRequest request) {
        Long id = investorInfoAuditDto.getId();
        Integer auditStatus = investorInfoAuditDto.getAuditStatus();
        String reason = investorInfoAuditDto.getReason();
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        Long auditUser = loginUaaUser.getUaaId();
        InvestorInfo investorInfo = investorDao.getInfo(id);
        System.out.println(investorInfo);
        log.info("investorInfo:{}", investorInfo);
        if (investorInfo == null || investorInfo.getInvestorNo() == null) {
            throw new BusinessException("未找到要审批的对象信息");
        }
        investorInfo.setId(id);
        investorInfo.setAuditStatus(3);
        investorInfo.setReason(reason);
        LocalDateTime now = LocalDateTime.now();
        investorInfo.setUpdateTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        investorDao.update(investorInfo);
        InvestorInfoAudit investorInfoAudit = new InvestorInfoAudit();
        investorInfoAudit.setAuditStatus(AuditStatusEnum.COMPLETED.getCode());
        investorInfoAudit.setReason(reason);
        investorInfoAudit.setAuditUser(auditUser);
        investorInfoAudit.setInvestorNo(investorInfo.getInvestorNo());
        investorInfoAudit.setIsDelete(0);
        List<InvestorInfoAudit> investorInfoAudits = investorInfoAuditDao.getInfoByInvestorNo(investorInfo.getInvestorNo());
        System.out.println(investorInfoAudits);
        if (investorInfoAudits != null && investorInfoAudits.size() > 0) {
            log.info("有多个相同编号的审核信息");
            for (InvestorInfoAudit investorInfoAudit1 : investorInfoAudits){
                if (investorInfoAudit1.getAuditUser().equals(auditUser)){
                    log.info("有i相同uuid的记录");
                    investorInfoAudit.setId(investorInfoAudit1.getId());
                    investorInfoAuditDao.update(investorInfoAudit);
                }else {
                    log.info("无i相同uuid的记录");
                    investorInfoAuditDao.add(investorInfoAudit);
                }
            }
        }else{
            log.info("首次审核");
            investorInfoAuditDao.add(investorInfoAudit);
        }
    }

    public JhhUserInfoVo getUserInfo(HttpServletRequest request) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get(request);
        String clientId = loginUaaUser.getClientId();
        Integer userType = loginUaaUser.getUserType();
        Long uaaId = loginUaaUser.getUaaId();
        JhhUserInfoVo jhhUserInfoVo = new JhhUserInfoVo();
        if (!clientId.equals("jmt-jhh")){
            throw new BusinessException("客户端id不正确");
        }
        switch (userType){
            case 0:
                OperatorInfo operatorInfo = operatorDao.getByUaaId(uaaId);
                BeanUtils.copyProperties(operatorInfo,jhhUserInfoVo);
                jhhUserInfoVo.setUserName(operatorInfo.getOperatorName());
                jhhUserInfoVo.setUserNo(operatorInfo.getOperatorNo());
                break;
            case 1:
                InvestorInfo investorInfo = investorDao.getByUaaId(uaaId);
                BeanUtils.copyProperties(investorInfo,jhhUserInfoVo);
                jhhUserInfoVo.setUserName(investorInfo.getInvestorName());
                jhhUserInfoVo.setUserNo(investorInfo.getInvestorNo());
                break;
            case 2:
                SalesmanInfo salesmanInfo = salesmanDao.getByUaaId(uaaId);
                BeanUtils.copyProperties(salesmanInfo,jhhUserInfoVo);
                jhhUserInfoVo.setUserName(salesmanInfo.getSalesmanName());
                jhhUserInfoVo.setUserNo(salesmanInfo.getSalesmanNo());
                break;
            default:
                throw new BusinessException("用户类型不存在");
        }
        return jhhUserInfoVo;
    }
    /**
     * 根据uaaId获取投资信息
     * @param uaaId
     * @return
     */
    public InvestorInfo getInvestorInfoByUaaId(Long uaaId) {
        return investorDao.getByUaaId(uaaId);
    }

    public JhhUserInfoVo getUserInfoByInvestor(String investorNo) {
        InvestorInfo byInvestorNo = investorDao.getByInvestorNo(investorNo);
        JhhUserInfoVo jhhUserInfoVo = new JhhUserInfoVo();

        BeanUtils.copyProperties(byInvestorNo,jhhUserInfoVo);
        return jhhUserInfoVo;
    }
}
