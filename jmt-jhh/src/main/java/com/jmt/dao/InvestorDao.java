package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.jhh.dto.InvestorInfo;
import com.jmt.model.jhh.dto.InvestorInfoDto;
import com.jmt.model.jhh.dto.InvestorPageQueryDto;
import com.jmt.model.jhh.vo.InvestorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvestorDao {
    /**
     * 根据id获取投资人信息
     * @param id
     * @return
     */

    InvestorInfo getInfo(Long id);
    /**
     * 分页查询投资人列表
     * @param queryData
     * @return
     */
    List<InvestorVo> getPage(@Param("queryData") InvestorPageQueryDto queryData);
    /**
     * 新增投资人信息
     * @param investorInfo
     */
    void add(InvestorInfo investorInfo);

    /**
     * 修改投资人信息
     * @param investorInfo
     */
    void update(InvestorInfo investorInfo);
    /**
     * 删除投资人信息
     * @param id
     */
    void delete(Long id);
    /**
     * 根据投资人编号查询
     * @param investorNo
     * @return
     */
    InvestorInfo getByInvestorNo(String investorNo);
    /**
     * 获取下拉框数据
     *
     * @return
     */
    List<InvestorVo> getInvestorSelect();
    /**
     * 根据uaaId查询
     * @param uaaId
     * @return
     */
    InvestorInfo getByUaaId(Long uaaId);
}
