<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysPermissionDao">
    <resultMap id="BaseResultMap" type="com.jmt.model.admin.permission.SysPermission">
        <id property="id" column="id" />
        <result property="permissKey" column="permissKey" />
        <result property="permissName" column="permissName" />
        <result property="parentId" column="parentId" />
        <result property="permissType" column="permissType" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>
    <sql id="Base_Column_List">
        id, permissKey, permissName, parentId, permissType, createTime, updateTime, isDelete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from sys_permission where id = #{id}
    </select>
    <select id="findByPermissionId" resultType="com.jmt.model.admin.permission.SysPermission" parameterType="java.lang.Long">
        select <include refid="Base_Column_List" /> from sys_permission as sps where sps.id = #{id}
    </select>
    <select id="findByPermissionIds" resultType="com.jmt.model.admin.permission.SysPermission">
        SELECT <include refid="Base_Column_List" /> FROM sys_permission WHERE isDelete = 0 and id IN <foreach collection="permissionIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>
    <select id="getPage" resultType="com.jmt.model.admin.permission.SysPermission" parameterType="com.jmt.model.admin.permission.SysPermission">
        SELECT <include refid="Base_Column_List" /> FROM sys_permission t
        <where>
            <if test="id != null">AND t.id = #{id}</if>
            <if test="permissKey != null and permissKey != ''">AND t.permissKey = #{permissKey}</if>
            <if test="permissName != null and permissName != ''">AND t.permissName = #{permissName}</if>
            <if test="parentId != null">AND t.parentId = #{parentId}</if>
            <if test="permissType != null">AND t.permissType = #{permissType}</if>
            <if test="createTime != null">AND t.createTime &gt;= #{createTime}</if>
            <if test="updateTime != null">AND t.updateTime &lt;= #{updateTime}</if>
            <if test="isDelete != null">AND t.isDelete = #{isDelete}</if>
        </where>
        ORDER BY t.createTime DESC
    </select>
    <select id="findAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM sys_permission WHERE isDelete = 0 ORDER BY parentId ASC, id ASC
    </select>
    <select id="findTopLevelPermissions" resultType="com.jmt.model.admin.permission.SysPermission">
        SELECT * FROM sys_permission WHERE (parentId IS NULL OR parentId = 0) AND isDelete = 0 ORDER BY id ASC
    </select>
    <select id="findChildrenByParentId" resultType="com.jmt.model.admin.permission.SysPermission">
        SELECT * FROM sys_permission WHERE parentId = #{parentId} AND isDelete = 0 ORDER BY id ASC
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update sys_permission set isDelete = 1, updateTime = NOW() where id = #{id} and isDelete = 0
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.permission.SysPermission" useGeneratedKeys="true">
        insert into sys_permission (id, permissKey, permissName, parentId, permissType, createTime, updateTime, isDelete) values (#{id}, #{permissKey}, #{permissName}, #{parentId}, #{permissType}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.permission.SysPermission" useGeneratedKeys="true">
        insert into sys_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="permissKey != null">permissKey,</if>
            <if test="permissName != null">permissName,</if>
            <if test="parentId != null">parentId,</if>
            <if test="permissType != null">permissType,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="permissKey != null">#{permissKey},</if>
            <if test="permissName != null">#{permissName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="permissType != null">#{permissType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.permission.SysPermission">
        update sys_permission
        <set>
            <if test="permissKey != null">permissKey = #{permissKey},</if>
            <if test="permissName != null">permissName = #{permissName},</if>
            <if test="parentId != null">parentId = #{parentId},</if>
            <if test="permissType != null">permissType = #{permissType},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.permission.SysPermission">
        update sys_permission set permissKey = #{permissKey}, permissName = #{permissName}, parentId = #{parentId}, permissType = #{permissType}, createTime = #{createTime}, updateTime = #{updateTime}, isDelete = #{isDelete} where id = #{id}
    </update>
</mapper>