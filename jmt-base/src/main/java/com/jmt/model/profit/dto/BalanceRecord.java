package com.jmt.model.profit.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BalanceRecord {

    /**
     * 主键
     */
    private Long id;

    /**
     * 余额归属人，uaa_user的主键uaaId
     */
    private Long uaaId;

    /**
     * 变动余额
     */
    @NotNull(message = "变动余额不能为空")
    @DecimalMin(value = "0.01", message = "变动余额必须大于等于0.01")
    @Digits(integer = 10, fraction = 2, message = "变动余额格式不正确，整数部分最多10位，小数部分最多2位")
    private BigDecimal balance;

    /**
     * 记录类型0-收入1-支出
     */
    @NotNull(message = "记录类型不能为空")
    @Pattern(regexp = "[01]", message = "记录类型只能是0(收入)或1(支出)")
    private Integer recordType;

    /**
     * 余额来源recordType=0时填写0-充值1-积分兑现
     */
    @Pattern(regexp = "[01]", message = "余额来源只能是0(充值)或1(积分兑现)")
    private Integer balanceSource;

    /**
     * 填写充值订单号或积分兑现申请单号
     */
    private String sourceId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    @NotNull(message = "删除状态不能为空")
    @Pattern(regexp = "[01]", message = "删除状态只能是0(否)或1(是)")
    private Integer isDelete;
}
