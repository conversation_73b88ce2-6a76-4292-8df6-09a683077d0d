package com.jmt.model.profit.dto;

import lombok.Data;

import java.util.Date;
@Data
public class PointsRecord {

    /**
     * 主键
     */
    private Long id;

    /**
     * 积分归属人，uaa_user的主键uaald
     */
    private Long uaaId;

    /**
     * 变动积分数
     */
    private Integer points;

    /**
     * 记录类型0-收入1-支出
     */
    private Integer recordType;

    /**
     * recordType=0时填写0-分润1-任务
     */
    private Integer pointSource;

    /**
     * 填写分润id或任务id
     */
    private String sourceId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
