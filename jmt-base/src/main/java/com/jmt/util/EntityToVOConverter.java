package com.jmt.util;


import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.admin.permission.SysPermissionVO;

import com.jmt.model.admin.resource.SysResource;
import com.jmt.model.admin.resource.SysResourceVO;
import com.jmt.model.admin.role.SysRole;
import com.jmt.model.admin.role.SysRoleVO;
import com.jmt.model.admin.user.SysUser;
import com.jmt.model.admin.user.SysUserVO;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实体类到VO的转换工具类
 */
public class EntityToVOConverter {

    /**
     * 将SysUser实体转换为SysUserVO
     */
    public static SysUserVO convertToUserVO(SysUser user) {
        if (user == null) {
            return null;
        }
        SysUserVO vo = new SysUserVO();
        BeanUtils.copyProperties(user, vo);
        return vo;
    }


    /**
     * 将SysPermission实体转换为SysPermissionVO
     */
    public static SysPermissionVO convertToPermissionVO(SysPermission permission) {
        if (permission == null) {
            return null;
        }
        SysPermissionVO vo = new SysPermissionVO();
        BeanUtils.copyProperties(permission, vo);
        return vo;
    }

    /**
     * 将SysRole实体转换为SysRoleVO
     */
    public static SysRoleVO convertToRoleVO(SysRole role) {
        if (role == null) {
            return null;
        }
        SysRoleVO vo = new SysRoleVO();
        BeanUtils.copyProperties(role, vo);
        return vo;
    }


    /**
     * 将SysResource实体转换为SysResourceVO
     */
    public static SysResourceVO convertToResourceVO(SysResource resource) {
        if (resource == null) {
            return null;
        }
        SysResourceVO vo = new SysResourceVO();
        BeanUtils.copyProperties(resource, vo);
        return vo;
    }


    // 集合转换工具方法

    /**
     * 将SysUser集合转换为SysUserVO集合
     */
    public static List<SysUserVO> convertToUserVOList(List<SysUser> users) {
        if (users == null) {
            return Collections.emptyList();
        }
        return users.stream()
                .map(EntityToVOConverter::convertToUserVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 将SysRole集合转换为SysRoleVO集合
     */
    public static List<SysRoleVO> convertToRoleVOList(List<SysRole> roles) {
        if (roles == null) {
            return Collections.emptyList();
        }
        return roles.stream()
                .map(EntityToVOConverter::convertToRoleVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    /**
     * 将Set<SysRole>集合转换为Set<SysRoleVO>集合
     */
    public static Set<SysRoleVO> convertToRoleVOSet(Set<SysRole> roles) {
        if (roles == null) {
            return Collections.emptySet();
        }
        return roles.stream()
                .map(EntityToVOConverter::convertToRoleVO)
                .collect(Collectors.toSet());
    }

    /**
     * 将Set<SysPermission>集合转换为Set<SysPermissionVO>集合
     */
    public static Set<SysPermissionVO> convertToPermissionVOSet(Set<SysPermission> permissions) {
        if (permissions == null) {
            return Collections.emptySet();
        }
        return permissions.stream()
                .map(EntityToVOConverter::convertToPermissionVO)
                .collect(Collectors.toSet());
    }

    /**
     * 将Set<SysResource>集合转换为Set<SysResourceVO>集合
     */
    public static Set<SysResourceVO> convertToResourceVOSet(Set<SysResource> resources) {
        if (resources == null) {
            return Collections.emptySet();
        }
        return resources.stream()
                .map(EntityToVOConverter::convertToResourceVO)
                .collect(Collectors.toSet());
    }
}
