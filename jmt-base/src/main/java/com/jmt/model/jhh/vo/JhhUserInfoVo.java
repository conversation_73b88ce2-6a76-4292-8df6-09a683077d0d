package com.jmt.model.jhh.vo;

import lombok.Data;

import java.util.Date;
@Data
public class JhhUserInfoVo {
    //收益人id
    private Long id;
    //uaaid
    private Long uaaId;
    //用户名称
    private String userName;
    //用户编号
    private String userNo;
    //头像
    private String headImg;
    //合约编号
    private String contractNo;
    //职位名称
    private String dutyName;
    //手机号码
    private String telPhone;
    //是否限制收益
    private Integer isProfitLimit;
    //限额
    private Integer limitAmount;
    //合约图片
    private String contractPicUrl;
    //证件图片
    private String licensePicUrl;
    //审核状态
    private Integer auditStatus;
    //更新时间
    private String reason;
}
