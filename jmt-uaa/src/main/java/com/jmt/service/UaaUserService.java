package com.jmt.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.util.StringUtils;
import com.jmt.base.BaseService;
import com.jmt.constant.JmtConstant;
import com.jmt.convertor.UaaUserConvertor;
import com.jmt.dao.UaaUserDao;
import com.jmt.model.uaa.dto.ChangePasswordDTO;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.uaa.UaaUser;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class UaaUserService extends BaseService {
    @Resource
    private UaaUserDao uaaUserDao;

    @Resource
    private UaaUserConvertor uaaUserConvertor;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Transactional
    public UaaUser registerUser(UaaUserDTO userDTO) {
        if (uaaUserDao.existsByLoginName(userDTO.getLoginName())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查手机号是否已注册
        if (userDTO.getTelPhone() != null) {
            UaaUser existingUser = uaaUserDao.existsByTelphone(userDTO.getTelPhone());
            if (existingUser != null) {
                return existingUser;
            }
        }

        com.jmt.model.uaa.UaaUser user = uaaUserConvertor.toEntity(userDTO);
        user.setPassword(passwordEncoder.encode(userDTO.getLoginName() + userDTO.getPassword()));
        String nickName = userDTO.getLoginName();
        if (StrUtil.isEmpty(nickName)) {
            nickName = JmtConstant.DEFAULT_NICKNAME + userDTO.getTelPhone();
        }
        user.setNickname(nickName);
        user.setActivated(0);
        user.setIsDelete(0);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());

        uaaUserDao.insert(user);
        return user;
    }


    @Transactional
    public Integer update(UaaUser uaaUser) {
        uaaUser.setUpdateTime(new Date());
        return uaaUserDao.updateById(uaaUser);
    }

    /**
     * 修改密码
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(ChangePasswordDTO dto) {
        UaaUser user = uaaUserDao.selectByUaaId(dto.getUaaId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 原密码是否正确
        if (StringUtils.isEmpty(user.getPassword())
                || !user.getPassword().equals(passwordEncoder.encode(user.getLoginName() + dto.getOldPassword()))) {
            throw new RuntimeException("原密码不正确");
        }

        // 新密码不能与旧密码相同
        if (user.getPassword().equals(passwordEncoder.encode(user.getLoginName() + dto.getNewPassword()))) {
            throw new RuntimeException("新密码不能与旧密码相同");
        }

        String encodedPassword = passwordEncoder.encode(user.getLoginName() + dto.getNewPassword());
        user.setPassword(encodedPassword);
        user.setUpdateTime(new Date());

        int updated = uaaUserDao.updatePassword(user);
        if (updated <= 0) {
            throw new RuntimeException("密码更新失败");
        }

    }
}
