package com.jmt.model.profit.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class BalanceRechargeOrder {
    /**
     * 主键
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单名称
     */
    @Size(max = 255, message = "订单名称长度不能超过255个字符")
    private String orderName;

    /**
     * 充值人，uaa_user的uaald
     */
    @NotNull(message = "充值人ID不能为空")
    private Long rechargeUser;

    /**
     * 支付方式:0-积分支付1-余额支付2-微信支付3-支付宝
     */
    @NotNull(message = "支付方式不能为空")
    @Pattern(regexp = "^[0123]$", message = "支付方式只能是0、1、2、3")
    private Integer payType;

    /**
     * 充值金额
     */
    private BigDecimal payAmount;

    /**
     * 订单状态:0-未支付1-支付成功2-支付失败3-取消支付
     */
    @NotNull(message = "订单状态不能为空")
    private Integer orderStatus;

    /**
     * 驳回必填
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
