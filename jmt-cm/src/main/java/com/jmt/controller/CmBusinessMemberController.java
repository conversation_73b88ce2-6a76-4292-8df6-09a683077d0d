package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.cm.dto.CmBusinessMemberDTO;
import com.jmt.model.cm.vo.CmBusinessMemberVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.CmBusinessMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cm/businessMember")
public class CmBusinessMemberController extends BaseController {
    @Resource
    private CmBusinessMemberService cmBusinessMemberService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @GetMapping("/v1/getPageList")
    public String getPageList(@RequestBody PageQuery<CmBusinessMemberDTO> pageQuery){
        PageResult<CmBusinessMemberDTO> pageResult = cmBusinessMemberService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 新增
     * @param cmBusinessMemberDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmBusinessMemberDTO cmBusinessMemberDTO) {
        try {
            cmBusinessMemberService.create(cmBusinessMemberDTO);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增团队成员失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmBusinessMemberDTO dto) {
        try {
            cmBusinessMemberService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmBusinessMemberService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmBusinessMemberVO cmBusinessMemberVO = cmBusinessMemberService.getById(id);
        if(cmBusinessMemberVO == null){
            return super.responseFail("不存在该成员");
        }
        return super.responseSuccess(cmBusinessMemberVO,"查询成功");
    }

    @ResponseBody
    @GetMapping("/v1/busNo/{busNo}")
    public String getByBusNo(@PathVariable String busNo) {
        List<CmBusinessMemberVO> cmBusinessMemberVO = cmBusinessMemberService.getByBusNo(busNo);
        if(cmBusinessMemberVO == null){
            return super.responseFail("不存在成员");
        }
        return super.responseSuccess(cmBusinessMemberVO,"查询成功");
    }


    /**
     * 商家启用/禁用团队成员
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusinessMem(@PathVariable Long id,
                                @RequestParam Integer auditStatus) {
        try {
            cmBusinessMemberService.auditBusinessMem(id, auditStatus);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }




}
