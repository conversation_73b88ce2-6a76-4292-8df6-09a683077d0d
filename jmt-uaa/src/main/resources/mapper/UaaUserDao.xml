<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.UaaUserDao">

	<!-- 根据主键查询用户 -->
	<select id="selectByUaaId" resultType="com.jmt.model.uaa.UaaUser" parameterType="java.lang.Long">
		SELECT
			uaaId,
			nickname,
			loginName,
			password,
			activated,
			email,
			sex,
			photo,
			telPhone,
			address,
			country,
			province,
			city,
			area,
			isRealAuth,
			unionId,
			createTime,
			updateTime,
			isDelete
		FROM
			uaa_user
		WHERE
			uaaId = #{uaaId,jdbcType=BIGINT}
		  AND isDelete = 0
	</select>

	<insert id="insert" parameterType="com.jmt.model.uaa.UaaUser" useGeneratedKeys="true" keyProperty="uaaId">
		INSERT INTO uaa_user (
			nickname, loginName, password,
			activated, email, sex, photo,
			telPhone, address, country,
			province, city, area,isRealAuth,
			unionId, createTime, updateTime,
			isDelete
		) VALUES (
					 #{nickname}, #{loginName}, #{password},
					 #{activated}, #{email}, #{sex}, #{photo},
					 #{telPhone}, #{address}, #{country},
					 #{province}, #{city}, #{area},#{isRealAuth},
					 #{unionId}, #{createTime}, #{updateTime},
					 #{isDelete}
				 )
	</insert>

	<select id="existsByLoginName" resultType="boolean">
		SELECT COUNT(1) FROM uaa_user WHERE loginName = #{loginName} AND isDelete = 0
	</select>

	<select id="existsByTelphone" resultType="com.jmt.model.uaa.UaaUser">
		SELECT uaaId,loginName,telPhone
		FROM uaa_user WHERE telPhone = #{telPhone} AND isDelete = 0
	</select>

	<!-- 更新用户信息 -->
	<update id="updateById" parameterType="com.jmt.model.uaa.UaaUser">
		UPDATE uaa_user
		<set>
			<if test="nickname != null">nickname = #{nickname},</if>
			<if test="email != null">email = #{email},</if>
			<if test="sex != null">sex = #{sex},</if>
			<if test="photo != null">photo = #{photo},</if>
			<if test="telPhone != null">telPhone = #{telPhone},</if>
			<if test="address != null">address = #{address},</if>
			<if test="country != null">country = #{country},</if>
			<if test="province != null">province = #{province},</if>
			<if test="city != null">city = #{city},</if>
			<if test="area != null">area = #{area},</if>
			<if test="isRealAuth != null">isRealAuth = #{isRealAuth},</if>
			<if test="unionId != null">unionId = #{unionId},</if>
			updateTime = #{updateTime},
		</set>
		WHERE uaaId = #{uaaId}
	</update>

	<!-- 修改用户密码 -->
	<update id="updatePassword" parameterType="com.jmt.model.uaa.UaaUser">
		UPDATE uaa_user
		<set>
			<if test="password != null">password = #{password},</if>
			updateTime = #{updateTime},
		</set>
		WHERE uaaId = #{uaaId}
	</update>
</mapper>