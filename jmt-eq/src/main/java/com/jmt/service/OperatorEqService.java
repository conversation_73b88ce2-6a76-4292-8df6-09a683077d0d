package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.OperatorEqDao;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.dto.RequestTrans;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OperatorEqService {
    @Resource
    private OperatorEqDao operatorEqDao;
    public PageResult<EqBusinessSetupVo> getPage(PageQuery<EqOperatorStockDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqBusinessSetupVo> page = operatorEqDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }

    public EqOperatorStockVo getInfo(Integer id) {
        return operatorEqDao.getInfo(id);
    }
    /**
     * 转移
     * @param requestTrans
     */
    public void transferStock(RequestTrans requestTrans) {
        operatorEqDao.transferStock(requestTrans.getSourceOperatorNo(), requestTrans.getTargetOperatorNo());
    }
    /**
     * 清空操作员设备
     */
    public void clearOperatorEq(String operatorNo) {
        operatorEqDao.clearOperatorEq(operatorNo);
    }


}
