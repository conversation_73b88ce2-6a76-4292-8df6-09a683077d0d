package com.jmt.dao;

import com.jmt.model.profit.ProfitSharePostdivide;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【profit_share_postdivide(收益分润表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitSharePostdivide
*/
public interface ProfitSharePostdivideMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitSharePostdivide record);

    int insertSelective(ProfitSharePostdivide record);

    ProfitSharePostdivide selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitSharePostdivide record);

    int updateByPrimaryKey(ProfitSharePostdivide record);
    List<ProfitSharePostdivide> selectByShareStatus(int status);
}
