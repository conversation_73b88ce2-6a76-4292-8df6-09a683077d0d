package com.jmt.model.cm.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 筷圣餐谋商家设备部署工单表
* @TableName cm_work_eq_deploy
*/
@Data
public class CmWorkEqDeploy implements Serializable {

    /**
    * 
    */
    @NotNull(message="[]不能为空")
    private Long id;
    /**
    * 部署工单编号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String workNo;
    /**
    * 部署商家编号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busNo;
    /**
    * 部署商家名称
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busName;
    /**
    * 联系人
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String linkman;
    /**
    * 联系人手机号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String telPhone;
    /**
    * 国家
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String country;
    /**
    * 省份
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String province;
    /**
    * 城市
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String city;
    /**
    * 区县
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String area;
    /**
    * 详细地址
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String address;
    /**
    * 经度
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String longitude;
    /**
    * 维度
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String latitude;
    /**
    * 运营商编号
jhh_operator_info的operatorNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String operatorNo;
    /**
    * 推荐业务编号
jhh_salesman_info的salesmanNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String refereeNo;
    /**
    * 部署业务编号
jhh_salesman_info的salesmanNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String deployerNo;
    /**
    * 申请工单编号
c_work_eq_apply的workNo
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String applyWorkNo;
    /**
    * 工单状态
0-待审核
1-同意
2-驳回
3-完成
    */
    private Integer workStatus;
    /**
     * 驳回必填
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String reason;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;



}
