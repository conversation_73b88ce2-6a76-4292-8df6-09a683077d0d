package com.jmt.controller;

import com.google.zxing.WriterException;
import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.TicketOrderDetail;
import com.jmt.model.zg.ZgProductOrder;
import com.jmt.model.zg.ZgProductPreOrder;
import com.jmt.service.ProductOrderService;
import com.jmt.service.ProductPreOrderService;
import com.jmt.service.TicketOrderService;
import com.jmt.service.TicketService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 商品订单管理控制器
 * 提供商品订单的创建、查询、取消、退款、核销以及囤货券订单管理等功能
 */
@RestController
@RequestMapping("/zg/order/v1")
public class ProductOrderController extends BaseController {

    @Resource
    private ProductOrderService productOrderService;
    @Resource
    private TicketOrderService ticketOrderService;
    @Resource
    private ProductPreOrderService productPreOrderService;
    @Resource
    private TicketService ticketService;

    /**
     * 使用囤货券生成商品订单
     * @param ticketNo 囤货券使用请求，包含券ID、商品信息、用户信息等
     * @return 生成的商品订单信息
     */
    @PostMapping("/useTicketAndCreateOrder")
    public String useTicketAndCreateOrder(@RequestParam String ticketNo) {
        return super.responseSuccess(ticketService.useTicketAndCreateProductOrder(ticketNo),"创建成功");
    }

    /**
     * 分页查询囤货券订单信息
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 囤货券订单分页数据
     */
    @GetMapping("/ticketOrderPage")
    public String getTicketOrderPage(@RequestBody PageQuery<TicketOrderDetail> pageQuery) {
        return super.responseSuccess(ticketOrderService.getTicketInfoPage(pageQuery), "查询成功");
    }

    /**
     * 取消商品订单
     * @param orderNo 订单编号
     * @return 操作结果（成功/失败）
     */
    @PostMapping("/cancelProductOrder")
    public String cancelProductOrder(@RequestParam String orderNo) {
        boolean result = productOrderService.cancelProductOrderByOrderNo(orderNo);
        return result ? super.responseSuccess("订单取消成功") : super.responseFail("订单取消失败");
    }

    /**
     * 商品订单退款
     * @param orderNo 订单编号
     * @return 操作结果（成功/失败）
     */
    @PostMapping("/refundProductOrder")
    public String refundProductOrder(@RequestParam String orderNo) {
        boolean result = productOrderService.refundProductOrderByOrderNo(orderNo);
        return result ? super.responseSuccess("订单退款成功") : super.responseFail("订单退款失败");
    }

    /**
     * 分页查询商品订单
     * @param pageQuery 分页查询参数，包含查询条件和分页信息
     * @return 商品订单分页数据
     */
    @GetMapping("/productOrderPage")
    public String getProductOrderPage(@RequestBody PageQuery<ZgProductOrder> pageQuery) {
        PageResult<ZgProductOrder> page = productOrderService.getZgProductOrderPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 根据订单编号获取商品订单详情
     * @param orderNo 订单编号
     * @return 商品订单详细信息
     */
    @GetMapping("/productOrderInfo")
    public String getProductOrderInfo(@RequestParam String orderNo) {
        ZgProductOrder order = productOrderService.getProductOrderByOrderNo(orderNo);
        return super.responseSuccess(order, "查询成功");
    }

    /**
     * 生成商品订单提货二维码
     * @param orderNo 订单编号
     * @return 二维码图片的字节数组（PNG格式）
     * @throws WriterException 二维码生成异常
     * @throws IOException IO异常
     */
    @GetMapping(value = "/generatePickupQRCode", produces = MediaType.IMAGE_PNG_VALUE)
    public byte[] generatePickupQRCode(@RequestParam String orderNo) throws WriterException, IOException {
        return productOrderService.generatePickupQRCode(orderNo);
    }

    /**
     * 商品订单提货核销
     * @param orderNo 订单编号
     * @return 操作结果（成功/失败）
     */
    @PostMapping("/verifyPickup")
    public String verifyPickup(@RequestParam String orderNo) {
        boolean result = productOrderService.verifyPickup(orderNo);
        return result ? super.responseSuccess("订单提货核销成功") : super.responseFail("订单提货核销失败");
    }

    /**
     * 创建商品预订订单
     * @param orderNo 订单编号
     * @return 创建的预订订单信息
     */
    @PostMapping("/createProductPreOrder")
    public String createProductPreOrder(@RequestParam String orderNo) {
        ZgProductPreOrder result = productPreOrderService.createProductPreOrder(orderNo);
        return super.responseSuccess(result, "商品预订订单生成成功");
    }
}