<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitBalanceCashApplyMapper">


    <sql id="Base_Column_List">
        id,applyUser,applyNo,balance,bankId,auditStatus,
        reason,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitBalanceCashApply">
        select
        <include refid="Base_Column_List" />
        from profit_balance_cash _apply
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_balance_cash _apply
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitBalanceCashApply" useGeneratedKeys="true">
        insert into profit_balance_cash _apply
        ( id,applyUser,applyNo,balance,bankId,auditStatus,
            reason,createTime,updateTime,isDelete)
        values (#{id},#{applyUser},#{applyNo},#{balance},#{bankId},#{auditStatus},
                   #{reason},#{createTime},#{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitBalanceCashApply" useGeneratedKeys="true">
        insert into profit_balance_cash _apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyUser != null">applyUser,</if>
            <if test="applyNo != null">applyNo,</if>
            <if test="balance != null">balance,</if>
            <if test="bankId != null">bankId,</if>
            <if test="auditStatus != null">auditStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyUser != null">#{applyUser},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="balance != null">#{balance},</if>
            <if test="bankId != null">#{bankId},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitBalanceCashApply">
        update profit_balance_cash _apply
        <set>
            <if test="applyUser != null">
                applyUser = #{applyUser},
            </if>
            <if test="applyNo != null">
                applyNo = #{applyNo},
            </if>
            <if test="balance != null">
                balance = #{balance},
            </if>
            <if test="bankId != null">
                bankId = #{bankId},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitBalanceCashApply">
        update profit_balance_cash _apply
        set
            applyUser =  #{applyUser},
            applyNo =  #{applyNo},
            balance =  #{balance},
            bankId =  #{bankId},
            auditStatus =  #{auditStatus},
            reason =  #{reason},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>
</mapper>