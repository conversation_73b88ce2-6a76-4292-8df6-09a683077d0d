package com.jmt.dao;

import com.jmt.model.zg.ZgCategory;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_category(筷圣直供经营类别)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgCategory
*/
public interface ZgCategoryMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgCategory record);

    int insertSelective(ZgCategory record);

    ZgCategory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgCategory record);

    int updateByPrimaryKey(ZgCategory record);

    List<ZgCategory> selectCategoryTree(Boolean includeDeleted);

    List<ZgCategory> getCategoryPage(ZgCategory queryData);
}
