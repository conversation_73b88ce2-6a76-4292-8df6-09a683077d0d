package com.jmt.model.profit.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class ShareConfig {
    /**
     * 主键
     */
    private Long id;

    /**
     * 分润项目 shareprofit_project 的主键ID
     */
    private Long projectId;

    /**
     * 分润项目名称
     */
    private String projectName;

    /**
     * 运营商编号 jhh_operator_info 的 operatorNo
     */
    private String operatorNo;

    /**
     * 0-比例1-补贴
     */
    private Integer shareType;

    /**
     * 运营商分润比例，当 shareType=0 时填写
     */
    private Double operatorRatio;

    /**
     * 设备收益人分润比例，当 shareType=0 时填写
     */
    private Double investorRatio;

    /**
     * 平台分润比例，当 shareType=0 时填写
     */
    private Double platformRatio;

    /**
     * 商家分润比例，当 shareType=0 时填写
     */
    private Double businessRatio;

    /**
     * 运营商补贴金额，当 shareType=1 时填写
     */
    private BigDecimal operatorAmount;

    /**
     * 设备收益人补贴金额，当 shareType=1 时填写
     */
    private BigDecimal investorAmount;

    /**
     * 平台补贴金额，当 shareType=1 时填写
     */
    private BigDecimal platformAmount;

    /**
     * 商家补贴金额，当 shareType=1 时填写
     */
    private BigDecimal businessAmount;

    /**
     * 0-启用1-禁用
     */
    private Integer useStauts;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否1-是
     */
    private Integer isDelete;
}
