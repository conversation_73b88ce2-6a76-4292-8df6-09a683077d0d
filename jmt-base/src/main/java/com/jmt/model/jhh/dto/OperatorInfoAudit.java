package com.jmt.model.jhh.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class OperatorInfoAudit {
    private Long id;

    private Long auditUser;

    private String operatorNo;

    private Integer auditStatus;

    private String reason;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;
}
