<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.CmUserDao">

	<!-- 根据ID查询 -->
	<select id="selectById" resultType="com.jmt.model.cm.entity.CmUser">
		SELECT id, uaaId, userName, headImg, nickName, sex, telPhone, email,country, province, city, area, address, roleType, operatorNo, refereeNo, isFirstLogin, createTime, updateTime, isDelete FROM cm_user
		WHERE id = #{id} AND isDelete = 0
	</select>

	<select id="selectByUaaId" resultType="com.jmt.model.cm.entity.CmUser">
		SELECT id, uaaId, userName, headImg, nickName, sex, telPhone, email,country, province, city, area, address, roleType, operatorNo, refereeNo, isFirstLogin, createTime, updateTime, isDelete FROM cm_user
		WHERE uaaId = #{uaaId} AND isDelete = 0
	</select>

	<insert id="insert" parameterType="com.jmt.model.cm.entity.CmUser" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO cm_user (
			uaaId, userName, headImg, nickName, sex, telPhone, email,
			country, province, city, area, address, roleType, operatorNo,
			refereeNo, isFirstLogin, createTime, updateTime, isDelete
		) VALUES (
					 #{uaaId}, #{userName}, #{headImg}, #{nickName}, #{sex}, #{telPhone}, #{email},
					 #{country}, #{province}, #{city}, #{area}, #{address}, #{roleType}, #{operatorNo},
					 #{refereeNo}, #{isFirstLogin}, #{createTime}, #{updateTime}, #{isDelete}
				 )
	</insert>

	<update id="updateByUaaId" parameterType="com.jmt.model.cm.entity.CmUser">
		UPDATE cm_user
		<set>
			<if test="userName != null">userName = #{userName},</if>
			<if test="headImg != null">headImg = #{headImg},</if>
			<if test="nickName != null">nickName = #{nickName},</if>
			<if test="sex != null">sex = #{sex},</if>
			<if test="telPhone != null">telPhone = #{telPhone},</if>
			<if test="email != null">email = #{email},</if>
			<if test="country != null">country = #{country},</if>
			<if test="province != null">province = #{province},</if>
			<if test="city != null">city = #{city},</if>
			<if test="area != null">area = #{area},</if>
			<if test="address != null">address = #{address},</if>
			<if test="roleType != null">roleType = #{roleType},</if>
			<if test="operatorNo != null">operatorNo = #{operatorNo},</if>
			<if test="refereeNo != null">refereeNo = #{refereeNo},</if>
			<if test="isFirstLogin != null">isFirstLogin = #{isFirstLogin},</if>
			updateTime = #{updateTime},
		</set>
		WHERE uaaId = #{uaaId}
	</update>

	<update id="updateById" parameterType="com.jmt.model.cm.entity.CmUser">
		UPDATE cm_user
		<set>
			<if test="userName != null">userName = #{userName},</if>
			<if test="headImg != null">headImg = #{headImg},</if>
			<if test="nickName != null">nickName = #{nickName},</if>
			<if test="sex != null">sex = #{sex},</if>
			<if test="telPhone != null">telPhone = #{telPhone},</if>
			<if test="email != null">email = #{email},</if>
			<if test="country != null">country = #{country},</if>
			<if test="province != null">province = #{province},</if>
			<if test="city != null">city = #{city},</if>
			<if test="area != null">area = #{area},</if>
			<if test="address != null">address = #{address},</if>
			<if test="roleType != null">roleType = #{roleType},</if>
			<if test="operatorNo != null">operatorNo = #{operatorNo},</if>
			<if test="refereeNo != null">refereeNo = #{refereeNo},</if>
			<if test="isFirstLogin != null">isFirstLogin = #{isFirstLogin},</if>
			updateTime = #{updateTime},
		</set>
		WHERE id = #{id}
	</update>

	<update id="deleteById" parameterType="java.lang.Long">
		UPDATE cm_user
		SET isDelete = 1,
			updateTime = NOW()
		WHERE id = #{id} AND isDelete = 0
	</update>
</mapper>