package com.jmt.model.profit;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 余额提现申请表
 * @TableName profit_balance_cash _apply
 */
@Data
public class ProfitBalanceCashApply {
    /**
     * 主键
     */
    private Long id;

    /**
     * 申请人，uaa_user的主键uaaId
     */
    private Long applyuser;

    /**
     * 申请编号
     */
    private String applyno;

    /**
     * 提现金额
     */
    private BigDecimal balance;

    /**
     * uaa_user_bankcard的主键ID
     */
    private Long bankid;

    /**
     * 审核状态
0-待审核
1-审核通过
2-审核不通过
3-已提现
     */
    private String auditstatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 更新时间
     */
    private Date updatetime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isdelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProfitBalanceCashApply other = (ProfitBalanceCashApply) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getApplyuser() == null ? other.getApplyuser() == null : this.getApplyuser().equals(other.getApplyuser()))
            && (this.getApplyno() == null ? other.getApplyno() == null : this.getApplyno().equals(other.getApplyno()))
            && (this.getBalance() == null ? other.getBalance() == null : this.getBalance().equals(other.getBalance()))
            && (this.getBankid() == null ? other.getBankid() == null : this.getBankid().equals(other.getBankid()))
            && (this.getAuditstatus() == null ? other.getAuditstatus() == null : this.getAuditstatus().equals(other.getAuditstatus()))
            && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
            && (this.getCreatetime() == null ? other.getCreatetime() == null : this.getCreatetime().equals(other.getCreatetime()))
            && (this.getUpdatetime() == null ? other.getUpdatetime() == null : this.getUpdatetime().equals(other.getUpdatetime()))
            && (this.getIsdelete() == null ? other.getIsdelete() == null : this.getIsdelete().equals(other.getIsdelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getApplyuser() == null) ? 0 : getApplyuser().hashCode());
        result = prime * result + ((getApplyno() == null) ? 0 : getApplyno().hashCode());
        result = prime * result + ((getBalance() == null) ? 0 : getBalance().hashCode());
        result = prime * result + ((getBankid() == null) ? 0 : getBankid().hashCode());
        result = prime * result + ((getAuditstatus() == null) ? 0 : getAuditstatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getCreatetime() == null) ? 0 : getCreatetime().hashCode());
        result = prime * result + ((getUpdatetime() == null) ? 0 : getUpdatetime().hashCode());
        result = prime * result + ((getIsdelete() == null) ? 0 : getIsdelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyuser=").append(applyuser);
        sb.append(", applyno=").append(applyno);
        sb.append(", balance=").append(balance);
        sb.append(", bankid=").append(bankid);
        sb.append(", auditstatus=").append(auditstatus);
        sb.append(", reason=").append(reason);
        sb.append(", createtime=").append(createtime);
        sb.append(", updatetime=").append(updatetime);
        sb.append(", isdelete=").append(isdelete);
        sb.append("]");
        return sb.toString();
    }
}