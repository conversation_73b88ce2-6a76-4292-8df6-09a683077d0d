package com.jmt.model.profit.dto;

import lombok.Data;

import java.util.Date;
@Data
public class ShareProject {
    /**
     * 主键
     */
    private Long id;

    /**
     * shareprofit_project的主键ID
     */
    private Long parentId;

    /**
     * 分润项目名称
     */
    private String projectName;

    /**
     * 订单编号前缀
     */
    private String orderNoPrefix;

    /**
     * 0-启用 1-禁用
     */
    private Integer useStauts;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
