package com.jmt.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.CmBusinessInfoFeignClient;
import com.jmt.client.JhhInvestorFeignClient;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.dao.*;
import com.jmt.enums.ProfitEnum;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.jhh.dto.InvestorInfo;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.profit.*;
import feign.FeignException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 收益分润核心服务类
 * 负责预分润计算、分润处理、积分更新等核心业务逻辑
 */
@Service
public class ProfitService extends BaseService {


    /** Gson实例（单例复用） */
    private static final Gson GSON = new Gson();

    // 自注入，解决内部方法事务不生效问题
    @Resource
    private ProfitService self;


    // ======================== 依赖注入 ========================
    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;
    @Resource
    private CmBusinessInfoFeignClient cmBusinessInfoFeignClient;
    @Resource
    private JhhInvestorFeignClient jhhInvestorFeignClient;

    @Resource
    private ProfitPayRecordMapper payRecordMapper;
    @Resource
    private ProfitShareProjectMapper projectMapper;
    @Resource
    private ProfitShareConfigMapper configMapper;
    @Resource
    private ProfitSharePredivideMapper predivideMapper;
    @Resource
    private ProfitPointsRecordMapper pointsRecordMapper;
    @Resource
    private ProfitSharePostdivideMapper postdivideMapper;




    /**
     * 根据支付记录对象计算预分润并生成记录
     * 供批量处理使用
     */
    @Transactional(rollbackFor = Exception.class)
    public void calculatePreProfitByPayRecord(ProfitPayRecord payRecord) {
        // 1. 校验支付记录状态
        if (payRecord.getPayStatus() != ProfitEnum.PAY_STATUS_SUCCESS.getCode()) {
            throw new RuntimeException("支付未成功，无法预分润: " + payRecord.getSeqNo());
        }

        // 2. 匹配分润项目（订单前缀规则：前3位标识项目）
        String orderNo = payRecord.getOrderNo();
        if (orderNo == null || orderNo.length() < 3) {
            throw new RuntimeException("订单号格式错误（需>=3位）: " + orderNo);
        }
        String orderPrefix = orderNo.substring(0, 3);
        ProfitShareProject project = projectMapper.selectByOrderPrefix(orderPrefix);
        if (project == null) {
            throw new RuntimeException("未找到匹配的分润项目，订单前缀: " + orderPrefix);
        }

        // 3. 获取有效的分润配置
        ProfitShareConfig config = configMapper.selectActiveByProjectId(project.getParentId());
        if (config == null) {
            throw new RuntimeException("分润项目[" + project.getId() + "]无有效配置");
        }

        // 4. 获取关联主体编号（设备收益人、商家）
        String investorNo =  "aaa"; //getInvestorNo();
        String businessNo = getBusinessNo(project.getId());

        // 5. 构建预分润记录
        ProfitSharePredivide predivide = new ProfitSharePredivide();
        predivide.setOrderNo(orderNo);
        predivide.setProjectName(project.getProjectName());
        int totalPoints = payRecord.getPayAmount()
                .multiply(new BigDecimal(ProfitEnum.POINTS_RATE.getCode()))
                .intValue();
        predivide.setProfitTotalPoints(totalPoints);

        // 6. 按分润类型计算各方积分
        predivide.setOperatorNo(config.getOperatorNo());
        predivide.setInvestorNo(investorNo);
        predivide.setBusinessNo(businessNo);
        if (config.getShareType() == ProfitEnum.SHARE_TYPE_RATIO.getCode()) {
            // 比例分润：总积分 * 比例（如30% -> 30）/ 100
            predivide.setOperatorPoints(calculatePoints(totalPoints, config.getOperatorRatio()));
            predivide.setInvestorPoints(calculatePoints(totalPoints, config.getInvestorRatio()));
            predivide.setPlatformPoints(calculatePoints(totalPoints, config.getPlatformRatio()));
            predivide.setBusinessPoints(calculatePoints(totalPoints, config.getBusinessRatio()));
        } else {
            // 补贴分润：直接使用配置的固定金额
            predivide.setOperatorPoints(config.getOperatorAmount().intValue());
            predivide.setInvestorPoints(config.getInvestorAmount().intValue());
            predivide.setPlatformPoints(config.getPlatformAmount().intValue());
            predivide.setBusinessPoints(config.getBusinessAmount().intValue());
        }

        // 7. 设置基础信息并保存
        predivide.setShareStauts(ProfitEnum.PREPROFIT_STATUS_INIT.getCode());
        predivide.setCreateTime(new Date());
        predivide.setUpdateTime(new Date());
        predivide.setIsDelete(0);
        predivideMapper.insert(predivide);
    }


    /**
     * 计算分润积分（总积分 × 比例 ÷ 100，四舍五入取整）
     *
     * @param totalPoints 总积分
     * @param ratio       分润比例（如30表示30%）
     * @return 计算后的积分
     */
    private Integer calculatePoints(int totalPoints, Double ratio) {
        if (ratio == null || ratio <= 0) {
            return 0;
        }
        return new BigDecimal(totalPoints)
                .multiply(new BigDecimal(ratio))
                .divide(new BigDecimal(100), 0, BigDecimal.ROUND_HALF_UP)
                .intValue();
    }


    /**
     * 处理单条预分润记录
     * 流程：校验分润条件 -> 更新各方积分 -> 生成正式分润记录 -> 解绑设备关系 -> 更新预分润状态
     *
     * @param predivide 预分润记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void processPredivide(ProfitSharePredivide predivide) {


        // 1. 为各方（运营商、设备收益人、商家、平台）添加积分记录
        updatePoints(predivide);

        // 2. 生成正式分润记录（关联预分润ID，便于追溯）
        ProfitSharePostdivide postdivide = new ProfitSharePostdivide();
        postdivide.setPreId(predivide.getId());
        postdivide.setOrderNo(predivide.getOrderNo());
        postdivide.setProjectName(predivide.getProjectName());
        postdivide.setProfitTotalPoints(predivide.getProfitTotalPoints());
        postdivide.setOperatorNo(predivide.getOperatorNo());
        postdivide.setInvestorNo(predivide.getInvestorNo());
        postdivide.setBusinessNo(predivide.getBusinessNo());
        postdivide.setOperatorPoints(predivide.getOperatorPoints());
        postdivide.setInvestorPoints(predivide.getInvestorPoints());
        postdivide.setPlatformPoints(predivide.getPlatformPoints());
        postdivide.setBusinessPoints(predivide.getBusinessPoints());
        postdivide.setShareStatus(ProfitEnum.POSTPROFIT_STATUS_INIT.getCode());
        postdivide.setCreateTime(new Date());
        postdivide.setUpdateTime(new Date());
        postdivide.setIsDelete(0);
        postdivideMapper.insert(postdivide);

        // 3. 解绑默认设备收益人与设备的关联（分润后解除绑定关系）
        // unbindInvestorDevice(predivide.getInvestorNo());

        // 4. 更新预分润状态为"已分润"
        predivide.setShareStauts(ProfitEnum.PREPROFIT_STATUS_DONE.getCode());
        predivide.setUpdateTime(new Date());
        predivideMapper.updateByPrimaryKeySelective(predivide);
        logger.info("预分润处理完成，生成正式分润记录 ID: {}", postdivide.getId());
    }







    /**
     * 为各方（运营商、设备收益人、商家、平台）添加积分记录
     *
     * @param predivide 预分润记录
     */
    private void updatePoints(ProfitSharePredivide predivide) {
        // 1. 获取运营商信息并添加积分
        String operatorJson = jhhOperatorFeignClient.getOperatorInfoByOperatorNo(predivide.getOperatorNo());
        OperatorInfo operatorInfo = parseFeignResponse(operatorJson, OperatorInfo.class);
        addPoints(predivide, operatorInfo.getUaaId(), predivide.getOperatorPoints(), "分润");

        // 2. 获取设备收益人信息并添加积分
        String investorJson = jhhInvestorFeignClient.getInfoByInvestorNo(predivide.getInvestorNo());
        InvestorInfo investorInfo = parseFeignResponse(investorJson, InvestorInfo.class);
        addPoints(predivide, investorInfo.getUaaId(), predivide.getInvestorPoints(), "分润");

        // 3. 获取商家信息并添加积分
        String businessJson = cmBusinessInfoFeignClient.getByBusinessNo(predivide.getBusinessNo());
        CmBusinessInfo businessInfo = parseFeignResponse(businessJson, CmBusinessInfo.class);
        addPoints(predivide, businessInfo.getUserId(), predivide.getBusinessPoints(), "分润");

        // 4. 平台积分（uaaId为0标识平台）
        addPoints(predivide, 0L, predivide.getPlatformPoints(), "平台分润");
    }


    /**
     * 解析Feign接口返回的JSON响应
     * 统一处理：提取data字段 -> 转换为目标对象
     *
     * @param json  接口返回的JSON字符串
     * @param clazz 目标对象类型
     * @return 转换后的对象
     */
    private <T> T parseFeignResponse(String json, Class<T> clazz) {
        JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
        if (!jsonObject.has("data")) {
            throw new RuntimeException("接口返回格式错误，缺少data字段: " + json);
        }
        String dataJson = jsonObject.get("data").toString();
        return GSON.fromJson(dataJson, clazz);
    }


    /**
     * 新增积分记录（封装积分添加的通用逻辑）
     *
     * @param predivide 预分润记录（关联来源）
     * @param uaaId     用户ID（0表示平台）
     * @param points    积分数量
     * @param remark    备注信息
     */
    private void addPoints(ProfitSharePredivide predivide, Long uaaId, int points, String remark) {
        try {
            ProfitPointsRecord record = new ProfitPointsRecord();
            record.setUaaId(uaaId);
            record.setPoints(points);
            record.setRecordType(0); // 0-收入（固定业务类型）
            record.setPointSource(0); // 0-分润（固定来源类型）
            record.setSourceId(predivide.getId().toString()); // 关联预分润ID
            record.setRemark(remark);
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            record.setIsDelete(0);
            pointsRecordMapper.insert(record);
        } catch (NumberFormatException e) {
            throw new RuntimeException("uaaId格式错误，无法转换为数字: " + uaaId, e);
        }
    }


    /**
     * 解绑设备收益人与设备的关联关系
     *
     * @param investorNo 设备收益人编号
     */
    private void unbindInvestorDevice(String investorNo) {
        try {
            // 实际场景需调用解绑接口：jhhInvestorFeignClient.unbindDevice(investorNo);
        } catch (FeignException e) {
            throw new RuntimeException("解绑设备收益人失败: " + e.getMessage(), e);
        }
    }


    /**
     * 批量处理所有待分润记录（供定时任务调用）
     * 特性：单条记录处理失败不影响整体事务
     */
    @Transactional(rollbackFor = Exception.class)
    public void processAllPredivides() {
        List<ProfitSharePredivide> predivideList = predivideMapper.selectByShareStatus(ProfitEnum.PREPROFIT_STATUS_INIT.getCode());

        // 逐个处理，捕获单条异常避免整体失败
        for (ProfitSharePredivide predivide : predivideList) {
            try {
                processPredivide(predivide);
            } catch (Exception e) {
                logger.error("处理预分润记录失败，ID: {}", predivide.getId(), e);
            }
        }
    }


    /**
     * 正式分润入账处理（将未入账分润标记为已入账）
     */
    @Transactional(rollbackFor = Exception.class)
    public void processPostdivides() {
        List<ProfitSharePostdivide> postdivideList = postdivideMapper.selectByShareStatus(ProfitEnum.POSTPROFIT_STATUS_INIT.getCode());

        for (ProfitSharePostdivide postdivide : postdivideList) {
            try {
                postdivide.setShareStatus(ProfitEnum.POSTPROFIT_STATUS_DONE.getCode());
                postdivide.setUpdateTime(new Date());
                postdivideMapper.updateByPrimaryKeySelective(postdivide);
                logger.info("正式分润入账完成，ID: {}", postdivide.getId());
            } catch (Exception e) {
                logger.error("处理正式分润失败，ID: {}", postdivide.getId(), e);
            }
        }
    }


    /**
     * 调用Feign接口获取设备收益人编号
     *
     * @param projectId 项目ID
     * @return 设备收益人编号
     */
    private String getInvestorNo(Long projectId) {
        try {
            String json = jhhInvestorFeignClient.getInfo(projectId);
            InvestorInfo investorInfo = parseFeignResponse(json, InvestorInfo.class);
            String investorNo = investorInfo.getInvestorNo();
            if (investorNo == null || investorNo.trim().isEmpty()) {
                throw new RuntimeException("设备收益人编号为空，项目ID: " + projectId);
            }
            return investorNo;
        } catch (FeignException e) {
            throw new RuntimeException("调用设备收益人服务失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("获取设备收益人编号失败: " + e.getMessage(), e);
        }
    }


    /**
     * 调用Feign接口获取商家编码
     *
     * @param projectId 项目ID
     * @return 商家编码
     */
    private String getBusinessNo(Long projectId) {
        try {
            String json = cmBusinessInfoFeignClient.getById(projectId);
            CmBusinessInfo businessInfo = parseFeignResponse(json, CmBusinessInfo.class);
            String businessNo = businessInfo.getBusNo();
            if (businessNo == null || businessNo.trim().isEmpty()) {
                throw new RuntimeException("商家编码为空，项目ID: " + projectId);
            }
            return businessNo;
        } catch (FeignException e) {
            throw new RuntimeException("调用商家服务失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("获取商家编码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量计算预分润：查询未处理的支付记录并批量生成预分润
     * @return 成功处理的条数
     */
    public int calculateBatchPreProfit() {
        // 查询未分润的支付记录（支付成功且未生成预分润）
        List<ProfitPayRecord> unprocessedPayRecords = payRecordMapper.selectUnprocessedPayRecords(
                ProfitEnum.PAY_STATUS_SUCCESS.getCode()
        );
        int successCount = 0;

        // 逐条处理，单条失败不影响整体
        for (ProfitPayRecord payRecord : unprocessedPayRecords) {
            try {
                self.calculatePreProfitByPayRecord(payRecord); // 调用有事务的方法
                successCount++;
            } catch (Exception e) {
                logger.error("批量处理预分润失败，支付流水号: {}", payRecord.getSeqNo(), e);
            }
        }
        return successCount;
    }
}
