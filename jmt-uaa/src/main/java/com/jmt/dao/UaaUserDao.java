package com.jmt.dao;

import com.jmt.model.uaa.UaaUser;
import org.springframework.stereotype.Repository;

@Repository
public interface UaaUserDao {
    Integer insert(UaaUser user);

    Boolean existsByLoginName(String loginName);

    UaaUser existsByTelphone(String telphone);

    Integer updateById(UaaUser uaaUser);

    UaaUser selectByUaaId(Long uaaId);

    Integer updatePassword(UaaUser uaaUser);
}
