<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitBalanceRecordDao">

    <insert id="insert" parameterType="com.jmt.model.profit.entity.ProfitBalanceRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO profit_balance_record (
        uaaId, balance, recordType, balanceSource, sourceId, remark, createTime, updateTime, isDelete
        ) VALUES (
        #{uaaId}, #{balance}, #{recordType}, #{balanceSource}, #{sourceId}, #{remark}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>
</mapper>