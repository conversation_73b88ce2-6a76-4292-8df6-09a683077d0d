package com.jmt.lkl;

import com.jmt.model.profit.dto.AlipayGoodsDetail;
import com.jmt.model.profit.dto.AlipayParams;
import com.jmt.model.profit.dto.LklGoodsDetailDTO;
import com.jmt.model.profit.dto.UnionPayParams;
import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.request.V3LabsTransPreorderRequest;
import com.lkl.laop.sdk.request.model.V3LabsTradeLocationInfo;
import com.lkl.laop.sdk.request.model.V3LabsTradePreorderAlipayBus;
import com.lkl.laop.sdk.request.model.V3LabsTradePreorderUnionPayBus;
import com.lkl.laop.sdk.request.model.V3LabsTradePreorderWechatBus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
@Slf4j
@Service
public class LakalaPaymentService extends LakalaBaseCommon {

    // 初始化配置
    static {
        try {
            doInit();
        } catch (Exception e) {
            log.error("拉卡拉支付初始化失败，请检查配置或证书！", e);
            throw new RuntimeException("支付系统初始化失败", e);
        }
    }

    /**
     * 创建微信支付请求
     *
     * @param merchantNo   商户号
     * @param termNo       终端号
     * @param outTradeNo   商户订单号
     * @param totalAmount  金额(分)
     * @param notifyUrl    回调地址
     * @param remark       备注
     * @param ipAddress    客户端IP
     * @param subAppid     子商户公众号ID 微信分配的子商户公众账号ID
     * @param userId       用户标识 用户在子商户sub_appid下的唯一标识
     * @param goodsTag     订单优惠标记 微信平台配置的商品标记
     * @param sceneInfo    门店信息
     * @param goodsDetails 商品详情
     * @return 支付请求对象
     */
    public static V3LabsTransPreorderRequest createWechatPaymentRequest(
            String merchantNo, String termNo, String outTradeNo,
            String totalAmount, String notifyUrl, String remark,
            String ipAddress, String subAppid, String userId,
            String goodsTag, String sceneInfo, List<LklGoodsDetailDTO> goodsDetails) {

        V3LabsTransPreorderRequest request = new V3LabsTransPreorderRequest();
        // 设置基本参数
        setBasePaymentParams(request, merchantNo, termNo, outTradeNo,
                "WECHAT", "51", totalAmount, notifyUrl, remark);

        // 设置位置信息
        request.setLocationInfo(new V3LabsTradeLocationInfo(ipAddress));

        // 设置微信业务字段
        V3LabsTradePreorderWechatBus wechatBus = new V3LabsTradePreorderWechatBus();
        wechatBus.setSubAppid(subAppid);
        wechatBus.setUserId(userId);
        wechatBus.setGoodsTag(goodsTag);
        wechatBus.setSceneInfo(sceneInfo);

        // 设置商品详情
        if (goodsDetails != null && !goodsDetails.isEmpty()) {
            V3LabsTradePreorderWechatBus.AccBusiDetail accBusiDetail = new V3LabsTradePreorderWechatBus.AccBusiDetail();
            List<V3LabsTradePreorderWechatBus.WechatGoodsDetail> wechatGoodsDetails = new ArrayList<>();

            int totalCost = 0;
            for (LklGoodsDetailDTO detail : goodsDetails) {
                V3LabsTradePreorderWechatBus.WechatGoodsDetail wechatGoodsDetail =
                        new V3LabsTradePreorderWechatBus.WechatGoodsDetail();
                wechatGoodsDetail.setGoodsId(detail.getGoodsId());
                wechatGoodsDetail.setPrice(detail.getPrice());
                wechatGoodsDetail.setQuantity(detail.getQuantity());
                wechatGoodsDetails.add(wechatGoodsDetail);

                totalCost += detail.getPrice() * detail.getQuantity();
            }

            accBusiDetail.setCostPrice(totalCost);
            accBusiDetail.setGoodsDetail(wechatGoodsDetails);
            wechatBus.setDetail(accBusiDetail);
        }

        request.setAccBusiFields(wechatBus);
        return request;
    }

    /**
     * 创建支付宝支付请求
     *
     * @param merchantNo   商户号
     * @param termNo       终端号
     * @param outTradeNo   商户订单号
     * @param totalAmount  金额(分)
     * @param notifyUrl    回调地址
     * @param remark       备注
     * @param locationInfo 位置信息
     * @param alipayParams 支付宝特定参数
     * @return 支付请求对象
     */
    public static V3LabsTransPreorderRequest createAlipayPaymentRequest(
            String merchantNo, String termNo, String outTradeNo,
            String totalAmount, String notifyUrl, String remark,
            V3LabsTradeLocationInfo locationInfo, AlipayParams alipayParams) {

        V3LabsTransPreorderRequest request = new V3LabsTransPreorderRequest();
        // 设置基本参数
        setBasePaymentParams(request, merchantNo, termNo, outTradeNo,
                "ALIPAY", "41", totalAmount, notifyUrl, remark);

        // 设置位置信息
        request.setLocationInfo(locationInfo);

        // 设置支付宝业务字段
        V3LabsTradePreorderAlipayBus alipayBus = new V3LabsTradePreorderAlipayBus();
        alipayBus.setStoreId(alipayParams.getStoreId());
        alipayBus.setUserId(alipayParams.getUserId());
        alipayBus.setDisablePayChannels(alipayParams.getDisablePayChannels());
        alipayBus.setBusinessParams(alipayParams.getBusinessParams());
        alipayBus.setQuitUrl(alipayParams.getQuitUrl());
        alipayBus.setTimeoutExpress(alipayParams.getTimeoutExpress());

        // 设置扩展参数
        if (alipayParams.getExtendParams() != null) {
            V3LabsTradePreorderAlipayBus.AlipayExtendParamInfo extendParamInfo =
                    new V3LabsTradePreorderAlipayBus.AlipayExtendParamInfo();
            extendParamInfo.setHbFqNum(alipayParams.getExtendParams().getHbFqNum());
            extendParamInfo.setSysServiceProviderId(alipayParams.getExtendParams().getSysServiceProviderId());
            extendParamInfo.setFoodOrderType(alipayParams.getExtendParams().getFoodOrderType());
            extendParamInfo.setHbFqSellerPercent(alipayParams.getExtendParams().getHbFqSellerPercent());
            alipayBus.setExtendParams(extendParamInfo);
        }

        // 设置商品详情
        if (alipayParams.getGoodsDetails() != null && !alipayParams.getGoodsDetails().isEmpty()) {
            List<V3LabsTradePreorderAlipayBus.AlipayGoodsDeTail> goodsDeTails = new ArrayList<>();
            for (AlipayGoodsDetail detail : alipayParams.getGoodsDetails()) {
                V3LabsTradePreorderAlipayBus.AlipayGoodsDeTail alipayGoodsDeTail =
                        new V3LabsTradePreorderAlipayBus.AlipayGoodsDeTail();
                alipayGoodsDeTail.setAlipayGoodsId(detail.getAlipayGoodsId());
                alipayGoodsDeTail.setGoodsId(detail.getGoodsId());
                alipayGoodsDeTail.setGoodsName(detail.getGoodsName());
                alipayGoodsDeTail.setQuantity(detail.getQuantity());
                alipayGoodsDeTail.setPrice(detail.getPrice());
                alipayGoodsDeTail.setShowUrl(detail.getShowUrl());
                goodsDeTails.add(alipayGoodsDeTail);
            }
            alipayBus.setGoodsDetail(goodsDeTails);
        }

        request.setAccBusiFields(alipayBus);
        return request;
    }

    /**
     * 创建银联支付请求
     *
     * @param merchantNo     商户号
     * @param termNo         终端号
     * @param outTradeNo     商户订单号
     * @param totalAmount    金额(分)
     * @param notifyUrl      回调地址
     * @param remark         备注
     * @param locationInfo   位置信息
     * @param unionPayParams 银联特定参数
     * @return 支付请求对象
     */
    public static V3LabsTransPreorderRequest createUnionPayPaymentRequest(
            String merchantNo, String termNo, String outTradeNo,
            String totalAmount, String notifyUrl, String remark,
            V3LabsTradeLocationInfo locationInfo, UnionPayParams unionPayParams) {

        V3LabsTransPreorderRequest request = new V3LabsTransPreorderRequest();
        // 设置基本参数
        setBasePaymentParams(request, merchantNo, termNo, outTradeNo,
                "UQRCODEPAY", "51", totalAmount, notifyUrl, remark);

        // 设置位置信息
        request.setLocationInfo(locationInfo);

        // 设置银联业务字段
        V3LabsTradePreorderUnionPayBus unionPayBus = new V3LabsTradePreorderUnionPayBus();
        unionPayBus.setUserId(unionPayParams.getUserId());
        unionPayBus.setFrontUrl(unionPayParams.getFrontUrl());
        unionPayBus.setFrontFailUrl(unionPayParams.getFrontFailUrl());

        request.setAccBusiFields(unionPayBus);
        return request;
    }

    /**
     * 设置支付请求的基本参数
     */
    private static void setBasePaymentParams(V3LabsTransPreorderRequest request,
                                             String merchantNo, String termNo, String outTradeNo,
                                             String accountType, String transType, String totalAmount,
                                             String notifyUrl, String remark) {

        request.setMerchantNo(merchantNo);
        request.setTermNo(termNo);
        request.setOutTradeNo(outTradeNo);
        request.setAccountType(accountType);
        request.setTransType(transType);
        request.setTotalAmount(totalAmount);
        request.setNotifyUrl(notifyUrl);
        request.setRemark(remark);
    }

    /**
     * 执行支付请求
     *
     * @param request 支付请求对象
     * @return 支付响应结果
     * @throws Exception
     */
    public static String executePayment(V3LabsTransPreorderRequest request) throws Exception {
        return LKLSDK.httpPost(request);
    }
}