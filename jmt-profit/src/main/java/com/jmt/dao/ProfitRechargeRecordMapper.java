package com.jmt.dao;

import com.jmt.model.profit.ProfitRechargeRecord;

/**
* <AUTHOR>
* @description 针对表【profit_recharge_record(退款流水表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitRechargeRecord
*/
public interface ProfitRechargeRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitRechargeRecord record);

    int insertSelective(ProfitRechargeRecord record);

    ProfitRechargeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitRechargeRecord record);

    int updateByPrimaryKey(ProfitRechargeRecord record);

}
