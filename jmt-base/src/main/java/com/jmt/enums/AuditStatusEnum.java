package com.jmt.enums;

/**
 * 审核状态枚举
 */
public enum AuditStatusEnum {

    PENDING(0, "待审核"),
    APPROVED(1, "同意"),
    REJECTED(2, "驳回"),
    COMPLETED(3, "完成");

    private final Integer code;
    private final String description;

    AuditStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举，找不到返回null
     */
    public static AuditStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AuditStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }



    /**
     * 判断是否已完成审核流程
     * @return 是否最终状态(同意/驳回/完成)
     */
    public boolean isFinalStatus() {
        return this == APPROVED || this == REJECTED || this == COMPLETED;
    }

    /**
     * 判断是否可以修改
     * @return 是否可以修改(只有待审核状态可以修改)
     */
    public boolean canModify() {
        return this == PENDING;
    }
}