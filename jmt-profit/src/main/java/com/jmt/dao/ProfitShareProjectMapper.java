package com.jmt.dao;

import com.jmt.model.profit.ProfitShareProject;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【profit_share_project(收益分润项目表)】的数据库操作Mapper
* @createDate 2025-07-21 15:09:19
* @Entity com.jmt.model.profit.ProfitShareProject
*/
public interface ProfitShareProjectMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProfitShareProject record);

    int insertSelective(ProfitShareProject record);

    ProfitShareProject selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProfitShareProject record);

    int updateByPrimaryKey(ProfitShareProject record);
    ProfitShareProject selectByOrderPrefix(@Param("orderPrefix") String orderPrefix);
    ProfitShareProject selectByProjectName(@Param("projectName") String projectName);
}
