package com.jmt.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jmt.base.BaseController;
import com.jmt.client.CommonFeignClient;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.model.auth.*;
import com.jmt.service.auth.PhoneAuthService;
import com.jmt.service.auth.SmsCodeAuthService;
import com.jmt.service.auth.UaaAuthService;
import com.jmt.service.auth.WeiXinAuthService;
import com.jmt.util.CaptchaUtil;
import com.jmt.util.LoginUserUtil;
import com.jmt.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一认证登录类
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("/uaa/**")
public class UaaAuthController extends BaseController {

    private final UaaAuthService uaaAuthService;

    private final WeiXinAuthService weiXinAuthService;

    private final PhoneAuthService phoneAuthService;

    private final SmsCodeAuthService smsCodeAuthService;

    private final CommonFeignClient commonFeignClient;

    private final RedisUtil redisUtil;

    /**
     *用户+密码+验证码登录
     */
    @RequestMapping(value = "/psw/login", method = RequestMethod.POST)
    @ResponseBody
    public String pswLogin(@RequestBody @Validated PasswordAuthLoginDto loginDto) {
        String captcha = loginDto.getCaptcha();
        if (StrUtil.isEmpty(captcha)) {
            return super.responseFailAuth("验证码不能为空");
        }
        boolean doesItExist = redisUtil.hasKey(RedisKeyConstant.CAPTCHA_CACHE_KEY + captcha);
        if (!doesItExist) {
            return super.responseFailAuth("验证码错误");
        }
        String token = uaaAuthService.passwordAuthLogin(loginDto);
        if (token == null) {
            return super.responseFailAuth("用户名或密码错误");
        }
        redisUtil.delete(RedisKeyConstant.CAPTCHA_CACHE_KEY + captcha);
        return token;
    }
    /**
     *手机短信验证码登录
     */
    @RequestMapping(value = "/sms/login", method = RequestMethod.POST)
    @ResponseBody
    public String smsLogin(@RequestBody @Validated SmsCodeAuthLoginDto loginDto) {
        String telPhone = loginDto.getTelPhone();
        String smsCode = loginDto.getSmsCode();
        if (StrUtil.isEmpty(smsCode)) {
            return super.responseFailAuth("短信验证码不能为空");
        }
        Object redisSmsCode = redisUtil.get(RedisKeyConstant.SMS_CACHE_KEY + telPhone);
        if (ObjectUtil.isEmpty(redisSmsCode) || !smsCode.equals(Convert.toStr(redisSmsCode))) {
            return super.responseFailAuth("短信验证码错误");
        }
        String token = smsCodeAuthService.smsCodeAuthLogin(loginDto);
        if (token == null) {
            return super.responseFailAuth("短信验证码错误");
        }
        return token;
    }
    /**
     *微信授权登录
     */
    @RequestMapping(value = "/wx/login", method = RequestMethod.POST)
    @ResponseBody
    public String wxLogin(@RequestBody @Validated WeiXinAuthLoginDto loginDto) {
        String clientId = loginDto.getClientId();
        if (StrUtil.isEmpty(clientId)) {
            return super.responseFail("客户端ID不能为空");
        }
        String code = loginDto.getCode();
        if (StrUtil.isEmpty(code)) {
            return super.responseFailAuth("微信临时授权码code不能为空");
        }
        String encryptedData = loginDto.getEncryptedData();
        if (StrUtil.isEmpty(encryptedData)) {
            return super.responseFailAuth("手机号加密数据不能为空");
        }
        String iv = loginDto.getIv();
        if (StrUtil.isEmpty(iv)) {
            return super.responseFailAuth("初始向量不能为空");
        }
        String token = weiXinAuthService.weiXinAuthLogin(loginDto);
        if (token == null) {
            return super.responseFailAuth("用户名或密码错误");
        }
        return token;
    }
    /**
     *app本机号码登录
     */
    @RequestMapping(value = "/phone/login", method = RequestMethod.POST)
    @ResponseBody
    public String phoneLogin(@RequestBody @Validated PhoneAuthLoginDto loginDto) {
        String token = phoneAuthService.phoneAuthLogin(loginDto);
        if (token == null) {
            return super.responseFailAuth("用户名或密码错误");
        }
        return token;
    }

    /**
     * 验证码
     * @return String
     */
    @ResponseBody
    @RequestMapping(value = "/captcha", method = RequestMethod.POST)
    public String captcha() {
        try {
            Map<String, Object> map;
            String code;
            while (true) {
                map = CaptchaUtil.initCode();
                code = Convert.toStr(map.get("code"), "");
                boolean doesItExist = redisUtil.hasKey(RedisKeyConstant.CAPTCHA_CACHE_KEY + code);
                if (!doesItExist) {
                    redisUtil.set(RedisKeyConstant.CAPTCHA_CACHE_KEY + code,code,RedisKeyConstant.CAPTCHA_CACHE_EXPIRE);
                    break;
                }
            }
            // 将图像输出。
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                Map<String, Object> result = new HashMap<>();
                ImageIO.write((BufferedImage) map.get("image"), "jpeg", outputStream);
                byte[] base64Img = Base64Utils.encode(outputStream.toByteArray());
                result.put("image", "data:image/jpeg;base64," + new String(base64Img).replaceAll("\n", ""));
                result.put("captcha", code);
                return super.responseSuccess(result,"验证码生成");
            } catch (Exception e) {
                logger.error("获取验证码异常:", e);
            }
        } catch (Exception e) {
            logger.error("获取验证码异常:", e);
        }
        return super.responseFail("获取验证失败");
    }

    @ResponseBody
    @RequestMapping(value = "/sms",method = RequestMethod.POST)
    public String sendSms(@RequestBody @Validated SmsCodeDto smsCodeDto){
        return commonFeignClient.sendSms(smsCodeDto);
    }

    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    @ResponseBody
    public String logout() {
        LoginUaaUser loginUser = LoginUserUtil.get();
        if (redisUtil.hasKey(RedisKeyConstant.TOKEN_CACHE_KEY+loginUser.getUaaId())) {
            redisUtil.delete(RedisKeyConstant.TOKEN_CACHE_KEY+loginUser.getUaaId());
        }
        return super.responseSuccess("退出成功");
    }
}
