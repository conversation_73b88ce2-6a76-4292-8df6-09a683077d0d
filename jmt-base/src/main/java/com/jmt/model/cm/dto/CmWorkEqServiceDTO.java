package com.jmt.model.cm.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class CmWorkEqServiceDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotNull(message = "ID不能为空")
    private Long id;

    @Size(max = 255, message = "工单编号长度不能超过255")
    private String workNo;

    @Size(max = 255, message = "商家编号长度不能超过255")
    private String busNo;

    @Size(max = 255, message = "商家名称长度不能超过255")
    private String busName;

    @Size(max = 255, message = "联系人长度不能超过255")
    private String linkman;

    @Size(max = 255, message = "联系电话长度不能超过255")
    private String telPhone;

    @Size(max = 255, message = "国家长度不能超过255")
    private String country;

    @Size(max = 255, message = "省份长度不能超过255")
    private String province;

    @Size(max = 255, message = "城市长度不能超过255")
    private String city;

    @Size(max = 255, message = "区县长度不能超过255")
    private String area;

    @Size(max = 255, message = "详细地址长度不能超过255")
    private String address;

    @Size(max = 255, message = "经度长度不能超过255")
    private String longitude;

    @Size(max = 255, message = "纬度长度不能超过255")
    private String latitude;

    @Size(max = 255, message = "运营商编号长度不能超过255")
    private String operatorNo;

    @Size(max = 255, message = "推荐业务编号长度不能超过255")
    private String refereeNo;

    @Size(max = 255, message = "部署业务编号长度不能超过255")
    private String deployerNo;

    private Integer workStatus;

    @Size(max = 255, message = "驳回原因长度不能超过255")
    private String reason;
}
