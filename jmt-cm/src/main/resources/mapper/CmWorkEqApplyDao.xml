<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmWorkEqApplyDao">

    <sql id="Base_Column_List">
        id,workNo, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, eqTotalNum, isProxy, remark, workStatus, reason, createTime,
        updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmWorkEqApply" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_work_eq_apply (
        workNo, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, eqTotalNum, isProxy, remark, workStatus, reason, createTime,
        updateTime, isDelete
        ) VALUES (
        #{workNo,jdbcType=VARCHAR}, #{busNo,jdbcType=VARCHAR}, #{busName,jdbcType=VARCHAR}, #{linkman,jdbcType=VARCHAR},
        #{telPhone,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR},
        #{operatorNo,jdbcType=VARCHAR}, #{refereeNo,jdbcType=VARCHAR}, #{eqTotalNum,jdbcType=INTEGER}, #{isProxy,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR}, #{workStatus,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER}
        )
    </insert>

    <update id="update" parameterType="com.jmt.model.cm.entity.CmWorkEqApply">
        UPDATE cm_work_eq_apply
        <set>
            <if test="workNo != null">workNo = #{workNo,jdbcType=VARCHAR},</if>
            <if test="busNo != null">busNo = #{busNo,jdbcType=VARCHAR},</if>
            <if test="busName != null">busName = #{busName,jdbcType=VARCHAR},</if>
            <if test="linkman != null">linkman = #{linkman,jdbcType=VARCHAR},</if>
            <if test="telPhone != null">telPhone = #{telPhone,jdbcType=VARCHAR},</if>
            <if test="country != null">country = #{country,jdbcType=VARCHAR},</if>
            <if test="province != null">province = #{province,jdbcType=VARCHAR},</if>
            <if test="city != null">city = #{city,jdbcType=VARCHAR},</if>
            <if test="area != null">area = #{area,jdbcType=VARCHAR},</if>
            <if test="address != null">address = #{address,jdbcType=VARCHAR},</if>
            <if test="longitude != null">longitude = #{longitude,jdbcType=VARCHAR},</if>
            <if test="latitude != null">latitude = #{latitude,jdbcType=VARCHAR},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo,jdbcType=VARCHAR},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo,jdbcType=VARCHAR},</if>
            <if test="eqTotalNum != null">eqTotalNum = #{eqTotalNum,jdbcType=INTEGER},</if>
            <if test="isProxy != null">isProxy = #{isProxy,jdbcType=INTEGER},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="workStatus != null">workStatus = #{workStatus,jdbcType=INTEGER},</if>
            <if test="reason != null">reason = #{reason,jdbcType=VARCHAR},</if>
            <if test="createTime != null">createTime = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">updateTime = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">isDelete = #{isDelete,jdbcType=INTEGER},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmWorkEqApply">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_apply
        WHERE id = #{id,jdbcType=BIGINT} and isDelete = 0
    </select>


    <select id="selectAll" resultType="com.jmt.model.cm.entity.CmWorkEqApply">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_apply
        WHERE isDelete = 0
    </select>

    <select id="selectByWorkStatus" parameterType="java.lang.Integer" resultType="com.jmt.model.cm.entity.CmWorkEqApply">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_apply FROM cm_work_eq_apply
        WHERE workStatus = #{workStatus,jdbcType=INTEGER}
        AND isDelete = 0
    </select>



    <update id="updateWorkStatus">
        UPDATE cm_work_eq_apply
        SET workStatus = #{workStatus,jdbcType=INTEGER},
        reason = #{reason,jdbcType=VARCHAR},
        updateTime = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_work_eq_apply
        SET isDelete = 1,
        updateTime = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectByCondition" parameterType="com.jmt.model.cm.entity.CmWorkEqApply" resultType="com.jmt.model.cm.entity.CmWorkEqApply">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_work_eq_apply
        <where>
            isDelete = 0
            <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
            <if test="workNo != null and workNo != ''">AND workNo = #{workNo,jdbcType=VARCHAR}</if>
            <if test="busNo != null and busNo != ''">AND busNo = #{busNo,jdbcType=VARCHAR}</if>
            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName,jdbcType=VARCHAR}, '%')</if>
            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman,jdbcType=VARCHAR}, '%')</if>
            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone,jdbcType=VARCHAR}</if>
            <if test="country != null and country != ''">AND country = #{country,jdbcType=VARCHAR}</if>
            <if test="province != null and province != ''">AND province = #{province,jdbcType=VARCHAR}</if>
            <if test="city != null and city != ''">AND city = #{city,jdbcType=VARCHAR}</if>
            <if test="area != null and area != ''">AND area = #{area,jdbcType=VARCHAR}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address,jdbcType=VARCHAR}, '%')</if>
            <if test="longitude != null and longitude != ''">AND longitude = #{longitude,jdbcType=VARCHAR}</if>
            <if test="latitude != null and latitude != ''">AND latitude = #{latitude,jdbcType=VARCHAR}</if>
            <if test="operatorNo != null and operatorNo != ''">AND operatorNo = #{operatorNo,jdbcType=VARCHAR}</if>
            <if test="refereeNo != null and refereeNo != ''">AND refereeNo = #{refereeNo,jdbcType=VARCHAR}</if>
            <if test="eqTotalNum != null">AND eqTotalNum = #{eqTotalNum,jdbcType=INTEGER}</if>
            <if test="isProxy != null">AND isProxy = #{isProxy,jdbcType=INTEGER}</if>
            <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>
        </where>
        ORDER BY createTime
    </select>
</mapper>