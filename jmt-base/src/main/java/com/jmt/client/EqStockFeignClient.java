package com.jmt.client;
import com.jmt.config.FeignConfig;
import com.jmt.model.eq.dto.RequestSendEq;
import com.jmt.model.eq.dto.RequestTrans;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

@FeignClient(name="jmt-eq",path ="/eq/operatorEq/v1",configuration = FeignConfig.class)
public interface EqStockFeignClient {
    @PostMapping(value = "/transferStock")
    String transferStock(@RequestBody RequestTrans requestTrans);
    @PostMapping(value = "/clearOperatorEq")
    String clearOperatorEq(@RequestBody String operatorNo);
}
