package com.jmt.base;

import cn.hutool.core.map.MapUtil;
import com.jmt.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class BaseService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 无访问权限编码
     */
    private static final String SERVICE_RESPONSE_NO_ACCESS_CODE = "403";
    /**
     * 身份认证失败编码
     */
    private static final String SERVICE_RESPONSE_FAIL_AUTH_CODE = "301";
    /**
     * 身份认证成功编码
     */
    private static final String SERVICE_RESPONSE_SUCCESS_AUTH_CODE = "300";


    /**
     * 无权限访问
     * @return 输出失败的JSON格式数据
     */
    protected String responseAccessDenied() {
        Map<Object, Object> resMap = MapUtil.builder().map();
        resMap.put("code",SERVICE_RESPONSE_NO_ACCESS_CODE);
        resMap.put("msg","无权限访问");
        resMap.put("data","无权限访问");
        return GsonUtil.toJson(resMap);
    }

    /**
     * 认证成功
     *
     * @param token token
     * @return 输出失败的JSON格式数据
     */
    protected String responseSuccessAuth(String token) {
        Map<Object, Object> resMap = MapUtil.builder().map();
        resMap.put("code",SERVICE_RESPONSE_SUCCESS_AUTH_CODE);
        resMap.put("msg","认证成功");
        resMap.put("token",token);
        return GsonUtil.toJson(resMap);
    }

    /**
     * 认证失败
     * @return 输出失败的JSON格式数据
     */
    protected String responseFailAuth() {
        Map<Object, Object> resMap = MapUtil.builder().map();
        resMap.put("code",SERVICE_RESPONSE_FAIL_AUTH_CODE);
        resMap.put("msg","认证失败");
        resMap.put("token","");
        return GsonUtil.toJson(resMap);
    }

    /**
     * 认证失败
     * @return 输出失败的JSON格式数据
     */
    protected String responseFailAuth(String msg) {
        Map<Object, Object> resMap = MapUtil.builder().map();
        resMap.put("code",SERVICE_RESPONSE_FAIL_AUTH_CODE);
        resMap.put("msg",msg);
        resMap.put("token","");
        return GsonUtil.toJson(resMap);
    }
}
