package com.jmt.dao;

import com.jmt.model.profit.dto.ShareProject;
import com.jmt.model.profit.vo.ShareProjectVo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareProjectDao {
    /**
     * 获取项目列表
     * @return
     */
    List<ShareProjectVo> getShareProjectSelect();
    /**
     * 获取项目信息
     * @param projectId
     * @return
     */
    ShareProject getInfo(Long projectId);
    /**
     * 根据获取项目信息
     * @param projectId
     * @return
     */
    ShareProject getInfoByParentId(Long projectId);
}
