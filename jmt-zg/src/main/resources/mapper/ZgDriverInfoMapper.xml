<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgDriverInfoMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgDriverInfo">
        <id property="id" column="id" />
        <result property="userId" column="userId" />
        <result property="driverNo" column="driverNo" />
        <result property="driverName" column="driverName" />
        <result property="idNo" column="idNo" />
        <result property="telPhone" column="telPhone" />
        <result property="country" column="country" />
        <result property="province" column="province" />
        <result property="city" column="city" />
        <result property="address" column="address" />
        <result property="longitude" column="longitude" />
        <result property="latitude" column="latitude" />
        <result property="areaScope" column="areaScope" />
        <result property="carPhoto" column="carPhoto" />
        <result property="carNumber" column="carNumber" />
        <result property="maxLoad" column="maxLoad" />
        <result property="driverStar" column="driverStar" />
        <result property="remark" column="remark" />
        <result property="auditStatus" column="auditStatus" />
        <result property="reason" column="reason" />
        <result property="isRealAuth" column="isRealAuth" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, userId, driverNo, driverName, idNo, telPhone,
        country, province, city, address, longitude,
        latitude, areaScope, carPhoto, carNumber, maxLoad,
        driverStar, remark, auditStatus, reason, isRealAuth,
        createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_driver_info
        where id = #{id}
    </select>

    <select id="selectByUserIdKey" resultType="com.jmt.model.zg.ZgDriverInfo" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from zg_driver_info
        where userId = #{userId}
    </select>

    <select id="getDriverInfoPage" parameterType="com.jmt.model.zg.ZgDriverInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_driver_info t
        <where>
            <!-- 主键ID条件 -->
            <if test="id != null">
                AND t.id = #{id}
            </if>

            <!-- 用户ID条件 -->
            <if test="userId != null">  
                AND t.userId = #{userId}
            </if>

            <!-- 配送员编号条件 -->
            <if test="driverNo != null">  
                AND t.driverNo = #{driverNo}
            </if>

            <!-- 配送员姓名条件 -->
            <if test="driverName != null">  
                AND t.driverName = #{driverName}
            </if>

            <!-- 身份证号条件 -->
            <if test="idNo != null">  
                AND t.idNo = #{idNo}
            </if>

            <!-- 手机号条件 -->
            <if test="telPhone != null">  
                AND t.telPhone = #{telPhone}
            </if>

            <!-- 国家条件 -->
            <if test="country != null">
                AND t.country = #{country}
            </if>

            <!-- 省份条件 -->
            <if test="province != null">
                AND t.province = #{province}
            </if>

            <!-- 城市条件 -->
            <if test="city != null">
                AND t.city = #{city}
            </if>

            <!-- 详细地址条件 -->
            <if test="address != null">
                AND t.address = #{address}
            </if>

            <!-- 经度条件 -->
            <if test="longitude != null">
                AND t.longitude = #{longitude}
            </if>

            <!-- 纬度条件 -->
            <if test="latitude != null">
                AND t.latitude = #{latitude}
            </if>

            <!-- 配送范围条件 -->
            <if test="areaScope != null">  
                AND t.areaScope = #{areaScope}
            </if>

            <!-- 车辆照片条件 -->
            <if test="carPhoto != null">  
                AND t.carPhoto = #{carPhoto}
            </if>

            <!-- 车牌号条件 -->
            <if test="carNumber != null">  
                AND t.carNumber = #{carNumber}
            </if>

            <!-- 车辆最大载重条件 -->
            <if test="maxLoad != null">  
                AND t.maxLoad = #{maxLoad}
            </if>

            <!-- 星级条件 -->
            <if test="driverStar != null">  
                AND t.driverStar = #{driverStar}
            </if>

            <!-- 备注说明条件 -->
            <if test="remark != null">
                AND t.remark = #{remark}
            </if>

            <!-- 审核状态条件 -->
            <if test="auditStatus != null">  
                AND t.auditStatus = #{auditStatus}
            </if>

            <!-- 审核意见条件 -->
            <if test="reason != null">
                AND t.reason = #{reason}
            </if>

            <!-- 是否已实名认证条件 -->
            <if test="isRealAuth != null">  
                AND t.isRealAuth = #{isRealAuth}
            </if>

            <!-- 创建时间范围（大于等于） -->
            <if test="createTime != null">  
                AND t.createTime >= #{createTime}
            </if>

            <!-- 更新时间范围（小于等于） -->
            <if test="updateTime != null">  
                AND t.updateTime &amp;lt #{updateTime}  <!-- 修正转义符，直接使用<（XML中可直接识别） -->
            </if>

            <!-- 逻辑删除状态 -->
            <if test="isDelete != null">  
                AND t.isDelete = #{isDelete}
            </if>
        </where>
        <!-- 按创建时间降序排序 -->
        ORDER BY t.createTime DESC
    </select>

    <select id="getDriverNoAndName" resultType="java.util.Map">
        SELECT driverNo, driverName FROM zg_driver_info WHERE isDelete = 0  
    </select>

    <select id="selectByDriverNo" resultType="com.jmt.model.zg.ZgDriverInfo">
        select
        <include refid="Base_Column_List" />
        from zg_driver_info
        where driverNo = #{driverNo}  <!-- 修正参数名 -->
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_driver_info
        where id = #{id}  <!-- 去除多余空格 -->
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgDriverInfo" useGeneratedKeys="true">
        insert into zg_driver_info
        ( id, userId, driverNo, driverName, idNo, telPhone,
        country, province, city, address, longitude,
        latitude, areaScope, carPhoto, carNumber, maxLoad,
        driverStar, remark, auditStatus, reason, isRealAuth,
        createTime, updateTime, isDelete)
        values (#{id}, #{userId}, #{driverNo}, #{driverName}, #{idNo}, #{telPhone},
        #{country}, #{province}, #{city}, #{address}, #{longitude},
        #{latitude}, #{areaScope}, #{carPhoto}, #{carNumber}, #{maxLoad},
        #{driverStar}, #{remark}, #{auditStatus}, #{reason}, #{isRealAuth},
        #{createTime}, #{updateTime}, #{isDelete})  <!-- 修正所有参数为小驼峰 -->
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgDriverInfo" useGeneratedKeys="true">
        insert into zg_driver_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">userId,</if>  
            <if test="driverNo != null">driverNo,</if>  
            <if test="driverName != null">driverName,</if>  
            <if test="idNo != null">idNo,</if>  
            <if test="telPhone != null">telPhone,</if>  
            <if test="country != null">country,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="address != null">address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="areaScope != null">areaScope,</if>  
            <if test="carPhoto != null">carPhoto,</if>  
            <if test="carNumber != null">carNumber,</if>  
            <if test="maxLoad != null">maxLoad,</if>  
            <if test="driverStar != null">driverStar,</if>  
            <if test="remark != null">remark,</if>
            <if test="auditStatus != null">auditStatus,</if>  
            <if test="reason != null">reason,</if>
            <if test="isRealAuth != null">isRealAuth,</if>  
            <if test="createTime != null">createTime,</if>  
            <if test="updateTime != null">updateTime,</if>  
            <if test="isDelete != null">isDelete,</if>  
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>  
            <if test="driverNo != null">#{driverNo},</if>  
            <if test="driverName != null">#{driverName},</if>  
            <if test="idNo != null">#{idNo},</if>  
            <if test="telPhone != null">#{telPhone},</if>  
            <if test="country != null">#{country},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="address != null">#{address},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="areaScope != null">#{areaScope},</if>  
            <if test="carPhoto != null">#{carPhoto},</if>  
            <if test="carNumber != null">#{carNumber},</if>  
            <if test="maxLoad != null">#{maxLoad},</if>  
            <if test="driverStar != null">#{driverStar},</if>  
            <if test="remark != null">#{remark},</if>
            <if test="auditStatus != null">#{auditStatus},</if>  
            <if test="reason != null">#{reason},</if>
            <if test="isRealAuth != null">#{isRealAuth},</if>  
            <if test="createTime != null">#{createTime},</if>  
            <if test="updateTime != null">#{updateTime},</if>  
            <if test="isDelete != null">#{isDelete},</if>  
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgDriverInfo">
        update zg_driver_info
        <set>
            <if test="userId != null">  
                userId = #{userId},
            </if>
            <if test="driverNo != null">  
                driverNo = #{driverNo},
            </if>
            <if test="driverName != null">  
                driverName = #{driverName},
            </if>
            <if test="idNo != null">  
                idNo = #{idNo},
            </if>
            <if test="telPhone != null">  
                telPhone = #{telPhone},
            </if>
            <if test="country != null">
                country = #{country},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="longitude != null">
                longitude = #{longitude},
            </if>
            <if test="latitude != null">
                latitude = #{latitude},
            </if>
            <if test="areaScope != null">  
                areaScope = #{areaScope},
            </if>
            <if test="carPhoto != null">  
                carPhoto = #{carPhoto},
            </if>
            <if test="carNumber != null">  
                carNumber = #{carNumber},
            </if>
            <if test="maxLoad != null">  
                maxLoad = #{maxLoad},
            </if>
            <if test="driverStar != null">  
                driverStar = #{driverStar},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="auditStatus != null">  
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="isRealAuth != null">  
                isRealAuth = #{isRealAuth},
            </if>
            <if test="createTime != null">  
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">  
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">  
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}  <!-- 去除多余空格 -->
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgDriverInfo">
        update zg_driver_info
        set
        userId = #{userId},  
        driverNo = #{driverNo},  
        driverName = #{driverName},  
        idNo = #{idNo},  
        telPhone = #{telPhone},  
        country = #{country},
        province = #{province},
        city = #{city},
        address = #{address},
        longitude = #{longitude},
        latitude = #{latitude},
        areaScope = #{areaScope},  
        carPhoto = #{carPhoto},  
        carNumber = #{carNumber},  
        maxLoad = #{maxLoad},  
        driverStar = #{driverStar},  
        remark = #{remark},
        auditStatus = #{auditStatus},  
        reason = #{reason},
        isRealAuth = #{isRealAuth},  
        createTime = #{createTime},  
        updateTime = #{updateTime},  
        isDelete = #{isDelete}  
        where id = #{id}  <!-- 去除多余空格 -->
    </update>
</mapper>