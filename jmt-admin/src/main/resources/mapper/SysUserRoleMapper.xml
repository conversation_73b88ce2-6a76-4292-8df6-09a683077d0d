<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysUserRoleDao">
    <resultMap id="BaseResultMap" type="com.jmt.model.admin.user.SysUserRole">
        <id property="id" column="id" />
        <result property="userId" column="userId" />
        <result property="roleId" column="roleId" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>
    <sql id="Base_Column_List">
        id, userId, roleId, createTime, updateTime, isDelete
    </sql>
    <select id="findAllByIdLongs" parameterType="long" resultType="java.lang.Long">
        SELECT roleId FROM sys_user_role WHERE userId = #{userId} AND isDelete = 0
    </select>
    <update id="deleteUserRoleByUserId" parameterType="com.jmt.model.admin.user.SysUserRole">
        UPDATE sys_user_role SET isDelete = #{isDelete}, updateTime = #{updateTime} WHERE userId = #{userId} AND isDelete = 0
    </update>
    <update id="deleteUserRoleByUserIdAndRoleId" parameterType="com.jmt.model.admin.user.SysUserRole">
        UPDATE sys_user_role SET isDelete = #{isDelete}, updateTime = #{updateTime} WHERE userId = #{userId} AND roleId = #{roleId} AND isDelete = 0
    </update>
    <update id="batchUpdateIsDeleteByUserId" parameterType="com.jmt.model.admin.user.SysUserRole">
        UPDATE sys_user_role SET isDelete = #{isDelete}, updateTime = #{updateTime} WHERE userId = #{userId} AND isDelete = 0
    </update>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_user_role (userId, roleId, createTime, updateTime, isDelete) VALUES <foreach collection="list" item="item" separator=",">(#{item.userId}, #{item.roleId}, #{item.createTime}, #{item.updateTime}, #{item.isDelete})</foreach>
    </insert>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> FROM sys_user_role WHERE id = #{id}
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.user.SysUserRole" useGeneratedKeys="true">
        INSERT INTO sys_user_role (userId, roleId, createTime, updateTime, isDelete) VALUES (#{userId}, #{roleId}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.user.SysUserRole" useGeneratedKeys="true">
        INSERT INTO sys_user_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">userId,</if>
            <if test="roleId != null">roleId,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.user.SysUserRole">
        UPDATE sys_user_role
        <set>
            <if test="userId != null">userId = #{userId},</if>
            <if test="roleId != null">roleId = #{roleId},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        WHERE id = #{id}
    </update>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        UPDATE sys_user_role SET isDelete = 1, updateTime = NOW() WHERE id = #{id}
    </update>
</mapper>