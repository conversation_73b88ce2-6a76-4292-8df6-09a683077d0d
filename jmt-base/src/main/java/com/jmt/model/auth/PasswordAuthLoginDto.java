
package com.jmt.model.auth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 账号密码登录信息
 * <AUTHOR>
 */
@Data
public class PasswordAuthLoginDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *客户端ID
     */
    @NotBlank(message="客户端ID不能为空")
    private String clientId;

    /**
     *登录账号
     */
    @NotBlank(message="登录账号不能为空")
    private String loginName;
    /**
     *密码
     */
    @NotBlank(message="登录密码不能为空")
    private String password;
    /**
     *验证码
     */
    @NotBlank(message="登录验证码不能为空")
    private String captcha;

    /**
     * 登录身份
     * 筷圣聚合伙移动端（客户端ID:jmt-jhh）：
     * 0-运营商
     * 1-设备受益人
     * 2-业务员
     * 其他客户端均填0
     */
    @NotNull(message="登录身份不能为空")
    private Integer userType = 0;

}
