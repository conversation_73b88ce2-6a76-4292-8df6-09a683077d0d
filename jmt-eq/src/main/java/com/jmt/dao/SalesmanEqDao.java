package com.jmt.dao;

import com.github.pagehelper.Page;
import com.jmt.model.eq.dto.EqOperatorStockRecordDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalesmanEqDao {

    /**
     * 获取分页数据
     * @param queryData 查询条件
     * @return
     */
    List<EqOperatorStockRecordVo> getPage(EqOperatorStockRecordDto queryData);
    /**
     * 按id获取详情
     * @param id
     * @return
     */
    EqOperatorStockRecordVo getInfo(Integer id);
}
