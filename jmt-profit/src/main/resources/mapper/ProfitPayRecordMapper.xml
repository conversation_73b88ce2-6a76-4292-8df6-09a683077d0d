<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitPayRecordMapper">

    <sql id="Base_Column_List">
        id,seqNo,orderNo,payUser,payType,payAmount,
        payStatus,reason,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.jmt.model.profit.ProfitPayRecord">
        select
        <include refid="Base_Column_List" />
        from profit_pay_record
        where  id = #{id}
    </select>

    <select id="selectBySeqNo" resultType="com.jmt.model.profit.ProfitPayRecord">
        SELECT * FROM profit_pay_record
        WHERE seqNo = #{seqNo}
    </select>
    <select id="selectUnprocessedPayRecords" resultType="com.jmt.model.profit.ProfitPayRecord">
        SELECT
        <include refid="Base_Column_List" />
        FROM
            profit_pay_record p
        WHERE
            p.payStatus = #{payStatus}  -- 支付成功
          AND p.isDelete = 0          -- 未删除
          -- 排除已生成预分润的记录
          AND NOT EXISTS (
            SELECT 1
            FROM profit_share_predivide pre
            WHERE pre.orderNo = p.orderNo
              AND pre.isDelete = 0
        )
        -- 可根据需要添加分页或排序
        ORDER BY p.createTime ASC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from profit_pay_record
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitPayRecord" useGeneratedKeys="true">
        insert into profit_pay_record
        ( id,seqNo,orderNo,payUser,payType,payAmount,
          payStatus,reason,createTime,updateTime,isDelete)
        values (#{id},#{seqNo},#{orderNo},#{payUser},#{payType},#{payAmount},
                #{payStatus},#{reason},#{createTime},#{updateTime},#{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.profit.ProfitPayRecord" useGeneratedKeys="true">
        insert into profit_pay_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="seqNo != null">seqNo,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="payUser != null">payUser,</if>
            <if test="payType != null">payType,</if>
            <if test="payAmount != null">payAmount,</if>
            <if test="payStatus != null">payStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="seqNo != null">#{seqNo},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="payUser != null">#{payUser},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.profit.ProfitPayRecord">
        update profit_pay_record
        <set>
            <if test="seqNo != null">
                seqNo = #{seqNo},
            </if>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="payUser != null">
                payUser = #{payUser},
            </if>
            <if test="payType != null">
                payType = #{payType},
            </if>
            <if test="payAmount != null">
                payAmount = #{payAmount},
            </if>
            <if test="payStatus != null">
                payStatus = #{payStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.profit.ProfitPayRecord">
        update profit_pay_record
        set
            seqNo =  #{seqNo},
            orderNo =  #{orderNo},
            payUser =  #{payUser},
            payType =  #{payType},
            payAmount =  #{payAmount},
            payStatus =  #{payStatus},
            reason =  #{reason},
            createTime =  #{createTime},
            updateTime =  #{updateTime},
            isDelete =  #{isDelete}
        where   id = #{id}
    </update>

    <update id="updateBySeqNoSelective">
        UPDATE profit_pay_record
        <set>
            <if test="preProfitStatus != null">preProfitStatus = #{preProfitStatus},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
        </set>
        WHERE seqNo = #{seqNo}
    </update>
</mapper>
