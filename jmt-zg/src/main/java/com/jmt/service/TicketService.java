package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.*;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.*;
import com.jmt.util.CodeGenerator;
import org.apache.commons.lang.math.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class TicketService extends BaseService {
    private static final Logger logger = LoggerFactory.getLogger(TicketService.class);

    // 订单状态常量
    private static final int ORDER_STATUS_UNPAID = 0;       // 未支付
    private static final int ORDER_STATUS_PAID = 1;         // 已支付
    private static final int ORDER_STATUS_CANCELED = 2;     // 已取消
    private static final int ORDER_STATUS_REFUNDED = 3;     // 已退款

    // 通用状态常量
    private static final int AUDIT_STATUS_PASSED = 1;       // 审核通过
    private static final int IS_DELETE_NO = 0;              // 未删除
    private static final int TICKET_STATUS_USED = 1;        // 囤货券已使用
    private static final int TICKET_STATUS_UNUSED = 0;      // 囤货券未使用

    @Resource
    private ZgTicketInfoMapper zgTicketInfoMapper;
    @Resource
    private ZgTicketCartMapper zgTicketCartMapper;
    @Resource
    private ZgTicketOrderMapper zgTicketOrderMapper;
    @Resource
    private ZgTicketOrderDetailMapper zgTicketOrderDetailMapper;
    @Resource
    private ZgProductOrderMapper zgProductOrderMapper;
    @Resource
    private ZgTicketPackageMapper zgTicketPackageMapper;

    /**
     * 使用囤货券生成商品订单
     * 事务保证：订单生成与囤货券状态更新原子性
     */
    @Transactional(rollbackFor = Exception.class)
    public ZgProductOrder useTicketAndCreateProductOrder(String ticketNo) {
        // 1. 查询囤货券明细获取用户ID
        ZgTicketOrderDetail orderDetail = zgTicketOrderDetailMapper.selectByTicketNo(ticketNo);
        if (orderDetail == null) {
            throw new IllegalArgumentException("囤货券明细不存在：" + ticketNo);
        }
        Long buyUserId = orderDetail.getBuyUserId();

        // 2. 校验核心参数
        if (buyUserId == null || ticketNo == null || ticketNo.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID和囤货券编号不能为空");
        }

        // 3. 查询用户持有的未使用囤货券包
        ZgTicketPackage ticketPackage = zgTicketPackageMapper.selectByUserIdAndTicketNo(buyUserId, ticketNo);
        if (ticketPackage == null) {
            throw new RuntimeException("用户[" + buyUserId + "]未持有未使用的囤货券：" + ticketNo);
        }

        // 4. 校验囤货券基础信息有效性
        String ticketSeqNo = ticketPackage.getTicketSeqNo();
        ZgTicketInfo ticketInfo = zgTicketInfoMapper.selectByTicketNo(ticketPackage.getTicketNo());
        if (ticketInfo == null || ticketInfo.getIsDelete() == 1 || ticketInfo.getAuditStatus() != AUDIT_STATUS_PASSED) {
            throw new RuntimeException("囤货券[" + ticketSeqNo + "]不存在、已删除或未通过审核");
        }

        // 5. 构建商品订单
        ZgProductOrder productOrder = new ZgProductOrder();
        productOrder.setBuyUserId(buyUserId);
        productOrder.setAddressId(0L); // 实际业务中需从请求参数获取
        productOrder.setProductNum(orderDetail.getQuantity());

        // 6. 生成订单编号（包含时间戳+随机数保证唯一性）
        String orderNo = "PRODUCT_" + System.currentTimeMillis() + RandomUtils.nextInt();
        productOrder.setOrderNo(orderNo);

        // 7. 填充订单关联信息
        productOrder.setSupplierNo(ticketPackage.getSupplierNo());
        productOrder.setSupplierName(ticketPackage.getSupplierName());
        productOrder.setTicketSeqNo(ticketSeqNo);
        productOrder.setTicketNo(ticketPackage.getTicketNo());
        productOrder.setOriginalPrice(ticketInfo.getOriginalPrice().intValue());
        productOrder.setCurrentPrice(ticketInfo.getCurrentPrice().intValue());
        productOrder.setPrepayAmount(ticketInfo.getPrepayAmount().intValue());
        productOrder.setPostpayAmount(ticketInfo.getPostpayAmount().intValue());
        productOrder.setProductPicture(ticketInfo.getProductPicture());
        productOrder.setProductName(ticketInfo.getProductName());
        productOrder.setProductSize(ticketInfo.getProductSize());
        productOrder.setEffectiveStartTime(ticketInfo.getEffectiveStartTime());
        productOrder.setEffectiveEndTime(ticketInfo.getEffectiveEndTime());
        productOrder.setTicketName(ticketInfo.getTicketName());

        // 8. 设置订单状态信息
        productOrder.setOrderName(ticketInfo.getProductName() + "x" + orderDetail.getQuantity());
        productOrder.setPayType(4); // 4-囤货券支付
        productOrder.setOrderStatus(ORDER_STATUS_PAID); // 囤货券支付即视为已支付
        productOrder.setIsDelete(IS_DELETE_NO);
        Date now = new Date();
        productOrder.setCreateTime(now);
        productOrder.setUpdateTime(now);

        // 9. 保存商品订单
        int insertResult = zgProductOrderMapper.insert(productOrder);
        if (insertResult <= 0) {
            throw new RuntimeException("生成商品订单失败");
        }

        // 10. 标记囤货券为已使用
        ticketPackage.setTStatus(TICKET_STATUS_USED);
        ticketPackage.setUpdateTime(now);
        int updateCount = zgTicketPackageMapper.updateByPrimaryKeySelective(ticketPackage);
        if (updateCount <= 0) {
            throw new RuntimeException("更新囤货券状态失败");
        }

        return productOrder;
    }

    /**
     * 分页查询囤货券信息
     */
    public PageResult<ZgTicketInfo> getTicketInfoPage(PageQuery<ZgTicketInfo> pageQuery) {
        if (pageQuery == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<ZgTicketInfo> list = zgTicketInfoMapper.getTicketInfoPage(pageQuery.getQueryData());
        return new PageResult<>(list);
    }

    /**
     * 获取用于下拉选择的囤货券信息（未删除的）
     */
    public List<ZgTicketInfo> getTicketSelectBoxData() {
        ZgTicketInfo queryData = new ZgTicketInfo();
        queryData.setIsDelete(IS_DELETE_NO); // 只查询未删除的囤货券
        return zgTicketInfoMapper.getTicketInfoPage(queryData);
    }

    /**
     * 上架囤货券
     */
    public void putTicketOnSale(ZgTicketInfo ticketInfo) {
        if (ticketInfo == null) {
            throw new IllegalArgumentException("囤货券信息不能为空");
        }
        // 初始化上架基础信息
        ticketInfo.setId(null);
        ticketInfo.setReason("");
        ticketInfo.setTicketNo(CodeGenerator.generateTicketNo());
        Date now = new Date();
        ticketInfo.setCreateTime(now);
        ticketInfo.setUpdateTime(now);
        ticketInfo.setIsDelete(IS_DELETE_NO);
        ticketInfo.setAuditStatus(0); // 初始状态：待审核
        zgTicketInfoMapper.insert(ticketInfo);
    }

    /**
     * 获取囤货券详情
     */
    public ZgTicketInfo getTicketInfo(String ticketNo) {
        if (ticketNo == null || ticketNo.trim().isEmpty()) {
            throw new IllegalArgumentException("囤货券编号不能为空");
        }
        return zgTicketInfoMapper.selectByTicketNo(ticketNo);
    }

    /**
     * 审核囤货券
     */
    public void auditTicket(Long id, Integer auditStatus, String reason) {
        if (id == null || auditStatus == null) {
            throw new IllegalArgumentException("ID和审核状态不能为空");
        }
        ZgTicketInfo ticketInfo = zgTicketInfoMapper.selectByPrimaryKey(id);
        if (ticketInfo == null) {
            throw new RuntimeException("囤货券不存在：" + id);
        }
        ticketInfo.setAuditStatus(auditStatus);
        ticketInfo.setReason(reason);
        ticketInfo.setUpdateTime(new Date());
        zgTicketInfoMapper.updateByPrimaryKeySelective(ticketInfo);
    }

    /**
     * 下架囤货券
     */
    public void takeTicketOffSale(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        ZgTicketInfo ticketInfo = zgTicketInfoMapper.selectByPrimaryKey(id);
        if (ticketInfo == null) {
            throw new RuntimeException("囤货券不存在：" + id);
        }
        ticketInfo.setIsDelete(1); // 标记为已删除（下架）
        ticketInfo.setUpdateTime(new Date());
        zgTicketInfoMapper.updateByPrimaryKeySelective(ticketInfo);
    }

    /**
     * 取消囤货券订单（回退库存至购物车）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTicketOrder(Long orderId) {
        ZgTicketOrder order = zgTicketOrderMapper.selectByPrimaryKey(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在：" + orderId);
        }

        // 更新订单状态为已取消
        order.setOrderStatus(ORDER_STATUS_CANCELED);
        order.setUpdateTime(new Date());
        int updateCount = zgTicketOrderMapper.updateByPrimaryKeySelective(order);
        if (updateCount <= 0) {
            throw new RuntimeException("更新订单状态失败");
        }

        // 回退明细至购物车
        List<ZgTicketOrderDetail> orderDetails = zgTicketOrderDetailMapper.selectByOrderNo(order.getOrderNo());
        for (ZgTicketOrderDetail detail : orderDetails) {
            addDetailToCart(detail);
        }

        return true;
    }

    /**
     * 获取囤货券订单详情
     */
    public ZgTicketOrder getTicketOrderInfo(Long orderId) {
        if (orderId == null) {
            throw new IllegalArgumentException("订单ID不能为空");
        }
        return zgTicketOrderMapper.selectByPrimaryKey(orderId);
    }

    /**
     * 囤货券订单退款（回退库存至购物车）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean refundTicketOrder(Long orderId) {
        ZgTicketOrder order = zgTicketOrderMapper.selectByPrimaryKey(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在：" + orderId);
        }

        // 更新订单状态为已退款
        order.setOrderStatus(ORDER_STATUS_REFUNDED);
        order.setUpdateTime(new Date());
        int updateCount = zgTicketOrderMapper.updateByPrimaryKeySelective(order);
        if (updateCount <= 0) {
            throw new RuntimeException("更新订单状态失败");
        }

        // 回退明细至购物车
        List<ZgTicketOrderDetail> orderDetails = zgTicketOrderDetailMapper.selectByOrderNo(order.getOrderNo());
        for (ZgTicketOrderDetail detail : orderDetails) {
            addDetailToCart(detail);
        }

        return true;
    }

    /**
     * 结算囤货券订单（生成主单、明细及囤货券包）
     */
    @Transactional(rollbackFor = Exception.class)
    public TicketOrderResponse settleTicketOrder(SettleTicketOrderRequest request) {
        // 参数校验
        if (request == null || request.getBuyUserId() == null
                || request.getTicketNos() == null || request.getTicketNos().isEmpty()
                || request.getQuantities() == null || request.getQuantities().isEmpty()
                || request.getTicketNos().size() != request.getQuantities().size()) {
            throw new IllegalArgumentException("请求参数错误：用户ID、囤货券编号、数量不能为空，且需一一对应");
        }

        Long buyUserId = request.getBuyUserId();
        List<String> ticketNos = request.getTicketNos();
        List<Integer> quantities = request.getQuantities();
        Integer payType = request.getPayType() != null ? request.getPayType() : 0;
        Date now = new Date();

        // 生成订单编号
        String orderNo = "TICKET_" + System.currentTimeMillis() + RandomUtils.nextInt();

        // 初始化订单主表
        ZgTicketOrder ticketOrder = new ZgTicketOrder();
        ticketOrder.setOrderNo(orderNo);
        ticketOrder.setOrderName("囤货券订单-" + orderNo);
        ticketOrder.setBuyUserId(buyUserId);
        ticketOrder.setPayType(payType);
        ticketOrder.setOrderStatus(ORDER_STATUS_UNPAID); // 未支付
        ticketOrder.setCreateTime(now);
        ticketOrder.setUpdateTime(now);
        ticketOrder.setIsDelete(IS_DELETE_NO);

        BigDecimal totalPayAmount = BigDecimal.ZERO;
        boolean hasValidDetail = false;

        // 处理每个囤货券明细
        for (int i = 0; i < ticketNos.size(); i++) {
            String ticketNo = ticketNos.get(i);
            Integer quantity = quantities.get(i);

            // 跳过无效数据
            if (ticketNo == null || ticketNo.trim().isEmpty() || quantity == null || quantity <= 0) {
                logger.warn("跳过无效囤货券信息：编号={}，数量={}", ticketNo, quantity);
                continue;
            }

            // 校验囤货券有效性
            ZgTicketInfo ticketInfo = zgTicketInfoMapper.selectByTicketNo(ticketNo);
            if (ticketInfo == null || ticketInfo.getIsDelete() == 1 || ticketInfo.getAuditStatus() != AUDIT_STATUS_PASSED) {
                throw new RuntimeException("囤货券[" + ticketNo + "]不存在、已删除或未通过审核");
            }

            // 校验库存
            if (ticketInfo.getStock() < quantity) {
                throw new RuntimeException("囤货券[" + ticketNo + "]库存不足，当前库存：" + ticketInfo.getStock());
            }

            // 校验价格信息
            if (ticketInfo.getCurrentPrice() == null) {
                throw new RuntimeException("囤货券[" + ticketNo + "]价格信息异常");
            }

            // 生成订单明细
            ZgTicketOrderDetail orderDetail = new ZgTicketOrderDetail();
            orderDetail.setOrderNo(orderNo);
            orderDetail.setSupplierNo(ticketInfo.getSupplierNo());
            orderDetail.setTicketNo(ticketNo);
            orderDetail.setTicketName(ticketInfo.getTicketName());
            orderDetail.setBuyUserId(buyUserId);
            orderDetail.setOriginalPrice(ticketInfo.getOriginalPrice());
            orderDetail.setCurrentPrice(ticketInfo.getCurrentPrice());
            orderDetail.setPrepayAmount(ticketInfo.getPrepayAmount());
            orderDetail.setPostpayAmount(ticketInfo.getPostpayAmount());
            orderDetail.setQuantity(quantity);
            orderDetail.setCreateTime(now);
            orderDetail.setUpdateTime(now);
            orderDetail.setIsDelete(IS_DELETE_NO);

            if (zgTicketOrderDetailMapper.insert(orderDetail) <= 0) {
                throw new RuntimeException("生成订单明细失败：" + ticketNo);
            }
            hasValidDetail = true;

            // 累加总金额
            totalPayAmount = totalPayAmount.add(ticketInfo.getCurrentPrice().multiply(BigDecimal.valueOf(quantity)));

            // 扣减库存
            ticketInfo.setStock(ticketInfo.getStock() - quantity);
            ticketInfo.setUpdateTime(now);
            zgTicketInfoMapper.updateByPrimaryKeySelective(ticketInfo);

            // 移除购物车对应记录
            zgTicketCartMapper.updateCartStatus(buyUserId, ticketNo, now);
        }

        // 校验是否有有效明细
        if (!hasValidDetail) {
            throw new RuntimeException("无有效囤货券信息，无法生成订单");
        }

        // 保存订单主表
        ticketOrder.setPayAmount(totalPayAmount);
        if (zgTicketOrderMapper.insert(ticketOrder) <= 0) {
            throw new RuntimeException("生成订单失败");
        }

        // 生成囤货券包
        List<ZgTicketOrderDetail> orderDetails = zgTicketOrderDetailMapper.selectByOrderNo(orderNo);
        for (ZgTicketOrderDetail detail : orderDetails) {
            ZgTicketInfo ticketInfo = zgTicketInfoMapper.selectByTicketNo(detail.getTicketNo());
            if (ticketInfo == null) {
                continue;
            }

            ZgTicketPackage ticketPackage = new ZgTicketPackage();
            ticketPackage.setBuyUserId(buyUserId);
            ticketPackage.setTicketSeqNo("SEQ_" + System.currentTimeMillis() + RandomUtils.nextInt());
            ticketPackage.setSupplierNo(detail.getSupplierNo());
            ticketPackage.setSupplierName(ticketInfo.getSupplierName());
            ticketPackage.setTicketNo(detail.getTicketNo());
            ticketPackage.setTicketName(detail.getTicketName());
            ticketPackage.setOriginalPrice(detail.getOriginalPrice().intValue());
            ticketPackage.setCurrentPrice(detail.getCurrentPrice().intValue());
            ticketPackage.setPrepayAmount(detail.getPrepayAmount().intValue());
            ticketPackage.setPostpayAmount(detail.getPostpayAmount().intValue());
            ticketPackage.setEffectiveStartTime(ticketInfo.getEffectiveStartTime());
            ticketPackage.setEffectiveEndTime(ticketInfo.getEffectiveEndTime());
            ticketPackage.setProductPicture(ticketInfo.getProductPicture());
            ticketPackage.setProductName(ticketInfo.getProductName());
            ticketPackage.setProductSize(ticketInfo.getProductSize());
            ticketPackage.setProductNum(detail.getQuantity());
            ticketPackage.setTStatus(TICKET_STATUS_UNUSED);
            ticketPackage.setCreateTime(now);
            ticketPackage.setUpdateTime(now);
            ticketPackage.setIsDelete(IS_DELETE_NO);
            zgTicketPackageMapper.insert(ticketPackage);
        }

        // 保存订单主表（补全金额）
        ticketOrder.setPayAmount(totalPayAmount);
        if (zgTicketOrderMapper.insert(ticketOrder) <= 0) {
            throw new RuntimeException("生成订单失败");
        }

        // 构建响应
        TicketOrderResponse response = new TicketOrderResponse();
        response.setOrderNo(orderNo);
        response.setOrderId(ticketOrder.getId());
        response.setPayAmount(totalPayAmount);
        response.setOrderStatus(ticketOrder.getOrderStatus());
        response.setMessage("订单生成成功");
        return response;
    }

    /**
     * 公共方法：将订单明细添加到购物车（已存在则更新数量）
     */
    private void addDetailToCart(ZgTicketOrderDetail detail) {
        ZgTicketCart existingCart = zgTicketCartMapper.selectByTicketNoAndBuyUserId(detail.getTicketNo(), detail.getBuyUserId());
        Date now = new Date();

        if (existingCart == null) {
            // 新增购物车记录
            ZgTicketCart cart = new ZgTicketCart();
            cart.setSupplierNo(detail.getSupplierNo());
            cart.setTicketNo(detail.getTicketNo());
            cart.setTicketName(detail.getTicketName());
            cart.setBuyUserId(detail.getBuyUserId());
            cart.setOriginalPrice(detail.getOriginalPrice().intValue());
            cart.setCurrentPrice(detail.getCurrentPrice().intValue());
            cart.setPrepayAmount(detail.getPrepayAmount().intValue());
            cart.setPostpayAmount(detail.getPostpayAmount().intValue());
            cart.setQuantity(detail.getQuantity());
            cart.setCreateTime(now);
            cart.setUpdateTime(now);
            cart.setIsDelete(IS_DELETE_NO);
            zgTicketCartMapper.insert(cart);
        } else {
            // 更新已有购物车数量
            existingCart.setQuantity(existingCart.getQuantity() + detail.getQuantity());
            existingCart.setUpdateTime(now);
            zgTicketCartMapper.updateByPrimaryKeySelective(existingCart);
        }
    }
}