package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.CmBusinessInfoDao;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmBusinessInfoService extends BaseService {
    @Resource
    private CmBusinessInfoDao cmBusinessInfoDao;
    @Transactional
    public int createBusinessInfo(CmBusinessInfo businessInfo) {
        businessInfo.setCreateTime(new Date());
        businessInfo.setUpdateTime(new Date());
        businessInfo.setIsDelete(0);
        businessInfo.setAuditStatus(0); // 默认待审核状态
        return cmBusinessInfoDao.insert(businessInfo);
    }


    @Transactional
    public int updateBusinessInfo(CmBusinessInfo businessInfo) {
        if (businessInfo.getId() == null) {
            throw new IllegalArgumentException("商家ID不能为空");
        }
        businessInfo.setUpdateTime(new Date());
        return cmBusinessInfoDao.updateById(businessInfo);
    }


    @Transactional
    public int deleteBusinessInfo(Long id) {
        CmBusinessInfo businessInfo = cmBusinessInfoDao.selectById(id);
        if (businessInfo == null) {
            throw new RuntimeException("用户不存在");
        }
        return cmBusinessInfoDao.delete(id);
    }


    public CmBusinessInfo getBusinessInfoById(Long id) {
        return cmBusinessInfoDao.selectById(id);
    }

    public CmBusinessInfo selectByUserId(Long userId) {
        return cmBusinessInfoDao.selectByUserId(userId);
    }


    /**
     * 分页查询商家信息
     */
    public PageResult<CmBusinessInfo> getPage(PageQuery<CmBusinessInfo> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<CmBusinessInfo> list = cmBusinessInfoDao.selectByCondition(pageQuery.getQueryData());
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }


    /**
     * 审核商家
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason) {
        CmBusinessInfo info = cmBusinessInfoDao.selectById(id);
        if (info == null) {
            throw new RuntimeException("商家不存在");
        }
        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (info.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的商家");
        }
        CmBusinessInfo businessInfo = new CmBusinessInfo();
        businessInfo.setId(id);
        businessInfo.setAuditStatus(auditStatus);
        businessInfo.setReason(reason);
        businessInfo.setUpdateTime(new Date());
        return cmBusinessInfoDao.updateById(businessInfo);
    }

    public CmBusinessInfo getBusinessInfoByBusinessNo(String businessNo) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoDao.selectByBusinessNo(businessNo);
        return cmBusinessInfo;
    }
}
