package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.DemoDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.DemoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/cm/demo/v1*")
public class DemoController extends BaseController {

    @Resource
    private DemoService demoService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<DemoDto> pageQuery) {
        PageResult<DemoDto> page = demoService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Integer id) {
        DemoDto demoDto = demoService.getInfo(id);
        return super.responseSuccess(demoDto,"查询成功");
    }

    /**
     * 新增
     * @param demoDto 新增参数
     * @return ResBody
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody DemoDto demoDto) {
        return super.responseSuccess("新增成功");
    }

}
