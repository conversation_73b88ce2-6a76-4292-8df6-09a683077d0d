package com.jmt.dao;


import com.jmt.model.admin.user.SysUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface SysUserDao {

    List<SysUser> getPage(SysUser sysUser);

    SysUser getInfo(Long userId);
    SysUser getInfoByUaaId(Long uaaId);

    Integer deleteByPrimaryKey(SysUser sysUser);

    Long insert(SysUser record);

    Integer insertSelective(SysUser record);

    SysUser selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysUser record);

    int updateByPrimaryKey(SysUser record);
    List<SysUser> selectComboList(@Param("keyword") String keyword);

    int batchUpdateIsDelete(@Param("userIds") List<Long> userIds, @Param("isDelete") Integer isDelete, @Param("updateTime") Date updateTime);
}
