package com.jmt.model.profit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProfitPayRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    private String seqNo;

    private String orderNo;

    private Long payUser;

    private Integer payType;

    private BigDecimal payAmount;

    private Integer payStatus;

    private String reason;
}
