package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.ShareProjectDao;
import com.jmt.model.profit.vo.ShareProjectVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class ShareProjectService extends BaseService {
    @Resource
    private ShareProjectDao shareProjectDao;
    /**
     * 分润项目下拉选择框数据接口
     * @param
     * @return
     */
    public List<ShareProjectVo> getShareProjectSelect() {
        return shareProjectDao.getShareProjectSelect();
    }


}
