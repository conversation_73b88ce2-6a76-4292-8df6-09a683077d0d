package com.jmt.dao;

import com.jmt.model.zg.ZgSupplierInfo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【zg_supplier_info(筷圣直供供应商信息表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgSupplierInfo
*/
public interface ZgSupplierInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgSupplierInfo record);

    int insertSelective(ZgSupplierInfo record);

    ZgSupplierInfo selectByPrimaryKey(Long id);
    ZgSupplierInfo selectByUserIdKey(Long id);
    int updateByPrimaryKeySelective(ZgSupplierInfo record);

    int updateByPrimaryKey(ZgSupplierInfo record);

    List<ZgSupplierInfo> getSupplierInfoPage(ZgSupplierInfo queryData);
    List<Map<String, String>> getSupplierNoAndName();

    ZgSupplierInfo selectBySupplierNo(String supplierNo);
}
