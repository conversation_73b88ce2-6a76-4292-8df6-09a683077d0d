<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgSupplierInfoMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgSupplierInfo">
        <id property="id" column="id" />
        <result property="userId" column="userId" />
        <result property="supplierNo" column="supplierNo" />
        <result property="supplierName" column="supplierName" />
        <result property="linkman" column="linkman" />
        <result property="telPhone" column="telPhone" />
        <result property="country" column="country" />
        <result property="province" column="province" />
        <result property="city" column="city" />
        <result property="address" column="address" />
        <result property="longitude" column="longitude" />
        <result property="latitude" column="latitude" />
        <result property="supplierPhoto" column="supplierPhoto" />
        <result property="supplierLicense" column="supplierLicense" />
        <result property="categoryId" column="categoryId" />
        <result property="supplierStar" column="supplierStar" />
        <result property="remark" column="remark" />
        <result property="auditStatus" column="auditStatus" />
        <result property="reason" column="reason" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, userId, supplierNo, supplierName, linkman, telPhone,
        country, province, city, address, longitude,
        latitude, supplierPhoto, supplierLicense, categoryId, supplierStar,
        remark, auditStatus, reason, createTime, updateTime,
        isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info
        where id = #{id}
    </select>

    <select id="selectByUserIdKey" resultType="com.jmt.model.zg.ZgSupplierInfo" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info
        where userId = #{id}
    </select>

    <!-- 新增：供应商分页查询方法 -->
    <select id="getSupplierInfoPage" parameterType="com.jmt.model.zg.ZgSupplierInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_supplier_info t
        <where>
            <!-- 主键ID条件 -->
            <if test="id != null">
                AND t.id = #{id}
            </if>

            <!-- 用户ID条件 -->
            <if test="userId != null">
                AND t.userId = #{userId}
            </if>

            <!-- 供应商编号条件 -->
            <if test="supplierNo != null and supplierNo != ''">
                AND t.supplierNo = #{supplierNo}
            </if>

            <!-- 供应商名称（支持模糊查询） -->
            <if test="supplierName != null and supplierName != ''">
                AND t.supplierName LIKE CONCAT('%', #{supplierName}, '%')
            </if>

            <!-- 联系人条件 -->
            <if test="linkman != null and linkman != ''">
                AND t.linkman = #{linkman}
            </if>

            <!-- 联系电话条件 -->
            <if test="telPhone != null and telPhone != ''">
                AND t.telPhone = #{telPhone}
            </if>

            <!-- 国家条件 -->
            <if test="country != null and country != ''">
                AND t.country = #{country}
            </if>

            <!-- 省份条件 -->
            <if test="province != null and province != ''">
                AND t.province = #{province}
            </if>

            <!-- 城市条件 -->
            <if test="city != null and city != ''">
                AND t.city = #{city}
            </if>

            <!-- 经营类别ID条件 -->
            <if test="categoryId != null">
                AND t.categoryId = #{categoryId}
            </if>

            <!-- 星级条件 -->
            <if test="supplierStar != null">
                AND t.supplierStar = #{supplierStar}
            </if>

            <!-- 审核状态条件 -->
            <if test="auditStatus != null">
                AND t.auditStatus = #{auditStatus}
            </if>

            <!-- 创建时间范围（大于等于） -->
            <if test="createTime != null">
                AND t.createTime >= #{createTime}
            </if>

            <!-- 更新时间范围（小于等于） -->
            <if test="updateTime != null">
                AND t.updateTime &amp;lt; #{updateTime}
            </if>

            <!-- 逻辑删除状态 -->
            <if test="isDelete != null">
                AND t.isDelete = #{isDelete}
            </if>
        </where>
        <!-- 按创建时间降序排序 -->
        ORDER BY t.createTime DESC
    </select>

    <select id="getSupplierNoAndName" resultType="java.util.Map">
        SELECT supplierNo, supplierName FROM zg_supplier_info WHERE isDelete = 0
    </select>
    <select id="selectBySupplierNo" resultType="com.jmt.model.zg.ZgSupplierInfo" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info
        where supplierNo = #{supplierNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_supplier_info
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgSupplierInfo" useGeneratedKeys="true">
        insert into zg_supplier_info
        (id, userId, supplierNo, supplierName, linkman, telPhone,
         country, province, city, address, longitude,
         latitude, supplierPhoto, supplierLicense, categoryId, supplierStar,
         remark, auditStatus, reason, createTime, updateTime,
         isDelete)
        values (#{id}, #{userId}, #{supplierNo}, #{supplierName}, #{linkman}, #{telPhone},
                #{country}, #{province}, #{city}, #{address}, #{longitude},
                #{latitude}, #{supplierPhoto}, #{supplierLicense}, #{categoryId}, #{supplierStar},
                #{remark}, #{auditStatus}, #{reason}, #{createTime}, #{updateTime},
                #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgSupplierInfo" useGeneratedKeys="true">
        insert into zg_supplier_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">userId,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="supplierName != null">supplierName,</if>
            <if test="linkman != null">linkman,</if>
            <if test="telPhone != null">telPhone,</if>
            <if test="country != null">country,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="address != null">address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="supplierPhoto != null">supplierPhoto,</if>
            <if test="supplierLicense != null">supplierLicense,</if>
            <if test="categoryId != null">categoryId,</if>
            <if test="supplierStar != null">supplierStar,</if>
            <if test="remark != null">remark,</if>
            <if test="auditStatus != null">auditStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="telPhone != null">#{telPhone},</if>
            <if test="country != null">#{country},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="address != null">#{address},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="supplierPhoto != null">#{supplierPhoto},</if>
            <if test="supplierLicense != null">#{supplierLicense},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="supplierStar != null">#{supplierStar},</if>
            <if test="remark != null">#{remark},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgSupplierInfo">
        update zg_supplier_info
        <set>
            <if test="userId != null">
                userId = #{userId},
            </if>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="supplierName != null">
                supplierName = #{supplierName},
            </if>
            <if test="linkman != null">
                linkman = #{linkman},
            </if>
            <if test="telPhone != null">
                telPhone = #{telPhone},
            </if>
            <if test="country != null">
                country = #{country},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="longitude != null">
                longitude = #{longitude},
            </if>
            <if test="latitude != null">
                latitude = #{latitude},
            </if>
            <if test="supplierPhoto != null">
                supplierPhoto = #{supplierPhoto},
            </if>
            <if test="supplierLicense != null">
                supplierLicense = #{supplierLicense},
            </if>
            <if test="categoryId != null">
                categoryId = #{categoryId},
            </if>
            <if test="supplierStar != null">
                supplierStar = #{supplierStar},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgSupplierInfo">
        update zg_supplier_info
        set
            userId = #{userId},
            supplierNo = #{supplierNo},
            supplierName = #{supplierName},
            linkman = #{linkman},
            telPhone = #{telPhone},
            country = #{country},
            province = #{province},
            city = #{city},
            address = #{address},
            longitude = #{longitude},
            latitude = #{latitude},
            supplierPhoto = #{supplierPhoto},
            supplierLicense = #{supplierLicense},
            categoryId = #{categoryId},
            supplierStar = #{supplierStar},
            remark = #{remark},
            auditStatus = #{auditStatus},
            reason = #{reason},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>