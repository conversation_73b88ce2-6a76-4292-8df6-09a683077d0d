package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmFarmingFoodInfoConvertor;
import com.jmt.dao.*;
import com.jmt.model.cm.dto.CmFarmingFoodInfoDTO;
import com.jmt.model.cm.entity.CmFarmingFoodAudit;
import com.jmt.model.cm.entity.CmFarmingFoodInfo;
import com.jmt.model.cm.entity.CmFarmingFoodOrder;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmFarmingFoodInfoVO;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmFarmingFoodInfoService extends BaseService {
    @Resource
    private CmFarmingFoodInfoDao cmFarmingFoodInfoDao;

    @Resource
    private CmFarmingFoodAuditDao cmFarmingFoodAuditDao;

    @Resource
    private CmFarmingFoodOrderDao cmFarmingFoodOrderDao;

    @Resource
    private CmFarmingFoodInfoConvertor cmFarmingFoodInfoConvertor;

    @Transactional
    public Long create(CmFarmingFoodInfoDTO dto) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoConvertor.toEntity(dto);

        cmFarmingFoodInfo.setAuditStatus(0);
        cmFarmingFoodInfo.setCreateTime(new Date());
        cmFarmingFoodInfo.setUpdateTime(new Date());
        cmFarmingFoodInfo.setIsDelete(0);
        Long result = cmFarmingFoodInfoDao.insert(cmFarmingFoodInfo);
        Long generatedId = cmFarmingFoodInfo.getId();

        CmFarmingFoodAudit audit = new CmFarmingFoodAudit();
        audit.setInfoId(generatedId);
        audit.setAuditStatus(0);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setIsDelete(0);
        cmFarmingFoodAuditDao.insert(audit);
        return result;
    }

    @Transactional
    public int update(CmFarmingFoodInfoDTO dto) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoDao.selectById(dto.getId());
        if (cmFarmingFoodInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        if (cmFarmingFoodInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmFarmingFoodInfo cmWorkEq = cmFarmingFoodInfoConvertor.toEntity(dto);
        cmWorkEq.setUpdateTime(new Date());
        return cmFarmingFoodInfoDao.update(cmWorkEq);
    }


    @Transactional
    public int delete(Long id) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoDao.selectById(id);
        if (cmFarmingFoodInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmFarmingFoodInfoDao.delete(id);
    }

    @Transactional
    public CmFarmingFoodInfoVO getById(Long id) {
        return cmFarmingFoodInfoConvertor.toVo(cmFarmingFoodInfoDao.selectById(id));
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmFarmingFoodInfoDTO> getPage(PageQuery<CmFarmingFoodInfoDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        CmFarmingFoodInfoDTO cmFarmingFoodInfoDTO = pageQuery.getQueryData();
        List<CmFarmingFoodInfoDTO> list = cmFarmingFoodInfoDao.selectByCondition(cmFarmingFoodInfoDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason, Long auditUserId) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoDao.selectById(id);
        if (cmFarmingFoodInfo == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmFarmingFoodInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单审核状态
        CmFarmingFoodInfo update = new CmFarmingFoodInfo();
        update.setId(id);
        update.setAuditStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());
        int updateCount = cmFarmingFoodInfoDao.update(update);

        // 查询审核记录
        CmFarmingFoodAudit existingAudit = cmFarmingFoodAuditDao.selectByInfoId(id);

        // 更新审核表
        CmFarmingFoodAudit audit = new CmFarmingFoodAudit();
        audit.setInfoId(id);
        audit.setAuditStatus(auditStatus);
        audit.setReason(reason);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setAuditUser(auditUserId);
        audit.setIsDelete(0);
        audit.setId(existingAudit.getId());
        cmFarmingFoodAuditDao.update(audit);

        return updateCount;
    }

    /**
     * 创建订单
     * @param order
     */
    @Transactional
    public void createOrder(CmFarmingFoodOrder order) {
        order.setOrderStatus(0);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = WorkNoGeneratorUtil.generate("FNO");
        order.setOrderNo(orderNo);
        cmFarmingFoodOrderDao.insert(order);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional
    public void cancelOrder(Long id) {
        CmFarmingFoodOrder order = cmFarmingFoodOrderDao.selectById(id);
        if (order == null || order.getIsDelete() == 1) {
            throw new RuntimeException("订单不存在或已被删除");
        }
        order.setOrderStatus(3); //取消支付
        order.setUpdateTime(new Date());
        cmFarmingFoodOrderDao.update(order);
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditOrder(Long id, Integer auditStatus, String reason) {
        CmFarmingFoodOrder order = cmFarmingFoodOrderDao.selectById(id);
        if (order == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单支付状态
        CmFarmingFoodOrder update = new CmFarmingFoodOrder();
        update.setId(id);
        update.setOrderStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmFarmingFoodOrderDao.update(update);
    }
}
