<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.ShareConfigDao">
	<insert id="add">
		INSERT INTO profit_share_config
		    (projectId,projectName,operatorNo,shareType,operatorRatio,investorRatio,platformRatio,businessRatio,operatorAmount,investorAmount,platformAmount,businessAmount,useStauts,createTime,updateTime,isDelete)
		VALUES
		    (#{projectId},#{projectName},#{operatorNo},#{shareType},#{operatorRatio},#{investorRatio},#{platformRatio},#{businessRatio},#{operatorAmount},#{investorAmount},#{platformAmount},#{businessAmount},#{useStauts},#{createTime},#{updateTime},#{isDelete})
	</insert>
	<update id="delete">
		UPDATE profit_share_config
		SET
		    isDelete = 1
		WHERE
		    id = #{id}
	</update>
	<update id="deleteByNo">
		UPDATE profit_share_config
		SET
			isDelete = 1
		WHERE
			operatorNo = #{operatorNo}
	</update>
	<select id="getPage" resultType="com.jmt.model.profit.dto.ShareConfig">
		SELECT id,projectId,projectName,operatorNo,shareType,operatorRatio,investorRatio,platformRatio,businessRatio,operatorAmount,investorAmount,platformAmount,businessAmount,useStauts,createTime,updateTime,isDelete
		FROM profit_share_config
		<where>
			<if test="queryData.projectId != null">
				and projectId = #{queryData.projectId}
			</if>
			<if test="queryData.shareType != null">
				and shareType = #{queryData.shareType}
			</if>
			<if test="queryData.operatorNo != null">
				and operatorNo = #{queryData.operatorNo}
			</if>
			<if test="true">
				AND isDelete = 0
			</if>
		</where>
	</select>
	<select id="getInfo" resultType="com.jmt.model.profit.dto.ShareConfigDto">
		SELECT id,projectId,projectName,operatorNo,shareType,operatorRatio,investorRatio,platformRatio,businessRatio,operatorAmount,investorAmount,platformAmount,businessAmount,useStauts,createTime,updateTime,isDelete
		FROM profit_share_config
		WHERE
			operatorNo = #{operatorNo}
		AND isDelete = 0
	</select>
</mapper>