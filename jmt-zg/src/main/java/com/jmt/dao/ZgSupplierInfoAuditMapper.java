package com.jmt.dao;

import com.jmt.model.zg.ZgSupplierInfoAudit;

/**
* <AUTHOR>
* @description 针对表【zg_supplier_info_audit(筷圣直供供应商信息审核记录表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgSupplierInfoAudit
*/
public interface ZgSupplierInfoAuditMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgSupplierInfoAudit record);

    int insertSelective(ZgSupplierInfoAudit record);

    ZgSupplierInfoAudit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgSupplierInfoAudit record);

    int updateByPrimaryKey(ZgSupplierInfoAudit record);

    ZgSupplierInfoAudit selectByAuditSupplierNo(String supplierNo);
}
